/**
 * 将表单接口标准的表单数据结构生成GTForm填报数据
 * BackendFormData -> FrontFormData
 */

 const SUB_FORM_DATA_KEY = "_mis_sub_items"

 const toFrontFormData = function( data ) {
   let obj = {}
   Object.keys( data ).forEach( key => {
     if ( key == SUB_FORM_DATA_KEY ) {
       data[SUB_FORM_DATA_KEY].forEach( k => {
         obj[k.form] = {
           form: k.form, data: k.data.map( item => {
             return toFrontFormData( item )
           } )
         }
       } )
     } else {
       obj[key] = data[key]
     }
   } )
   return obj
 }

 const toApiFormData = function( formData ) {
   if ( !formData ) return
   let apiData = {}
   let subForms = []
   Object.keys( formData ).forEach( ( field ) => {
     const value = formData[field]
     if ( isSubForm( value ) ) {
       // 对表单数据进行递归处理
       value.data = value.data.map( ( v ) => {
         return toApiFormData( v )
       } )
       subForms.push( value )
     } else {
       apiData[field] = value
     }
   } )
   if ( subForms.length > 0 ) {
     apiData[SUB_FORM_DATA_KEY] = subForms
   }
   return apiData
 }

 function isSubForm ( obj ) {
   return (
     obj &&
     typeof obj === "object" &&
     !Array.isArray( obj ) &&
     obj.form &&
     obj.data &&
     Array.isArray( obj.data )
   )
 }

 export default{
   toFrontFormData,
   toApiFormData
 }
