
import omitDeep from 'omit-deep-lodash'
import md5 from 'crypto-js/md5'

const DEL_FIELDS = ['uid', 'res_uid']

export default class ChangeDetector {

  constructor( source ) {
	  this.setSource(source)
  }

  setSource(source){
	  if(!source || (typeof source !== 'object')) return
	  const cloneSource = omitDeep(source, DEL_FIELDS)
	  const str = JSON.stringify(cloneSource)
	  this.sourceFootprint = md5(str).toString()
  }

  detect (target) {
    if(!target || (typeof target !== 'object')) return
    const cloneTarget = omitDeep(target, DEL_FIELDS)
		const str = JSON.stringify(cloneTarget)
    const footprint = md5(str).toString()
		return this.sourceFootprint !== footprint
  }
}
