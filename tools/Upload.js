import Base64 from "crypto-js/enc-base64";
import HmacSHA1 from "crypto-js/hmac-sha1";
import Utf8 from "crypto-js/enc-utf8";
import moment from "moment";

import FileAPI from "@/apis/file";
import UIDUtil from "@/utils/uid";

const VALID_MINUTES = 15;
const CONTENT_LENGTH_MAX = 1024 * 1024 * 1024;

const genSignature = (stsConfig) => {
  const date = new Date();
  date.setMinutes(date.getMinutes() + VALID_MINUTES);
  const policyText = JSON.stringify({
    expiration: date.toISOString(),
    conditions: [["content-length-range", 0, CONTENT_LENGTH_MAX]],
  });
  const policy = Base64.stringify(Utf8.parse(policyText));
  const signature = Base64.stringify(
    HmacSHA1(policy, stsConfig.accessKeySecret)
  );
  return {
    policy,
    signature,
  };
};

const genFileName = (fileSuffix, filePathPrefix, dir = "") => {
  if (!fileSuffix) return;
  const uploadDate = moment(new Date()).format("YYYYMMDD");
  const fileName = UIDUtil.genUID();
  if (!filePathPrefix) return `${dir}/${uploadDate}/${fileName}.${fileSuffix}`;
  return `${dir}/${filePathPrefix}/${uploadDate}/${fileName}.${fileSuffix}`;
};

const getFileSuffix = (fileName) => {
  const index = fileName.lastIndexOf(".");
  return index === -1 ? "notype" : fileName.slice(index + 1);
};

const upload = async (file, filePathPrefix) => {
  if (!file) return;
  return new Promise(async (resolve, reject) => {
    try {
      const fileSuffix = getFileSuffix(file.url || file.name);
      const stsConfig = await FileAPI.getTempToken();
      const fileName = genFileName(fileSuffix, filePathPrefix, stsConfig.dir);
      const { policy, signature } = genSignature(stsConfig);
      // 上传
      const formData = {
        key: fileName,
        policy,
        signature,
        OSSAccessKeyId: stsConfig.accessKeyId,
        success_action_status: "200",
        "x-oss-security-token": stsConfig.securityToken,
      };
      uni.uploadFile({
        url: stsConfig.endPoint,
        formData,
        filePath: file.url,
        fileType: file.type,
        name: "file",
        success(res) {
          if (res.statusCode === 200) {
            resolve(fileName);
          } else {
            reject(res);
          }
        },
        fail(err) {
          reject(err);
        },
      });
    } catch (e) {
      reject(e);
    }
  });
};

export default {
  upload,
};
