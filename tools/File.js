
export default {

	/**
	 * 移动文件并重命名
	 * @param {Object} filePath         文件路径
	 * @param {Object} newFileName      新文件名
	 * @param {Object} targetPath       目标路径
	 */
	async moveTo(filePath, newFileName, targetPath){
		if(!filePath || !newFileName || !targetPath) return
		try{
			const targetEntry = await this._getDirectory(targetPath)
			const fileEntry = await this._getFile(filePath)
			const suffixIndex = fileEntry.name.indexOf('.')
			const suffix = fileEntry.name.substr(suffixIndex)
			const targetFileName = `${newFileName}${suffix}`
			return new Promise((resolve, reject) => {
				fileEntry.moveTo(targetEntry, targetFileName,
					function(fileEntry){
						resolve(fileEntry.fullPath)
					},
					function(e){
						reject(e)
					})
			})
		}
		catch(err){
			console.log('移动文件出错：', err)
		}
	},
	
	/**
	 * 复制文件并重命名
	 * @param {Object} filePath         文件路径
	 * @param {Object} newFileName      新文件名
	 * @param {Object} targetPath       目标路径
	 */
	async copyTo(filePath, newFileName, targetPath){
		if(!filePath || !newFileName || !targetPath) return
		try{
			const targetEntry = await this._getDirectory(targetPath)
			const fileEntry = await this._getFile(filePath)
			const suffixIndex = fileEntry.name.indexOf('.')
			const suffix = fileEntry.name.substr(suffixIndex)
			const targetFileName = `${newFileName}${suffix}`
			return new Promise((resolve, reject) => {
				fileEntry.copyTo(targetEntry, targetFileName,
					function(fileEntry){
						resolve(fileEntry.fullPath)
					},
					function(e){
						reject(e)
					})
			})
		}
		catch(err){
			console.log('拷贝文件出错：', err)
		}
	},
	

	_getDirectory(filePath) {
		if(!filePath) return
		return new Promise((resolve, reject) => {
			plus.io.requestFileSystem(plus.io.PRIVATE_DOC,
				function(fs) {
					fs.root.getDirectory(filePath, {
						create: true
					},
					function(dir){
						resolve(dir)
					},
					function(err){
						reject(err)
					})
				},
				function(err){
					reject(err)
				})
		})
	},

	_getFile(filePath) {
		if(!filePath) return
		return new Promise((resolve, reject) => {
			plus.io.resolveLocalFileSystemURL(filePath,
				function(entry) {
					resolve(entry)
				},
				function(e){
					reject(e)
				})
		})
	}
}