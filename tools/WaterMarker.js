// 水印宽度占图片宽度比例
const MAX_WIDTH_FACTOR = 0.6
// 水印文字与图片宽度比例
const FONT_SIZE_FACTOR = 0.025

// 打水印的图片控制大小
const MAX_SIZE = 800 * 800
// CANVAS导出图片时的放大倍数，确保压缩后水印的清晰度
const SCALE_OUT_FACTOR = 1.2

export default {

	/**
	 * 打水印
	 * @param {Object} file            图片文件
	 * @param {Object} watermarks      水印内容
	 * @param {Object} context         打水印的组件（CANVAS的父组件）
	 * @param {Object} canvasId        CANVAS ID
	 */
	watermark(file, watermarks, context, canvasId) {
		if (!file || !watermarks) return file
		return new Promise((resolve, reject) => {
			uni.getImageInfo({
				src: file.url,
				success: (res) => {
					// 获取图片尺寸，计算缩小系数
					const scaleFactor = this.getScaleFactor(res.width, res.height)
					const canvasWidth = Math.floor(res.width * scaleFactor)
					const canvasHeight = Math.floor(res.height * scaleFactor)
					// 创建画布
					context.canvasWidth = canvasWidth + 'px'
					context.canvasHeight = canvasHeight + 'px'
					// 避免canvas未完成初始化导致图片不全
					context.$nextTick(() => {
						let ctx = uni.createCanvasContext(canvasId, context)
						// 绘制图片
						ctx.drawImage(file.url, 0, 0, canvasWidth, canvasHeight)
						// 对长文本进行断行
						const items = this.genWatermarks(watermarks, ctx, canvasWidth)
						// 绘制水印
						this.draw(items, ctx, canvasWidth, canvasHeight)
						// 导出图片到临时目录
						ctx.draw(false, () => {
							setTimeout(() => {
								uni.canvasToTempFilePath({
									canvasId: canvasId,
									destWidth: Math.floor(canvasWidth * SCALE_OUT_FACTOR),
									destHeight: Math.floor(canvasHeight * SCALE_OUT_FACTOR),
									success: (temp) => {
										// 文件移动到指定目录并重命名
										resolve(temp.tempFilePath)
									},
									fail: (err) => {
										reject(err)
									}
								}, context)
							}, 100)
						})
					})
				}
			})
		})
	},

	// 绘制一行文字
	drawText(ctx, item, index, lines, canvasWidth, canvasHeight){
		if(!ctx || !item || !lines || !canvasHeight) return
		const fontSize = canvasWidth * FONT_SIZE_FACTOR
		const paddingEdge = fontSize
		const paddingLine = fontSize / 2
		// 多行的情况，确保文本对齐
		const x = paddingEdge + item.padding
		const y = canvasHeight - ( paddingEdge + ( lines - index - 1 ) * (fontSize + paddingLine)) - paddingLine / 2
		ctx.setFontSize(fontSize)
		ctx.setFillStyle('#ffffff')
		ctx.fillText(item.text, x, y)
	},

	// 打断过长文本，注意字符宽度不一样
	breakLine(text, width, maxWidth, padding, ctx){
		if(!text || !width || !maxWidth) return
		if(width <= maxWidth) return [ text ]
		let lines = []
		let line = ''
		for(let c of text){
			const width = ctx.measureText(line + c).width + (lines.length === 0 ? 0 : padding)
			if(width > maxWidth){
				lines.push(line)
				line = c
			}
			else{
				line += c
			}
		}
		lines.push(line)
		return lines
	},

	// 生成水印内容，对长文本进行打断处理
	genWatermarks(watermarks, ctx, canvasWidth){
		if(!watermarks || !ctx || !canvasWidth) return
		const maxWidth = canvasWidth * MAX_WIDTH_FACTOR
		let items = []
		const fontSize = canvasWidth * FONT_SIZE_FACTOR
		ctx.setFontSize(fontSize)
		watermarks.forEach((item, index) => {
			const text = `${item.label}：${item.value}`
			let metrics = ctx.measureText(text)
			let width = metrics.width
			metrics = ctx.measureText(`${item.label}：`)
			// 计算labelWidth，用户绘制多行文本时对齐
			let labelWidth = metrics.width
			const lines = this.breakLine(text, width, maxWidth, labelWidth, ctx)
			lines.forEach((line, index) => {
				items.push({
					text: line,
					padding: index === 0 ? 0 : labelWidth
				})
			})
		})
		return items
	},

	// 绘制水印
	draw(items, ctx, canvasWidth, canvasHeight){
		if(!items || !ctx) return
		// 绘制背景
		const fontSize = canvasWidth * FONT_SIZE_FACTOR
		const paddingEdge = fontSize
		const paddingLine = fontSize / 2
		const paddingBg = paddingEdge / 2
		const width = canvasWidth * MAX_WIDTH_FACTOR + (paddingEdge - paddingBg) * 2
		const height = (fontSize + paddingLine) * items.length + paddingBg * 1.5
		const x = paddingBg
		const y = canvasHeight - paddingBg - height
		ctx.fillStyle = '#000000'
		ctx.globalAlpha = 0.6
		ctx.fillRect(x, y, width, height)
		ctx.globalAlpha = 1
		items.forEach((item, index) => this.drawText(ctx, item, index, items.length, canvasWidth, canvasHeight))
	},

	// 计算图片缩放比例
	getScaleFactor(width, height){
		if(!width || !height) return 1
		const size = width * height
		return size <= MAX_SIZE ? 1 : Math.sqrt(MAX_SIZE / size)
	},



}
