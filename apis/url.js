export const LOGIN_URL = {
  LOGIN: "oauth/token",
  LOGOUT: "oauth/logout",
};

export const USER_URL = {
  EDITPSD: "user/modifyPassword",
  GETUSERINFO: "user/info",
  NEWGETUSERINFO: "custom/user/info",
};

/**
 * 表单定义操作
 */
export const FORM_DESIGN_URL = {
  GET: (formUid) => `formDesign/${formUid}`,
};

/**
 * 表单数据操作
 */
export const FORM_DATA_URL = {
  ADD: (formUid) => `form/${formUid}`,
  LIST: (formUid) => `form/${formUid}/search`,
  GET: (formUid, formRecordUid) =>
    `form/${formUid}/${formRecordUid}?resolveSubItems=true`,
  UPDATE: (formUid) => `form/${formUid}`,
  DELETE: (formUid, formRecordUid) => `form/${formUid}/${formRecordUid}`,
  TABLE: () => `resource/current/table`,
  AGGREGATION:(formUid)=> `form/${formUid}/aggregation`,
  UPDATE_STATE: (formUid, dataId) => `custom/form/${ formUid }/${ dataId }/state`, // 定制接口: 状态更新
  JOIN_SEARCH: (formName) => `form/${formName}/joinSearch`,
}

/**
 * 获取上传sts token
 */
export const STS_URL = {
  GET: "sts/getTempToken",
};
