import request from "./ajax";
import { LOGIN_URL, USER_URL } from "./url";
import CryptoJ<PERSON> from "crypto-js";

export default {
  // 登录
  async login(username, password) {
    return request.post(LOGIN_URL.LOGIN, {
      username,
      password: this.getMD5PassWord(password),
      grant_type: "password",
    });
  },

  async refreshToken() {
    let data = await request.post(LOGIN_URL.LOGIN, {
      grant_type: "refresh_token",
      refresh_token: uni.getStorageSync("refresh_token"),
    });

    uni.setStorageSync("refresh_token", data.refresh_token);
    uni.setStorageSync("access_token", data.access_token);

    return data;
  },

  // 修改个人密码
  async changePassword(id, oldPassword, newPassword, confirmPassword) {
    return await request.post(USER_URL.EDITPSD, {
      oldPassword: this.getMD5PassWord(oldPassword),
      newPassword: this.getMD5PassWord(newPassword),
      confirmPassword: this.getMD5PassWord(confirmPassword),
    });
  },

  // 获取当前用户信息(定制)
  async getCustomUserInfo() {
    return await request.get(USER_URL.GETCUSTOMUSERINFO);
  },

  async getUserInfo() {
    return await request.get(USER_URL.GETUSERINFO);
  },

  async getNewUserInfo() {
    return await request.get(USER_URL.NEWGETUSERINFO);
  },

  getMD5PassWord(password) {
    return CryptoJS.MD5(password).toString();
  },
};
