export default {
	
	/**
	 * 打开数据库
	 * @param {Object} dbName
	 * @param {Object} path
	 */
	async openDB(dbName, path){
		const isOpen = this.isOpenDB(dbName, path)
		if(isOpen){
			return true
		}
		return new Promise((resolve, reject) => {
			plus.sqlite.openDatabase({
				name: dbName,
				path,
				//
				success: function(e){
					resolve(true)
				},
				//
				fail: function(e){
					reject(`打开数据库${dbName}失败：${JSON.stringify(e)}`)
				}
			})
		})
	},
	
	/** 
	 * 是否已经打开数据库
	 * @param {Object} dbName
	 * @param {Object} path
	 */
	isOpenDB(dbName, path){
		return plus.sqlite.isOpenDatabase({
			name: dbName,
			path
		})
	},
	
	/** 查询数据表是否存在
	 * @param {Object} dbName
	 * @param {Object} tableName
	 */
	async existsOrCreateTable(dbName, tableName, tableSql){
		if(!dbName || !tableName || !tableSql) return
		const sql = `SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}' LIMIT 1`
		let result = await this.selectSQL(dbName, sql)
		if(result && result.length > 0) return true
		await this.executeSQL(dbName, tableSql)
		return true
	},
	
	/**
	 * 执行增、删、改操作
	 * @param {Object} dbName
	 * @param {Object} sql
	 */
	async executeSQL(dbName, sql){
		return new Promise((resolve, reject) => {
			plus.sqlite.executeSql({
				name: dbName,
				sql,
				//
				success: function(e){
					resolve(e)
				},
				//
				fail: function(e){
					reject(`执行SQL'${sql}'失败：${JSON.stringify(e)}`)
				}
			})
		})
	},
	
	/**
	 * 执行查询操作
	 * @param {Object} dbName
	 * @param {Object} sql
	 */
	async selectSQL(dbName, sql){
		return new Promise((resolve, reject) => {
			plus.sqlite.selectSql({
				name: dbName,
				sql,
				//
				success: function(e){
					resolve(e)
				},
				//
				fail: function(e){
					reject(`执行SQL'${sql}'失败：${JSON.stringify(e)}`)
				}
			})
		})
	},
	
	/**
	 * 关闭数据库
	 */
	async closeDB(dbName){
		return new Promise((resolve, reject) => {
			plus.sqlite.closeDatabase({
				name: dbName,
				//
				success: function(e){
					resolve(true)
				},
				//
				fail: function(e){
					reject(`关闭数据库${dbName}失败：${JSON.stringify(e)}`)
				}
			})
		})
	}
	
}