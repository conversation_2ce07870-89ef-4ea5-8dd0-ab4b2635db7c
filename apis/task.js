import formData from "./formData";
//import env from "@/env";

const TASK_FORM_UID = "task_form_table";
const OUT_FIELDS = [
  "task_type",
  "task_name",
  "task_index",
  "task_form",
  "create_time",
  "type_index",
];

export default {
  getThumbUrl(formName) {
    return `${env.API_URL}formDesign/${formName}/thumb`;
  },

  async getTaskList() {
    let result = await formData.getFormRecords(TASK_FORM_UID, {
      outFields: OUT_FIELDS,
    });
    if (!result || !result.list) return;
    const list = result.list;
    if (list.length === 0) return [];
    // 数据处理
    const taskGroups = list.reduce((groups, item) => {
      const task = {
        name: item.task_name,
        groupName: item.task_type,
        groupIndex: item.type_index,
        index: item.task_index,
        form: item.task_form,
        thumb: this.getThumbUrl(item.task_form),
        createTime: item.create_time,
      };
      console.log("task", task);
      groups[item.task_type]
        ? groups[item.task_type].push(task)
        : (groups[item.task_type] = [task]);
      return groups;
    }, {});
    // 转成数组
    return Object.values(taskGroups)
      .map((group) => {
        const { groupName, groupIndex } = group[0];
        const obj = {
          groupName,
          groupIndex,
          items: group.sort((a, b) => {
            return a.index - b.index;
          }),
        };
        return obj;
      })
      .sort((a, b) => {
        return a.groupIndex - b.groupIndex;
      });
  },
};
