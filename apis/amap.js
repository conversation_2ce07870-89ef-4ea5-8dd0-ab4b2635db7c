
import axios from 'axios'
import env from '@/env'

const URL = env.AMAP_GEOCODE_API_URL

const AMAP = axios.create()

const ZHIXIA_CITIES = ['110000', '120000', '310000', '500000']
const ZHIXIA_CITY_NAME = '市辖区'

export default {

	// 获取位置信息
	async getLocation(lon, lat){
		const url = `${URL}&location=${lon},${lat}`
		try{
			const result = await AMAP.get(url)
			if(!result) return
			const address = result.data?.regeocode?.formatted_address
			let { province, city, district, township, towncode } = result.data?.regeocode?.addressComponent
			let provinceCode, cityCode, districtCode, townCode
			if(towncode){
				provinceCode = `${towncode.substr(0, 2)}0000`
				cityCode = `${towncode.substr(0, 4)}00`
				districtCode = towncode.substr(0, 6)
				townCode = towncode.substr(0, 9)
			}
			if(ZHIXIA_CITIES.indexOf(provinceCode) >= 0){
				city = ZHIXIA_CITY_NAME
				cityCode = `${provinceCode.substr(0, 3)}100`
			}
			return {
				// 地址
				address,
				// 省
				provinceName: province,
				provinceCode,
				// 市
				cityName: city,
				cityCode,
				// 区县
				districtName: district,
				districtCode,
				// 乡镇
				townName: township,
				townCode
			}
		}
		catch(err){
			console.log('获取地址错误', err)
		}
	}

}