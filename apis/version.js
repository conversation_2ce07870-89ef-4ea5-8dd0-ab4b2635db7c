import request from './ajax'
import { FORM_DATA_URL } from './url'
const VERSION_TABLE = '应用版本表'
export default {
  /**
   * 查询表单的数据列表
   * @param {*} formId 表单的uid
   * @returns
   */
  async checkVersion () {
    let res = await request.post( FORM_DATA_URL.LIST( VERSION_TABLE ))
    const versionData = res.list[0]
    const { 版本 } = versionData
    const upgrade = plus.runtime.version !== 版本

    return {
      upgrade,
      版本,
      url: versionData['地址']
    }
  }
}
