import SQLITE from './sqlite'
import { FORM_DESIGN_URL } from "@/apis/url"
import xHttp from "./ajax"

// 基础数据库（只读）信息
const DB_NAME = 'base'
const DB_PATH = '_www/static/sqlite/base.db'
// 表单定义数据表名称
const FORM_DEF_TABLE = 'form'

export default {

	/**
	 * 获取表单定义
	 * @param formId 表单ID
	 */
	async getFormDef(formId){
		if(!formId) return
		try{
			await SQLITE.openDB(DB_NAME, DB_PATH)
			const sql = `SELECT def FROM ${FORM_DEF_TABLE} WHERE id='${formId}'`
			const result = await SQLITE.selectSQL(DB_NAME, sql)
			return result && result[0] && result[0].def && JSON.parse(result[0].def)
		}
		catch(e){
			console.log('获取表单定义失败:', e)
		}
	},

	/**
	 * @param {Object} tableName 表名称
	 * @param {Object} filter    过滤条件
	 */
	async queryTable(tableName, filter){
		if(!tableName) return
		try{
			let sql = `SELECT * FROM ${tableName} WHERE 1=1`
			await SQLITE.openDB(DB_NAME, DB_PATH)
			if(filter && Array.isArray(filter)){
				filter.forEach(items => {
					if(items && Array.isArray(items) && items.length === 3){
						sql += ` AND "${items[1]}"='${items[2]}'`
					}
				})
			}
			return await SQLITE.selectSQL(DB_NAME, sql)
		}
		catch(e){
			console.log(`获取字典${tableName}数据失败：`, e)
		}
	},

	async closeDB(){
		try{
			await SQLITE.closeDB(DB_NAME)
		}
		catch(e){
			console.log(`关闭数据库${DB_NAME}错误：${e}`)
		}
	}

}
