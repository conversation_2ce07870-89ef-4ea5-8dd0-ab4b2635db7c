import Vue from "vue";
import App from "./App";

import uView from "uview-ui";
import utils from "@/utils";
import events from "@/events";
import constants from "@/constants";
import apis from "@/apis";
import component from "@/import";

// 注册组件  =======================================================

// 表单组件
import CascaderField from "@/components/form/field/CascaderField";
import CheckboxField from "@/components/form/field/CheckboxField";
import DateField from "@/components/form/field/DateField";
import DateTimeField from "@/components/form/field/DateTimeField";
import IntegerField from "@/components/form/field/IntegerField";
import LonLatField from "@/components/form/field/LonLatField";
import NumberField from "@/components/form/field/NumberField";
import NumberWithUnitField from "@/components/form/field/NumberWithUnitField";
import PictureField from "@/components/form/field/PictureField";
import RadioField from "@/components/form/field/RadioField";
import SelectField from "@/components/form/field/SelectField";
import SubForm from "@/components/form/field/SubForm";
import TextField from "@/components/form/field/TextField";
import GTForm from "@/components/form/GTForm";

// 布局组件
import Grid from "@/components/form/layout/Grid";
import SplitLine from "@/components/form/layout/SplitLine";
import Title from "@/components/form/layout/Title";

// 注册组件  =======================================================

import TiledSubForm from "@/components/form/field/TiledSubForm";
import YearSelectField from "@/components/form/field/YearSelectField";
import PeerSelectField from "@/components/form/field/PeerSelectField";
import TextAreaField from "@/components/form/field/TextAreaField";

// 子系统定制组件
import plowlandCropInputField from "@/pages_plowland/components/field/CropInputField";
import plowlandPictureField from "@/pages_plowland/components/field/PictureField";
import plowlandTextField from "@/pages_plowland/components/field/TextField";
import plowlandTextAreaField from "@/pages_plowland/components/field/TextAreaField";

Vue.use(uView);
Vue.use(utils);
Vue.use(events);
Vue.use(constants);
Vue.use(apis);
Vue.use(component);

App.mpType = "app";

Vue.component("CascaderField", CascaderField);
Vue.component("CheckboxField", CheckboxField);
Vue.component("DateField", DateField);
Vue.component("DateTimeField", DateTimeField);
Vue.component("IntegerField", IntegerField);
Vue.component("LonLatField", LonLatField);
Vue.component("NumberField", NumberField);
Vue.component("NumberWithUnitField", NumberWithUnitField);
Vue.component("PictureField", PictureField);
Vue.component("RadioField", RadioField);
Vue.component("SelectField", SelectField);
Vue.component("SubForm", SubForm);
Vue.component("TextField", TextField);

Vue.component("GTForm", GTForm);

Vue.component("Grid", Grid);
Vue.component("SplitLine", SplitLine);
Vue.component("Title", Title);

Vue.component("TiledSubForm", TiledSubForm);
Vue.component("YearSelectField", YearSelectField);
Vue.component("PeerSelectField", PeerSelectField);
Vue.component("TextAreaField", TextAreaField);

// 注册子系统组件  =======================================================
Vue.component("plowland-CropInputField", plowlandCropInputField);
Vue.component("plowland-PictureField", plowlandPictureField);
Vue.component("plowland-TextField", plowlandTextField);
Vue.component("plowland-TextAreaField", plowlandTextAreaField);

// 注册扩展组件  ====================================================

const app = new Vue({
  ...App,
});

app.$mount();
