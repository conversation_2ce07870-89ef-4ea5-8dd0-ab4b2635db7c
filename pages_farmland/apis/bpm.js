import ajax from "@/apis/ajax";

export default {
  /**
   * 提交单据
   * @param {*} proccode 流程编码大
   * @param {*} formId 表单id大
   * @param {*} dataId 数据id
   * @param {*} params 请求体附加-些流程参数{"arg1":1，"arg2":"tom"}
   * @returns
   */
  async auditMisSubmit(procCode, formId, dataId, params) {
    return await ajax.post(`audit/mis/submit/${procCode}/${formId}/${dataId}`, params);
  },

  async viewProcess(formId, dataId) {
    return await ajax.get(`audit/mis/process/${formId}/${dataId}`);
  },
};
