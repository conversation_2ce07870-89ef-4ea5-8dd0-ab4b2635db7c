<template>
  <view class="page-container">
    <u-navbar
      title="数据填报"
      :placeholder="true"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      :safe-area-inset-top="true"
      @leftClick="navBackClick"
    ></u-navbar>

    <view class="form-content">
      <GTForm v-if="options" :formUid="formUid" :options="options" ref="gtForm" @initialized="gtFormInit" @submit="submitHandler"></GTForm>
    </view>
    <view class="btn-list" v-if="!readonly">
      <view class="btn-item" v-if="useCache">
        <u-button type="info" @click="saveLocalHandler" :custom-style="buttonStyle">暂存</u-button>
      </view>
      <view class="btn-item">
        <u-button type="primary" @click="commitHandler" :loading="submitLoading" :custom-style="buttonStyle"> 立即提交 </u-button>
      </view>
    </view>
  </view>
</template>

<script>
import GTForm from "@/components/form/GTForm/index.vue";
import bpmRequest from "@/pages_farmland/apis/bpm";
import formUtils from "@/utils/form";
import * as turf from "@turf/turf";
import _ from "lodash";

export default {
  name: "PointFillPage",
  components: {
    GTForm,
  },
  data() {
    return {
      formUid: null,
      useCache: false,
      cacheRecordId: null,
      pointId: "",
      formRecordId: null,
      currentTask: null,
      options: null,
      submitLoading: false,
      readonly: false,
      loading: false,
      defaultRecordData: {},
      buttonStyle: {
        width: "100%",
        backgroundColor: this.$constants.COLOR.PRIMARY_COLOR,
      },
    };
  },
  watch: {
    loading(newValue) {
      if (newValue) {
        uni.showLoading({
          title: "加载中",
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  methods: {
    gtFormInit(e) {
      this.defaultRecordData = { ...e };
    },
    onBackPress(e) {
      if (e.from === "backbutton") {
        this.navBackClick();
        return true;
      }
    },
    navBackClick() {
      const formData = _.omitBy(this.$refs.gtForm.getFormData, _.isNil);
      const defaultFormData = _.omitBy(this.defaultRecordData, _.isNil);
      console.log("formData :>> ", formData, defaultFormData);
      const same = _.isEqual(defaultFormData, formData);
      console.log("same :>> ", same);
      if (this.useCache && !same) {
        uni.showModal({
          title: "提示",
          content: `数据未保存，是否确定退出此页面？`,
          success: async (e) => {
            if (e.confirm) {
              uni.navigateBack();
            }
          },
        });
      } else {
        uni.navigateBack();
      }
    },
    commitHandler() {
      this.$refs.gtForm.submit();
    },
    async submitHandler(data) {
      console.log("提交的数据", data);

      this.submitLoading = true;
      const values = {
        ...formUtils.toApiFormData(data),

        pcode: this.currentTask.pointInfo.pcode,
        pname: this.currentTask.pointInfo.pname,
        ccode: this.currentTask.pointInfo.ccode,
        cname: this.currentTask.pointInfo.cname,
        fcode: this.currentTask.pointInfo.fcode,
        fname: this.currentTask.pointInfo.fname,
        point_code: this.currentTask.pointInfo.point_code,
        area_verified: this.currentTask.pointInfo.area_verified,
        area_reference: this.currentTask.pointInfo.area_reference,
      };

      const from = turf.point([values.sampling_longitude, values.sampling_latitude]);
      const to = turf.point([this.currentTask.pointInfo.preset_longitude, this.currentTask.pointInfo.preset_latitude]);

      const distance = turf.distance(from, to);
      console.log("distance :>> ", distance);
      if (distance > 0.2 && values.is_offset === "否") {
        uni.showModal({
          title: "提示",
          content: `当前样点现场坐标已超过预设点位200米，请填写偏移说明或编辑样点坐标`,
          showCancel: false,
          success: async (e) => {
            console.log("e :>> ", e);
            this.submitLoading = false;
          },
        });
      } else {
        console.log("values :>> ", values);
        console.log("currentTask :>> ", this.currentTask);
        if (this.currentTask.sampling_task_type === "土壤") {
          values.sample_serial_number = `${this.currentTask.pointInfo.year}-${this.currentTask.point_code}-T`;
        } else {
          values.sample_serial_number = `${this.currentTask.pointInfo.year}-${this.currentTask.point_code}-${values.agricultural_type_code}`;
        }
        if (values._id) {
          await this.updateFormData(this.formUid, values, this.cacheRecordId);
        } else {
          await this.addFormData(this.formUid, values, this.cacheRecordId);
        }
        this.submitLoading = false;
      }
    },
    async addFormData(formUid, values, cacheRecordId) {
      try {
        await this.$apis.formData.addFormRecord(formUid, values, cacheRecordId);
        let proccode;
        if (this.currentTask.project_type === "省控监测采样项目") {
          proccode = "省控点审批流程";
        } else if (this.currentTask.project_type === "国控监测采样项目") {
          proccode = "国控点审批流程";
        } else if (this.currentTask.project_fcode) {
          proccode = "县级审批流程";
        } else {
          proccode = "市级审批流程";
        }
        await bpmRequest.auditMisSubmit(proccode, "sampling_task", this.currentTask._id, {
          audit_status: "已提交",
        });
        uni.showToast({
          icon: "none",
          title: "数据已提交",
        });
        uni.navigateBack();
      } catch ({ msg, data, code }) {
        switch (code) {
          case 4011:
            break;
          default:
            break;
        }
      } finally {
        this.submitLoading = false;
      }
    },
    async updateFormData(formUid, values, cacheRecordId) {
      try {
        await this.$apis.formData.updateFormRecord(formUid, values._id, values, cacheRecordId);
        if (["未填报", "采样单位已退回", "县级管理员已退回", "市级管理员已退回", "省级管理员已退回"].indexOf(this.currentTask.audit_status) !== -1) {
          let proccode;
          if (this.currentTask.project_type === "省控监测采样项目") {
            proccode = "省控点审批流程";
          } else if (this.currentTask.project_type === "国控监测采样项目") {
            proccode = "国控点审批流程";
          } else if (this.currentTask.project_fcode) {
            proccode = "县级审批流程";
          } else {
            proccode = "市级审批流程";
          }
          await bpmRequest.auditMisSubmit(proccode, "sampling_task", this.currentTask._id, {});
        }

        uni.showToast({
          icon: "none",
          title: "数据已提交",
          mask: true,
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } catch ({ msg, data, code }) {
        switch (code) {
          case 4011:
            break;
          default:
            break;
        }
      } finally {
        this.submitLoading = false;
      }
    },
    async saveLocalHandler() {
      const formData = await this.$refs.gtForm.getFormData;
      console.log("formData :>> ", formData);
      this.saveLocal(formData);
    },
    // 本地存储
    async saveLocal(formData) {
      if (!formData) return;
      const cacheFormRecordId = await this.$apis.formDataLocal.upsertCacheRecord(this.formUid, this.cacheRecordId, formData, this.pointId);
      if (cacheFormRecordId) {
        this.cacheRecordId = cacheFormRecordId;
        uni.showToast({
          icon: "none",
          title: "数据已缓存",
        });
        // 重置变化检测
      } else {
        console.log("数据缓存失败");
      }
    },
  },
  async onLoad(e) {
    try {
      this.loading = true;
      this.formUid = e.formUid;
      const formDef = await this.$apis.formDef.getFormDef(this.formUid);
      const task = uni.getStorageSync(e.taskId);
      this.currentTask = task;
      let recordData = {
        point_id: task.sampling_point_id,
      };
      const opt = {
        formDef,
        formRecordData: recordData,
        readonly: false,
      };

      console.log("task :>> ", task);
      const res = await this.$apis.formData.getFormRecords(this.formUid, {
        filter: ["=", "point_id", task.sampling_point_id],
      });
      console.log("res :>> ", res);
      if (res.list && res.list.length) {
        recordData = formUtils.toFrontFormData(res.list[0]);
      } else {
        this.useCache = true;
        this.pointId = task.sampling_point_id;
        // const cac = await this.$apis.formDataLocal.getCacheRecords(this.formUid)
        // console.log('cac :>> ', cac)
        const cacheDataResult = await this.$apis.formDataLocal.getCacheRecord(this.formUid, task.sampling_point_id);
        console.log("cacheDataResult :>> ", cacheDataResult);
        if (cacheDataResult) {
          this.cacheRecordId = task.sampling_point_id;
          recordData = formUtils.toFrontFormData(cacheDataResult.content);
        } else {
          const formMap = {
            土壤: "ncpcjxxb",
            农产品: "trcjxxb",
          };
          const mainRes = await this.$apis.formData.getFormRecords(formMap[task.sampling_task_type], {
            filter: ["=", "point_id", task.sampling_point_id],
          });
          console.log("mainRes :>> ", mainRes);
          if (mainRes && mainRes.list && mainRes.list.length) {
            const obj = mainRes.list[0];
            const r = {
              point_id: obj.point_id,
              town: obj.town,
              village: obj.village,
              group_name: obj.group_name,
              land_type: obj.land_type,
              surrounding_east: obj.surrounding_east,
              surrounding_west: obj.surrounding_west,
              water_condition: obj.water_condition,
              current_season_disaster: obj.current_season_disaster,
              sampling_longitude: obj.sampling_longitude,
              sampling_latitude: obj.sampling_latitude,
              sampling_elevation: obj.sampling_elevation,
              sampling_address: obj.sampling_address,
              is_offset: obj.is_offset,
              offset_description: obj.offset_description,
              gps_device_display: obj.gps_device_display,
              center_east_direction: obj.center_east_direction,
              center_west_direction: obj.center_west_direction,
              center_south_direction: obj.center_south_direction,
              center_north_direction: obj.center_north_direction,
              ph_measurement_photo: obj.ph_measurement_photo,
              sampling_team: obj.sampling_team,
              sampling_team_leader: obj.sampling_team_leader,
              sampling_team_members: obj.sampling_team_members,
              nitrogen: obj.nitrogen,
              phosphorus: obj.phosphorus,
              potassium: obj.potassium,
              organic: obj.organic,
              compound: obj.compound,
              remarks: obj.remarks,
              surrounding_south: obj.surrounding_south,
              surrounding_north: obj.surrounding_north,
            };
            recordData = formUtils.toFrontFormData(r);
          }
        }
      }

      opt.formRecordData = {
        ...recordData,
        point_id: task.sampling_point_id,
        pcode: task.pointInfo.pcode,
        pname: task.pointInfo.pname,
        ccode: task.pointInfo.ccode,
        cname: task.pointInfo.cname,
        fcode: task.pointInfo.fcode,
        fname: task.pointInfo.fname,
        point_code: task.pointInfo.point_code,
        area_verified: task.pointInfo.area_verified,
        area_reference: task.pointInfo.area_reference,
      };

      this.options = opt;
    } catch (error) {
      console.error("加载表单失败:", error);
    } finally {
      this.loading = false;
    }
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
}

.scroll-container {
  background-color: #f5f5f5;
  flex: 1;
}

.form-content {
  padding-bottom: 68px;
  background-color: white;
}

.btn-list {
  display: flex;
  padding: 8px;
  width: 100%;
  box-sizing: border-box;
  background-color: white;
  border-top: 1px solid #eee;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 8;
}

.btn-item {
  flex: 1;
  margin: 0 8px;
}
</style>
