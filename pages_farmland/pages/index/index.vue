<template>
  <view class="page-container">
    <u-navbar title="工作台" :bgColor="$constants.COLOR.PRIMARY_COLOR" :placeholder="true" :safe-area-inset-top="true" @leftClick="backHome">
      <template #left>
        <u-icon name="home" size="40rpx" color="#fff"></u-icon>
      </template>
    </u-navbar>

    <scroll-view class="scroll-container" scroll-y>
      <view class="task-item" v-for="menu in menuList" :key="menu._id" @click="menuItemClick(menu)">
        <view class="task-item-content">
          <view class="task-item-title">{{ menu.name }}</view>
        </view>
        <u-icon name="arrow-right" />
      </view>
    </scroll-view>

    <Tabbar :config="tabbar" :value="0"></Tabbar>
  </view>
</template>

<script>
import Tabbar from "@/components/common/Tabbar";
import { tabbar } from "@/pages_farmland/config";

export default {
  name: "Home",
  components: { Tabbar },
  data() {
    return {
      tabbar,
      menuList: [
        {
          _id: "1",
          name: "省控监测采样",
          val: "省控监测采样项目",
        },
        {
          _id: "2",
          name: "国控监测采样",
          val: "国控监测采样项目",
        },
        {
          _id: "3",
          name: "其他监测采样",
          val: "其他项目",
        },
      ],
    };
  },
  onShow() {},
  mounted() {},
  methods: {
    backHome() {
      uni.reLaunch({
        url: "/pages/Workspace/index",
      });
    },
    menuItemClick(menu) {
      console.log("menu :>> ", menu);
      uni.navigateTo({
        url: `/pages_farmland/pages/workspaceMenu/workspaceMenu?menu=${menu.val}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  background-color: #f5f5f5;
  flex: 1;
}

.task-item {
  padding: 16px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
  margin: 12px;
}

.task-item-content {
  flex: 1;
}

.task-item-title {
  font-size: 16px;
  color: #333;
}

:deep(.loading-black) {
  padding: 10px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
}
</style>
