<template>
  <view class="page-container">
    <u-navbar title="地图" placeholder :bgColor="$constants.COLOR.PRIMARY_COLOR" :safe-area-inset-top="true" @leftClick="navigateBack"></u-navbar>

    <SearchBar :config="searchConfig" v-if="searchConfig" @change="searchChangeHandler" />

    <view class="map-content-wrapper">
      <view class="map-container">
        <BaseMap class="map-component" ref="mapRef" :filterMap="filterMap" v-if="projectId" :projectId="projectId" />
      </view>
    </view>
  </view>
</template>

<script>
import SearchBar from "@/pages_farmland/components/SearchBar/index.vue";
import { SEARCH_CONFIG } from "./list.config";
import BaseMap from "./Map/index.vue";

export default {
  name: "pointMapPage",
  components: {
    SearchBar,
    BaseMap,
  },
  data() {
    return {
      searchConfig: SEARCH_CONFIG,
      filterMap: {},
      projectId: "",
      mapRef: null,
    };
  },
  onShow() {
    // 页面显示时的逻辑
  },
  onLoad(e) {
    this.initPage(e);
  },
  methods: {
    navigateBack() {
      uni.navigateBack();
    },
    async searchChangeHandler(field, filter) {
      console.log(field, filter);
      this.filterMap[field] = filter;
      if (this.$refs.mapRef) {
        await this.$refs.mapRef.reloadData();
      }
    },
    initPage(e) {
      console.log(e);
      if (e.projectId) {
        this.projectId = e.projectId;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/* 页面主容器 */
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 地图内容包装器 */
.map-content-wrapper {
  flex: 1;
  overflow: hidden;
}

/* 地图容器 */
.map-container {
  height: 100%;
  width: 100%;
}

/* 地图组件 */
.map-component {
  height: 100%;
  width: 100%;
}
</style>
