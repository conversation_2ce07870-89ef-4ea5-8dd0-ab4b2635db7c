const tks = [
	'9280a716be4df212fd20ebf0f5c94dce',
	'7191874d4f84f45a31a464919da9230a',
	'a6b9ed4542685fcc5a38c3b5a34db566',
]

function getRandomItem(arr) {
	const randomIndex = Math.floor(Math.random() * arr.length)
	return arr[randomIndex]
}
export const getStyle = {
	container: "map-box", // container ID
	style: {
		version: 8,
		sources: {
			'baseMap': {
				type: 'raster',
				tiles: [
					'https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}'
				],
				tileSize: 256,
				maxzoom: 16
			},
			'cia-map': {
				type: 'raster',
				tiles: [
					'http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8'
				],
				tileSize: 256,
				maxzoom: 16
			},

		},
		layers: [{
				id: 'baseMap',
				type: 'raster',
				source: 'baseMap',
			},
			{
				id: 'cia-map',
				type: 'raster',
				source: 'cia-map',
			}
		]
	}, // style URL
	center: [108, 23], // starting position [lng, lat]
	zoom: 10, // starting zoom
	doubleClickZoom: false, // 禁止双击缩放
	pitchWithRotate: false, // 禁止绕x轴旋转
	dragRotate: false,
	touchPitch: false,
	fitBoundsOptions: {
		padding: 20,
	},
}