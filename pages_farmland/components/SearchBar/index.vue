<template>
  <view class="search-container">
    <view class="main-search-row">
      <view class="admin-filter-section" v-if="searchConfig.adminFilter">
        <u-picker :show="pickerShow" :columns="columns" @confirm="handleConfirm" @cancel="pickerShow = false"> </u-picker>
        <u-button type="text" @click="pickerShow = true">
          {{ currentAdminLabel }}
          <u-icon name="arrow-down" size="12" />
        </u-button>
      </view>

      <view class="search-input-section" v-if="searchConfig && searchConfig.searchFilter">
        <u-search :placeholder="`请输入${searchConfig.searchFilter.label}`" @search="searchHandler" @clear="searchHandler" :showAction="false" />
      </view>
    </view>
    <view class="filter-options-row" v-if="searchConfig.selectFilters && searchConfig.selectFilters.length">
      <view class="filter-option-item" v-for="f in searchConfig.selectFilters" :key="f.field" @click="showFilterPicker(f)">
        <text>{{ f.value !== null ? getFilterLabel(f) : f.label }}</text>
        <u-icon name="arrow-down" size="12" />
      </view>
    </view>

    <!-- Filter Picker -->
    <u-picker :show="filterPickerShow" :columns="filterColumns" keyName="label" @confirm="handleFilterConfirm" @cancel="filterPickerShow = false">
    </u-picker>
  </view>
</template>

<script>
export default {
  name: "SearchBar",
  props: {
    config: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      searchConfig: { ...this.config },
      columns: [],
      value: [],
      defaultLabel: "",
      currentAdmin: null,
      adminMap: {},
      searchValue: "",
      pickerShow: false,
      filterPickerShow: false,
      filterColumns: [],
      currentFilter: null,
    };
  },
  computed: {
    currentAdminLabel() {
      const text = this.currentAdmin ? this.currentAdmin.label : this.defaultLabel;
      return text.length > 3 ? `${text.slice(0, 3)}...` : text;
    },
  },
  mounted() {
    if (this.searchConfig.adminFilter) {
      this.init();
    }
  },
  methods: {
    searchHandler(e) {
      const filter = e && e.value ? ["like", this.searchConfig.searchFilter.field, `%${e.value}%`] : null;
      this.$emit("change", this.searchConfig.searchFilter.field, filter);
    },
    handleChange(e) {
      console.log(e);
      console.log("e.op :>> ", e.op);
      const filter = e.value !== null ? [e.op || "=", e.field, e.value] : null;
      this.$emit("change", e.field, filter);
    },
    showFilterPicker(filter) {
      this.currentFilter = filter;
      this.filterColumns = [
        filter.options.map((option) => ({
          label: option.label,
          value: option.value,
        })),
      ];
      this.filterPickerShow = true;
    },
    getFilterLabel(filter) {
      const option = filter.options.find((opt) => opt.value === filter.value);
      return option ? option.label : filter.label;
    },
    handleFilterConfirm(e) {
      console.log("选择", e);

      if (this.currentFilter) {
        this.currentFilter.value = e.value[0].value;
        this.handleChange(this.currentFilter);
      }
      this.filterPickerShow = false;
    },
    async init() {
      const config = this.searchConfig.adminFilter;
      const userInfo = uni.getStorageSync("userInfo") || {};

      console.log(userInfo, "userInfo");

      let level = Object.keys(config.adminConfigs)[0];
      this.defaultLabel = userInfo.fname || userInfo.cname || userInfo.pname || config.defaultLabel;
      console.log(this.defaultLabel);

      if (userInfo.tcode && config.adminConfigs.village) {
        level = "village";
      } else if (userInfo.fcode && config.adminConfigs.town) {
        level = "town";
      } else if (userInfo.ccode && config.adminConfigs.county) {
        level = "county";
      } else if (userInfo.pcode && config.adminConfigs.city) {
        level = "city";
      }
      const conf = config.adminConfigs[level];

      try {
        const res = await this.$apis.formData.getFormRecords(conf.url, {
          filter: conf.filterField && userInfo[conf.filterField] ? ["=", conf.filterField, userInfo[conf.filterField]] : null,
        });
        const col = [
          ...res.list.map((item) => {
            return {
              ...item,
              label: item[conf.labelkey],
              value: item[conf.valuekey],
              level: conf.level,
              next: conf.next,
              field: conf.field,
            };
          }),
        ];
        console.log(res, col);
        this.columns = [col];
      } catch (error) {
        console.error("Failed to load admin data:", error);
      }
    },
    async columnChange({ selectedItem, resolve, finish }) {
      const config = this.searchConfig.adminFilter;
      console.log(selectedItem);
      if (selectedItem.next) {
        const conf = config.adminConfigs[selectedItem.next];
        let col = [];
        if (this.adminMap[selectedItem[conf.filterField]]) {
          col = this.adminMap[selectedItem.value];
        } else {
          try {
            const res = await this.$apis.formData.getFormRecords(conf.url, {
              filter: ["=", conf.filterField, selectedItem.value],
            });
            col = [
              ...res.list.map((item) => {
                return {
                  ...item,
                  label: item[conf.labelkey],
                  value: item[conf.valuekey],
                  next: conf.next,
                  level: conf.level,
                  field: conf.field,
                };
              }),
            ];
            this.adminMap[selectedItem.value] = col;
          } catch (error) {
            console.error("Failed to load column data:", error);
          }
        }

        resolve(col);
      } else {
        finish();
      }
    },
    handleConfirm(e) {
      this.currentAdmin = e.value && e.value.length ? e.value[e.value.length - 1] : null;
      this.pickerShow = false;

      const filter = this.currentAdmin ? ["=", this.currentAdmin.field, this.currentAdmin.value] : null;

      this.$emit("change", "admin", filter);
      console.log(filter);
    },
  },
};
</script>
<style lang="scss" scoped>
/* 搜索容器主体 */
.search-container {
  background-color: white;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 主搜索行 */
.main-search-row {
  display: flex;
  align-items: center;
}

/* 管理员筛选区域 */
.admin-filter-section {
  padding-left: 12px;
}

/* 搜索输入区域 */
.search-input-section {
  flex: 1;
}

/* 筛选选项行 */
.filter-options-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

/* 筛选选项项目 */
.filter-option-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
  gap: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.filter-option-item:hover {
  background-color: #e8e8e8;
}

.filter-option-item text {
  color: #333;
}
</style>
