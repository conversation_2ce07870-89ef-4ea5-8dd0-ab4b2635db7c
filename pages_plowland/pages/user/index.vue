<template>
  <view>
    <u-navbar title="我的" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder></u-navbar>
    <u-cell-group>
      <u-cell v-for="item in userList" :title="item.title" :value="getValue(item)" :icon="item.icon"></u-cell>
      <u-cell title="应用版本" :value="versionInfo.version" isLink @click="checkVersion"></u-cell>
    </u-cell-group>
    <view class="logout">
      <u-button text="退出登录" type="primary" @click="outLogin"></u-button>
    </view>
    <Tabbar :config="tabbar" :value="1"></Tabbar>
  </view>
</template>

<script>
import Tabbar from "@/components/common/Tabbar";
import { tabbar } from "@/pages_plowland/config";

const VERSION_TEST = "DEV.0.1";
export default {
  components: { Tabbar },
  data() {
    return {
      tabbar,
      userInfo: null,
      userList: [
        {
          title: "姓名",
          value: "nickname",
        },
        {
          title: "角色",
          value: "role_name",
          type: "role_name",
          customRender: (role_name) => {
            console.log("role_name", role_name);

            let str = "";
            role_name.forEach((i) => {
              str += `、${i}`;
            });
            str = str.replace("、", "");
            return str;
          },
        },
        {
          title: "邮箱",
          value: "email",
        },
        {
          title: "电话",
          value: "phone",
        },
        {
          title: "单位",
          value: "company",
        },
      ],
      versionInfo: {
        version: null,
      },
    };
  },
  created() {
    let version = "";
    try {
      version = plus.runtime.version;
    } catch (e) {
      version = VERSION_TEST;
    }
    this.versionInfo.version = version;

    const userInfo = JSON.parse(uni.getStorageSync("userInfo"));
    console.log(userInfo);
    this.userInfo = userInfo;
  },
  methods: {
    getValue(item) {
      console.log("item", item);

      if (!item.type) {
        return this.userInfo[item.value];
      }
      if (item.type == "role_name") {
        return item.customRender(this.userInfo[item.value]);
      }
    },
    outLogin() {
      uni.showToast({
        title: "退出成功",
        complete() {
          uni.clearStorageSync();
          uni.$u.route({
            type: "redirectTo",
            url: `/pages/Login/index`,
          });
        },
      });
    },
    async checkVersion() {
      this.showLoading(this.$constants.VERSION.CHECK);
      const result = await this.$apis.version.checkVersion();
      uni.hideLoading();
      if (!result) {
        this.showError(this.$constants.VERSION.FAILED);
        return;
      }
      const { upgrade, version, url } = result;
      if (upgrade === true) {
        uni.showModal({
          title: "版本更新",
          content: this.$constants.VERSION.NEW_VERSION,
          success(res) {
            if (res.confirm) {
              plus.runtime.openURL(url);
            }
          },
        });
      } else {
        this.showInfo(this.$constants.VERSION.LATEST_ALREADY);
      }
    },

    showLoading(msg) {
      uni.showLoading({
        title: msg,
        mask: true,
      });
    },
    showError(msg) {
      uni.showToast({
        title: msg,
        icon: "error",
        duration: 2000,
      });
    },

    showInfo(msg) {
      uni.showToast({
        title: msg,
        icon: "info",
        duration: 2000,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .u-navbar__content__left {
  display: none;
}
.logout {
  padding: 50rpx;
  margin-top: 100rpx;
}
/deep/.u-cell__value {
  width: 50vw;
}
</style>
