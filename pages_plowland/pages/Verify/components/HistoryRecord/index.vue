<template>
  <view class="record">
    <template v-if="list.length !== 0">
      <u-tabs :list="list" scrollable keyName="task_name" @click="tabClick"></u-tabs>
      <template v-if="formName === '地块核查记录表'">
        <u-cell-group v-if="info" :border="false">
          <u-cell title="种植作物" :border="false" :label="info.种植作物 == '其它' ? info.备注 : info.种植作物"></u-cell>
          <u-cell title="在用农户姓名" :border="false" :label="info.在用农户姓名"></u-cell>
          <u-cell title="核查人员" :border="false" :label="info.核查人员"></u-cell>
          <u-cell title="核查时间" :border="false">
            <u--text
              slot="label"
              mode="date"
              size="14px"
              color="#909193"
              margin="5px 0 0 0"
              :text="info.核查时间"
              format="yyyy-mm-dd hh:MM"
            ></u--text>
          </u-cell>
        </u-cell-group>
      </template>
      <template v-if="formName === '未确权地块核查记录表'">
        <u-cell-group :border="false">
          <u-cell title="种植作物" :border="false" :label="info.种植作物.join(',')"></u-cell>
          <u-cell title="作物详情" :border="false" :label="info.备注"></u-cell>
          <u-cell title="现场照片" :border="false">
            <view slot="label">
              <u--image
                v-for="(img, index) in info.现场照片"
                :key="index"
                :showLoading="true"
                :src="img"
                width="80px"
                height="80px"
                @click="previewImg(index)"
              ></u--image>
            </view>
          </u-cell>
          <u-cell title="在用农户姓名" :border="false" :label="info.在用农户姓名"></u-cell>
          <u-cell title="核查人员" :border="false" :label="info.核查人员"></u-cell>
          <u-cell title="核查时间" :border="false">
            <u--text
              slot="label"
              mode="date"
              size="14px"
              color="#909193"
              margin="5px 0 0 0"
              :text="info.核查时间"
              format="yyyy-mm-dd hh:MM"
            ></u--text>
          </u-cell>
        </u-cell-group>
      </template>
    </template>
    <template v-else-if="!loading">
      <u-empty mode="data" marginTop="120" text="暂无历史核查记录数据"></u-empty>
    </template>
  </view>
</template>

<script>
import customRequest from "@/pages_plowland/apis/custom";
import Env from "@/env";

export default {
  props: {
    formName: {
      type: String,
    },
    id: {
      type: String,
    },
    task: {
      type: Array,
    },
  },
  data() {
    return {
      loading: false,
      list: [],
      info: null,
    };
  },
  watch: {
    id() {
      this.getRecord();
    },
  },
  computed: {
    plantRemark() {
      return "asd";
    },
  },
  async mounted() {
    await this.getRecord();
  },
  methods: {
    // 查看图片
    previewImg(index) {
      uni.previewImage({
        urls: this.info.现场照片,
        current: index,
      });
    },
    // 切换查看
    tabClick(val) {
      this.info = val;
    },
    // 获取数据
    async getRecord() {
      this.loading = true;
      try {
        const { fcode } = JSON.parse(uni.getStorageSync("userInfo"));
        const params = {
          filter: ["=", "__main_form_record_uid_field__", this.id],
        };
        const data = await customRequest.getFormRecords(fcode, this.formName, params);
        const list = [];
        data.list.forEach((item) => {
          const task = this.task.find((items) => items._id === item.任务_id);
          if (task) {
            const processedItem = { ...item, task_info: task };
            processedItem.task_name = `${task.年份}${task.时间段}${task.任务名称}`;
            if (processedItem.现场照片) {
              processedItem.现场照片 = processedItem.现场照片.map((img) => {
                return `${Env.OSS_ENDPOINT}/${img}`;
              });
            }
            list.push(processedItem);
          }
        });
        if (list.length !== 0) this.info = list[0];
        this.list = list;
        console.log(this.list);
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.record {
  min-height: 50vh;

  /deep/ .u-cell__label {
    font-size: 28rpx !important;
  }
}
</style>
