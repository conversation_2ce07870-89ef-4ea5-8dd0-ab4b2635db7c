<template>
  <view class="content">
    <u-navbar title="地块核查" :bgColor="$constants.COLOR.PRIMARY_COLOR" :placeholder="true" @leftClick="navigateBack">
      <view slot="right" class="area-text" :style="{ fontSize: areaValue.length > 5 ? '24rpx' : '28rpx' }" @click="districtSelectShow = true"
        >切换[{{ areaValue }}]</view
      >
    </u-navbar>
    <u-overlay :show="searchOverlay" @click="closeSearch" zIndex="98"></u-overlay>
    <filter-popup
      v-model="filterVisible"
      :data="filterOptions"
      :customStyle="filterStyle"
      @close="filterVisible = false"
      @confirm="confirmFilter"
    ></filter-popup>
    <view class="search-box" :style="topFilterStyle">
      <u-search placeholder="请输入地块编码" bgColor="#fff" v-model="searchValue" @custom="searchPointByCode" @search="searchPointByCode"></u-search>
      <view class="filter" @click="filterVisible = !filterVisible">
        <image src="/static/images/plowland/filter.png" mode="heightFix" class="icon"></image>
        <view class="text">筛选</view>
      </view>
      <view class="search-result" v-if="searchOverlay">
        <view v-if="searchList.length == 0 && searchTotal == 0" style="padding: 40rpx 0">
          <u-empty mode="search"></u-empty>
        </view>
        <view class="block" v-for="item in searchList" :key="item.地块编码" @click="searchItemClick(item)"> 地块编码：{{ item.地块编码 }}</view>
      </view>
    </view>
    <view :style="contentStyle">
      <Map
        ref="map"
        :point="pointList"
        :multipleSelect="!readonlyRole"
        :config="mapConfig"
        @point-click="pointClick"
        @getSelectPoint="getSelectPoint"
        @moveEnd="refreshPoint"
        @mapLoaded="mapLoaded = true"
        @pointLoaded="pointLoaded = true"
        @pointLoading="pointLoaded = false"
        @zoomChange="zoomChange"
      ></Map>

      <view class="check-all-container" v-show="selectPointList.length > 1">
        <view class="select-count">已选择{{ selectPointList.length }}个地块</view>
        <view class="btn-list">
          <view class="close-btn">
            <u-button :plain="true" type="info" size="mini" text="取消选择" @click="closeSelectPointIdList"> </u-button>
          </view>
          <view class="close-btn">
            <u-button type="primary" size="mini" text="同步数据" @click="openSelectTask('sync')"></u-button>
          </view>
          <view v-if="!readonlyRole">
            <u-button type="primary" size="mini" text="批量填报" @click="showFillPopup('multiFill')"> </u-button>
          </view>
        </view>
      </view>
    </view>

    <AreaPicker v-model="districtSelectShow" @confirm="confirDistrictSelct"></AreaPicker>

    <!-- 点位详情弹窗 -->
    <u-popup :show="pointInfoShow" @close="pointInfoShowClose" closeable :overlay="false">
      <view class="point-container" v-if="currentPointRecord">
        <view class="point-code" @click="getPointCode(currentPointRecord.plotCode)"> 地块编码：{{ currentPointRecord.plotCode }}</view>
        <view class="point-item-container">
          <template v-if="currentPointRecord.地块状态 === '已核查'">
            <view class="point-item">种植作物：{{ formatText(currentPointRecord) }}</view>
            <view class="point-item" v-if="currentPointRecord.地块类型 == '未确权地块'"> 作物详情：{{ currentPointRecord.remark }}</view>
            <view class="point-item">地址：{{ addressText(currentPointRecord) }}</view>
            <view class="point-item">核查日期：{{ formatDate(currentPointRecord.verifyTime) }}</view>
          </template>
          <template v-else>
            <view class="point-item">地址：{{ addressText(currentPointRecord) }}</view>
            <view class="point-item">地块状态：{{ currentPointRecord.地块状态 }}</view>
          </template>
          <view class="point-item primary" style="text-align: end" @click="openHistory">查看历史核查记录数据</view>
        </view>
        <view class="btn-list">
          <u-button type="primary" :plain="true" text="导航" @click="navigationMap()"></u-button>
          <template v-if="editRole">
            <u-button type="error" text="删除" @click="removeVerifyData"></u-button>
            <u-button type="primary" text="编辑" @click="showFillPopup('edit')"></u-button>
          </template>
          <u-button v-else-if="viewRole" type="primary" text="查看" @click="showFillPopup('read')"></u-button>
          <template v-else-if="fillRole">
            <u-button type="primary" text="同步数据" @click="openSelectTask('sync')"></u-button>
            <u-button type="primary" text="填报" @click="showFillPopup('fill')"></u-button>
          </template>
        </view>
      </view>
    </u-popup>
    <!-- 同步任务选择 -->
    <u-picker
      :show="taskSelectVisible"
      :columns="[taskList]"
      keyName="task_name"
      closeOnClickOverlay
      @confirm="selectTaskInfo"
      @cancel="taskSelectVisible = false"
      @close="taskSelectVisible = false"
    ></u-picker>
    <!-- 填报弹窗 -->
    <form-popup
      v-model="showFillFormPopup"
      :data="formRecordData"
      :formName="formInputName"
      :readonly="btnState == 'read'"
      @close="closeFillPopup"
      @submit="submitOperateHandler"
    ></form-popup>
    <!-- 历史任务查看 -->
    <u-popup :show="historyVisible" closeOnClickOverlay @close="historyVisible = false">
      <history-record :id="selectId" :task="taskList" :formName="recordFormName"></history-record>
    </u-popup>
  </view>
</template>
<script>
import BasePage from "@/pages/BasePage";
import customRequest from "@/pages_plowland/apis/custom";
import Map from "@/pages_plowland/components/Map";
import AreaPicker from "@/pages_plowland/components/AreaPicker/index";
import FormPopup from "@/pages_plowland/components/FormPopup/index";
import FilterPopup from "@/pages_plowland/components/FilterPopup/index";
import Navigator from "@/tools/Navigation.js";
import HistoryRecord from "../components/HistoryRecord/index";
import dayjs from "dayjs";
import { cropNameList, rules, MAP_CONFIG } from "./config.js";

const TASK_FORM_NAME = "任务表";
const CERTAIN_FIELDS = ["种植作物", "核查时间", "在用农户姓名", "核查人员", "备注"];
const UNCERTAIN_FIELDS = ["种植作物", "现场照片", "其它作物", "在用农户姓名", "核查时间", "核查人员", "备注"];
const FIELD_KEY = {
  cropTypeList: "种植作物",
  plotCode: "地块编码",
  cropType: "种植作物",
  remark: "备注",
  farmerName: "在用农户姓名",
  verifyUser: "核查人员",
  verifyTime: "核查时间",
  pics: "现场照片",
  otherCropType: "其它作物",
};

export default {
  mixins: [BasePage],
  components: {
    Map,
    AreaPicker,
    FormPopup,
    FilterPopup,
    HistoryRecord,
  },
  data() {
    return {
      // 其他
      requestLoad: false,
      userInfo: {},
      fcode: null,
      // 过滤与搜索
      areaFilter: null,
      typeFilter: null,
      stateFilter: null,
      searchOverlay: false,
      filterVisible: false,
      filterOptions: [
        {
          title: "土地类型",
          key: "typeFilter",
          multiple: false,
          options: ["水田", "旱地", "水浇地"],
        },
        {
          title: "核查状态",
          key: "stateFilter",
          multiple: false,
          options: ["未核查", "已核查"],
        },
      ],
      searchList: [],
      searchTotal: 0,
      areaValue: "",
      searchValue: "",
      districtSelectShow: false,
      // 数据
      list: [],
      pointList: [],
      formRecordData: {},
      cropNameList,
      rules,
      btnState: "",
      formInputName: "",
      page: {
        pageNum: 1,
        pageSize: 1000,
      },
      recordFormName: "",
      // 任务数据
      taskList: [],
      currentTaskId: "",
      lastTaskId: "",
      selectTaskType: "",
      selectId: null,
      // 地图数据
      selectPointList: [],
      pointInfoShow: false,
      currentPointRecord: null,
      plotCreateInfo: {
        not: false,
        code: "",
      },
      pointLoaded: false,
      pointInfoLoading: false,
      mapLoaded: false,
      mapZoom: 0,
      boundsInfo: null,
      // 弹窗
      taskSelectVisible: false,
      showFillFormPopup: false,
      historyVisible: false,
    };
  },
  computed: {
    readonlyRole() {
      if (!this.userInfo.role_code) return true;
      return !this.userInfo.role_code.includes("check_manage");
    },
    mapConfig() {
      if (!this.userInfo || !this.userInfo.fcode) return;
      const result = {
        layers: MAP_CONFIG.layers,
        point: MAP_CONFIG.point,
        sources: MAP_CONFIG.sources(this.userInfo.ccode, this.userInfo.fcode),
      };
      return result;
    },
    editRole() {
      if (this.plotCreateInfo && !this.plotCreateInfo.not && this.currentPointRecord.地块状态 === "已核查" && !this.readonlyRole) {
        return true;
      } else {
        return false;
      }
    },
    viewRole() {
      return this.currentPointRecord.地块状态 === "已核查";
    },
    fillRole() {
      return this.currentPointRecord.地块状态 === "未核查" && !this.readonlyRole;
    },
    topFilterStyle() {
      const system = uni.getSystemInfoSync();
      return {
        position: "fixed",
        width: "100vw",
        zIndex: 99,
        top: `${system.statusBarHeight + 44}px`,
      };
    },
    filterStyle() {
      const system = uni.getSystemInfoSync();
      return {
        top: `${system.statusBarHeight + 88}px`,
      };
    },
    contentStyle() {
      const system = uni.getSystemInfoSync();
      return {
        position: "fixed",
        width: "100vw",
        zIndex: 1,
        top: `${system.statusBarHeight + 88}px`,
        bottom: `${system.safeAreaInsets.bottom + 50}px`,
      };
    },
  },
  async mounted() {
    uni.$off("refresh-point");
    uni.$off("flyToPoint");
    uni.$on("refresh-point", () => {
      this.reloadData();
    });
    uni.$on("flyToPoint", (data) => {
      if (this.mapLoaded) {
        this.flyToMap(data.中心经度, data.中心纬度, 18);
      }
      const interval = setInterval(() => {
        if (this.pointLoaded) {
          this.$refs.map.pointClick(data);
          clearInterval(interval);
        }
      }, 200);
    });
    this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
    this.fcode = this.userInfo.fcode;
    this.areaValue = this.userInfo.fname ? this.userInfo.fname : "";
    // 获取任务
    await this.getCurrentTask();
    this.handleOptions();
  },

  methods: {
    openHistory() {
      this.recordFormName = this.selectPointList[0].地块类型 === "确权地块" ? "地块核查记录表" : "未确权地块核查记录表";
      this.selectId = this.selectPointList[0]._id;
      this.historyVisible = true;
    },
    // 打开任务选择弹窗
    openSelectTask(type) {
      this.selectTaskType = type;
      this.taskSelectVisible = true;
    },
    // 选择任务信息
    async selectTaskInfo(e) {
      try {
        const task = e.value[0];
        uni.showLoading({
          title: "数据处理中...",
          mask: true,
        });
        switch (this.selectTaskType) {
          case "sync": // 同步数据
            await this.syncLastRecord(task);
            break;
          case "history": // 查看历年数据
            this.queryPointFillHistory(task);
            break;
          default:
            break;
        }
      } catch (err) {
        uni.showToast({
          title: "同步数据出错!",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
        this.taskSelectVisible = false;
      }
    },
    // 同步数据
    async syncLastRecord(task) {
      if (this.requestLoad) return;
      this.requestLoad = true;
      try {
        if (this.selectPointList.length > 200) {
          uni.showToast({
            title: "单次至多只能填报200个点位，请重新选择",
            icon: "none",
          });
          return;
        }
        if (this.selectPointList.length == 0) {
          uni.showToast({
            title: "请选择需要填报的点位",
            icon: "none",
          });
          return;
        }
        // 此处判断不能同选
        let formType = "";
        let isError = false;
        let isCheck = false;
        this.selectPointList.forEach((item) => {
          console.log("核查的数据", item);
          if (item.地块状态 == "已核查") isCheck = true;
          if (!formType) {
            formType = item.地块类型;
          } else if (formType !== item.地块类型) {
            isError = true;
          }
        });
        if (isCheck) {
          uni.showToast({
            title: "已核查的数据无法同步",
            icon: "none",
          });
          return;
        }
        // 验证结果
        if (isError) {
          uni.showToast({
            title: "确权与未确权数据不能同时填报",
            icon: "none",
          });
          return;
        }
        const ids = this.selectPointList.map((item) => item._id);
        const params = {
          oldTaskId: task._id,
          newTaskId: this.currentTaskId,
          oldInfoIds: ids,
          plotType: formType == "确权地块" ? "QQDK" : "WQQDK",
        };
        await customRequest.synchroInfo(this.fcode, params);
        this.closeFillPopup();
        this.pointInfoShowClose();
        this.hideLoading();
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS, () => {
          uni.$emit("refresh-list");
          this.reloadData();
        });
      } catch (error) {
        this.showError(error || "同步失败，请稍后重试");
        console.log(error);
      } finally {
        this.requestLoad = false;
      }
    },
    // 确认筛选
    async confirmFilter(data) {
      if (!data) return;
      data.forEach((item) => {
        this[item.key] = item.value ? item.value : null;
      });

      if (this.mapZoom >= 15) {
        await this.reloadData();
      }
      this.filterVisible = false;
    },

    // 处理筛选选项数据
    async handleOptions() {
      try {
        const params = {
          page: { pageSize: 1000, pageNum: 1 },
          outFields: ["value", "label"],
        };
        const data = await this.$apis.formData.getFormRecords("种植作物", params);
        let options = [];
        if (this.lastTaskId) {
          options = [
            {
              title: "上半年种植作物",
              key: "lastCrop",
              multiple: false,
              options: data.list,
            },
            {
              title: "下半年种植作物",
              key: "nowCrop",
              multiple: false,
              options: data.list,
            },
          ];
        } else {
          options.push({
            title: "种植作物",
            key: "nowCrop",
            multiple: false,
            options: data.list,
          });
        }
        this.filterOptions.push(...options);
      } catch (error) {
        console.log(error);
      }
    },
    formatText(info) {
      let text = "";
      if (this.currentPointRecord.地块类型 == "确权地块") {
        text = info.cropType == "其它" ? info.remark : info.cropType;
      } else if (info.cropTypeList) text = info.cropTypeList.join(",");
      else text = info.cropType;
      return text;
    },

    navigateBack() {
      uni.navigateBack();
    },
    // 搜索模块
    closeSearch() {
      this.searchOverlay = false;
      this.searchList = [];
    },
    searchItemClick(data) {
      this.searchValue = data.地块编码;
      this.searchOverlay = false;
      this.searchList = [];
      this.flyToMap(data.中心经度, data.中心纬度, 18);
      const interval = setInterval(() => {
        if (this.pointLoaded) {
          this.$refs.map.pointClick(data);
          clearInterval(interval);
        }
      }, 200);
    },
    //  行政区划切换
    confirDistrictSelct(e) {
      // 判断编码并且查找镇级中心经纬度并且移至
      if (e.data.value.length >= 9) {
        this.queryLngLatFly(e.data.value.substring(0, 9));
      }
      this.areaValue = e.data.label;
      const level = e.config.valuekey;
      if (level == "tcode" || level == "vcode") {
        this.areaFilter = {
          level,
          code: e.data.value,
        };
      } else {
        this.areaFilter = null;
      }
      this.districtSelectShow = false;
      this.$refs.map.resetPointList();
      this.reloadData();
    },
    async queryLngLatFly(code) {
      try {
        const filter = ["=", "tcode", code];
        const res = await this.$apis.formData.getFormRecords("admin_town", {
          filter,
        });
        const info = res.list[0];
        const min = this.$utils.proj.wgs84togcj02(info.x_min, info.y_min);
        const max = this.$utils.proj.wgs84togcj02(info.x_max, info.y_max);
        if (info.质心_x && info.质心_y) this.$refs.map.getLocation([min, max]);
      } catch (error) {
        console.error("error", error);
      }
    },
    getPointCode(data) {
      uni.setClipboardData({
        data,
        success() {
          uni.showToast({
            title: "复制成功!",
            icon: "success",
          });
        },
      });
    },
    flyToMap(lng, lat, zoom = 16) {
      const lnglat = this.$utils.proj.wgs84togcj02(lng, lat);
      this.$refs.map.getLocation({
        longitude: lnglat[0],
        latitude: lnglat[1],
        zoom,
      });
    },
    navigationMap() {
      const { 中心经度, 中心纬度, 地块编码 } = this.currentPointRecord;
      const lnglat = this.$utils.proj.wgs84togcj02(中心经度, 中心纬度);
      Navigator.navigateTo(lnglat[0], lnglat[1], `地块编码${地块编码}`);
    },
    // 地址拼接
    addressText(item) {
      return `${item.fname}${item.tname}${item.vname}`;
    },
    pointInfoShowClose() {
      this.pointInfoShow = false;
      this.closeSelectPointIdList();
    },
    // 查询当前点的填报记录数据
    async queryPointFillHistory(task) {
      uni.showLoading({
        title: "请求中...",
      });
      const info = this.currentPointRecord;
      try {
        const params = {
          plotCodes: [info.地块编码],
          taskId: task._id,
          plotType: info.地块类型 === "确权地块" ? "QQDK" : "WQQDK",
        };
        const res = await customRequest.getPlotRecord(this.fcode, params);
        const data = res[0];
        if (!data.verifyTime) {
          uni.showToast({
            title: "该年份无填报数据",
            icon: "none",
          });
          return;
        }

        // 赋值相应的表名称
        this.formInputName = info.地块类型 == "确权地块" ? "地块核查记录表" : "未确权地块核查记录表";
        const fields = this.formName == "地块核查记录表" ? CERTAIN_FIELDS : UNCERTAIN_FIELDS;
        const keys = FIELD_KEY;
        const newObj = {};

        Object.keys(keys).forEach((key) => {
          const cnkey = keys[key];
          if (fields.includes(cnkey) && data[key]) {
            newObj[cnkey] = data[key];
          }
        });
        if (data.recordId) newObj._id = data.recordId;
        newObj.任务_id = this.currentTaskId;
        this.formRecordData = newObj;
        this.btnState = "read";
        this.pointInfoShowClose();
        this.showFillFormPopup = true;
      } catch (err) {
        console.log(err);
      } finally {
        uni.hideLoading();
      }
    },

    // 点击点
    async pointClick(info) {
      if (this.pointInfoLoading) return;
      this.pointInfoLoading = true;
      if (!this.currentTaskId) return;
      try {
        if (this.selectPointList.length > 1) {
          this.pointInfoShow = false;
          return;
        }
        if (this.selectPointList.length === 0) {
          this.pointInfoShow = false;
          return;
        }
        const params = {
          plotCodes: [info.地块编码],
          taskId: this.currentTaskId,
          plotType: info.地块类型 === "确权地块" ? "QQDK" : "WQQDK",
        };
        const data = await customRequest.getPlotRecord(this.fcode, params);
        const record = { ...info, ...data[0] };
        if (this.lastTaskId) {
          params.taskId = this.lastTaskId;
          const lastData = await customRequest.getPlotRecord(this.fcode, params);
          if (lastData.length > 0 && lastData[0].recordId) record.lastData = lastData[0];
        }
        this.currentPointRecord = record;
        this.pointInfoShow = true;
      } catch (error) {
        console.error("error", error);
      } finally {
        this.pointInfoLoading = false;
      }
    },
    async getSelectPoint(selectPointList) {
      this.selectPointList = selectPointList;
      this.plotCreateInfo = await this.isCreated(selectPointList);
    },
    // 判断点位是否为我创建
    async isCreated(data) {
      let formType = "";
      let isError = false;
      const result = {
        not: false,
        code: "",
      };
      data.forEach((item) => {
        if (!formType) {
          formType = item.地块类型;
        } else if (formType !== item.地块类型) {
          isError = true;
          result.not = true;
        }
      });
      if (isError) return result;
      const id = data.map((item) => item._id);
      const filter = ["and", ["in", "__main_form_record_uid_field__", id], ["=", "任务_id", this.currentTaskId]];
      if (id.length === 0) return false;
      const formName = formType === "确权地块" ? "地块核查记录表" : "未确权地块核查记录表";
      try {
        const { list: ary } = await customRequest.getFormRecords(this.fcode, formName, { filter });
        ary.forEach((item) => {
          if (this.userInfo.user_id !== item.create_by) {
            result.not = true;
            const { 地块编码 } = data.find((items) => items._id === item.__main_form_record_uid_field__);
            result.code = 地块编码;
          }
        });
        return result;
      } catch (error) {
        console.error("error", error);
        return result;
      }
    },
    //  获取任务
    async getCurrentTask() {
      const { list: taskData } = await this.$apis.formData.getFormRecords(TASK_FORM_NAME);
      if (taskData.length !== 0) {
        const currentTaskInfo = taskData.find((item) => item.任务状态 === "已启动");
        this.currentTaskId = currentTaskInfo._id;
        let lastTask = null;
        if (currentTaskInfo.时间段 == "下半年") lastTask = taskData.find((item) => item.年份 == currentTaskInfo.年份 && item.时间段 == "上半年");

        this.taskList = taskData
          .map((item) => {
            item.task_name = `${item.年份}${item.时间段}${item.任务名称}`;
            return item;
          })
          .filter((item) => item.任务状态 !== "已启动");

        if (lastTask) this.lastTaskId = lastTask._id;
      } else {
        this.currentTaskId = null;
        this.lastTaskId = null;
      }
    },
    async reloadData() {
      this.page = {
        pageNum: 1,
        pageSize: 1000,
      };
      this.list = [];
      await this.getSearchFormData();
    },
    zoomChange(e) {
      this.mapZoom = e;
    },
    refreshPoint({ bounds }) {
      const max = this.$utils.proj.gcj02towgs84(bounds._ne.lng, bounds._ne.lat);
      const min = this.$utils.proj.gcj02towgs84(bounds._sw.lng, bounds._sw.lat);
      this.boundsInfo = {
        x_max: Number(max[0]),
        y_max: Number(max[1]),
        x_min: Number(min[0]),
        y_min: Number(min[1]),
      };
      this.reloadData();
    },
    //  获取过滤条件
    getParams() {
      const filter = {
        taskId: this.currentTaskId,
      };
      if (this.areaFilter) filter[this.areaFilter.level] = this.areaFilter.code;
      if (this.typeFilter) filter.landType = this.typeFilter;
      if (this.stateFilter) filter.verify = this.stateFilter === "已核查";

      if (this.boundsInfo) {
        filter.lonMax = this.boundsInfo.x_max;
        filter.lonMin = this.boundsInfo.x_min;
        filter.latMax = this.boundsInfo.y_max;
        filter.latMin = this.boundsInfo.y_min;
      }
      return filter;
    },
    async searchPointByCode(searchValue) {
      try {
        if (!this.currentTaskId) {
          this.searchOverlay = true;
          this.searchList = [];
          this.searchTotal = 0;
          return;
        }
        if (!searchValue) {
          uni.showToast({
            title: "请输入地块编码!",
            icon: "none",
          });
          return;
        }
        const filter = {
          taskId: this.currentTaskId,
        };
        if (this.areaFilter) filter[this.areaFilter.level] = this.areaFilter.code;
        if (this.typeFilter) filter.landType = this.typeFilter;
        if (this.stateFilter) filter.verify = this.stateFilter === "已核查";
        if (searchValue) filter.plotCode = searchValue;
        filter.taskId = this.currentTaskId;
        const list = await customRequest.getPlotInRange(this.fcode, filter);
        this.searchOverlay = true;
        this.searchList = list;
        this.searchTotal = list.length;
      } catch (error) {
        this.showError(error || "查询失败，请稍后重试");
      }
    },
    async getSearchFormData() {
      if (!this.currentTaskId || !this.boundsInfo) return;
      try {
        const params = this.getParams();
        // 判断是否筛选了上半年作物
        let lastData = [];
        let filterIds = null;
        if (this.lastCrop) {
          const lastParams = this.getParams();
          lastParams.taskId = this.lastTaskId;
          lastParams.verify = true;
          lastParams.plantCrop = this.lastCrop;
          lastData = await customRequest.getPlotInRange(this.fcode, lastParams);
          filterIds = lastData.map((item) => item._id);
        }
        // 查询现任务地块点
        if (this.nowCrop) {
          params.verify = true;
          params.plantCrop = this.nowCrop;
        }
        let data = await customRequest.getPlotInRange(this.fcode, params);
        if (filterIds) {
          data = data.filter((item) => {
            return filterIds.includes(item._id);
          });
        }

        this.list = data;
      } catch (error) {
        this.showError(error || "获取数据失败，请稍后重试");
        console.error("error", error);
      } finally {
        this.pointList = this.list.map((item) => {
          const lnglat = this.$utils.proj.wgs84togcj02(item.中心经度, item.中心纬度);
          item.longitude = lnglat[0];
          item.latitude = lnglat[1];
          return item;
        });
        this.hideLoading();
      }
    },

    locationChangeHandler(e) {
      this.$refs.map.locationChangeHandler(e);
    },
    //  取消框选点位
    closeSelectPointIdList() {
      this.$refs.map.resetPointList();
      this.selectPointList = [];
    },
    //  填报弹窗
    showFillPopup(type) {
      if (this.selectPointList.length > 200) {
        uni.showToast({
          title: "单次至多只能填报200个点位，请重新选择",
          icon: "none",
        });
        return;
      }
      if (type === "multiFill" && this.selectPointList.length == 0) {
        uni.showToast({
          title: "请选择需要填报的点位",
          icon: "none",
        });
        return;
      }
      // 此处判断不能同选
      let formType = "";
      let isError = false;
      this.selectPointList.forEach((item) => {
        if (!formType) {
          formType = item.地块类型;
        } else if (formType !== item.地块类型) {
          isError = true;
        }
      });
      // 验证结果
      if (isError) {
        uni.showToast({
          title: "确权与未确权数据不能同时填报",
          icon: "none",
        });
        return;
      }
      if (this.plotCreateInfo.not && type != "read") {
        uni.showToast({
          title: `无法填报他人地块，地块编码为：${this.plotCreateInfo.code}`,
          icon: "none",
        });
        return;
      }
      // 赋值相应的表名称
      this.formInputName = formType == "确权地块" ? "地块核查记录表" : "未确权地块核查记录表";

      this.pointInfoShow = false;
      this.btnState = type;
      const data = this.currentPointRecord;
      //  编辑
      if (type === "edit" || type === "read") {
        const fields = this.formName == "地块核查记录表" ? CERTAIN_FIELDS : UNCERTAIN_FIELDS;
        const keys = FIELD_KEY;
        const newObj = {};

        Object.keys(keys).forEach((key) => {
          const cnkey = keys[key];
          if (fields.includes(cnkey) && data[key]) {
            newObj[cnkey] = data[key];
          }
        });
        if (data.recordId) newObj._id = data.recordId;
        newObj.任务_id = this.currentTaskId;
        this.formRecordData = newObj;
      }
      this.showFillFormPopup = true;
    },
    //  处理获取到的时间戳
    formatDate(date) {
      return dayjs(date).format("YYYY-MM-DD");
    },
    //  提交表单数据
    async submitOperateHandler(data) {
      try {
        // await this.submitValidator();
        switch (this.btnState) {
          case "edit":
            this.editFormRecord(data);
            break;
          case "fill":
            this.addFormData(data);
            break;
          case "multiFill":
            this.batchFillFormData(data);
            break;
          default:
            break;
        }
      } catch (err) {
        uni.showToast({
          title: "提交失败",
          icon: "error",
        });
      }
    },

    // submitValidator() {
    //   return new Promise((resolve, reject) => {});
    // },

    //  关闭填报弹窗
    closeFillPopup() {
      this.formRecordData = null;
      this.showFillFormPopup = false;
      this.closeSelectPointIdList();
    },
    // 数据删除
    removeVerifyData() {
      const formName = this.currentPointRecord.地块类型 === "确权地块" ? "地块核查记录表" : "未确权地块核查记录表";
      try {
        uni.showModal({
          title: "删除提示",
          content: "是否要删除该条核查记录",
          success: async (res) => {
            if (res.confirm) {
              this.showLoading();
              await customRequest.deleteFormRecord(this.fcode, formName, this.currentPointRecord.recordId);
              this.hideLoading();
              this.pointInfoShow = false;
              this.closeSelectPointIdList();
              this.showSuccess(this.$constants.MSG.DELETE_SUCCESS, () => {
                uni.$emit("refresh-list");
                this.reloadData();
              });
            }
          },
        });
      } catch (e) {
        this.hideLoading();
        this.showError(this.$constants.MSG.DELETE_FAIL);
      }
      this.currentRecordData = undefined;
    },
    //  数据新增
    async addFormData(data) {
      const { _id } = this.currentPointRecord;
      const recordData = {
        __main_form_record_uid_field__: _id,
        ...data,
        任务_id: this.currentTaskId,
      };
      this.showLoading();
      try {
        await customRequest.addFormRecord(this.fcode, this.formInputName, this.$utils.form.toApiFormData(recordData));
        this.closeFillPopup();
        this.closeSelectPointIdList();
        this.hideLoading();
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS, () => {
          uni.$emit("refresh-list");
          this.reloadData();
        });
      } catch (error) {
        console.error("error", error);
        this.hideLoading();
        this.showError(error.msg || this.$constants.MSG.DATA_SUBMIT_FAIL);
      }
    },
    //  数据编辑
    async editFormRecord(data) {
      this.showLoading();
      try {
        await customRequest.updateFormRecord(this.fcode, this.formInputName, data._id, data);
        this.closeFillPopup();
        this.hideLoading();
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS, () => {
          uni.$emit("refresh-list");
          this.reloadData();
        });
      } catch (e) {
        this.hideLoading();
        this.showError(e.msg || this.$constants.MSG.DATA_SUBMIT_FAIL);
      }
    },

    //  批量填报
    async batchFillFormData(data) {
      const fillPointIdList = [];
      const editPointIdList = [];
      let fillPointDataList = [];
      this.selectPointList.forEach((pointItem) => {
        console.log("pointItem", pointItem);
        if (pointItem.地块状态 === "未核查") fillPointIdList.push(pointItem._id);
        else editPointIdList.push(pointItem._id);
        fillPointDataList = fillPointIdList.map((_id) => {
          return {
            __main_form_record_uid_field__: _id,
            ...data,
            任务_id: this.currentTaskId,
          };
        });
      });

      this.showLoading();
      try {
        //  批量添加
        if (fillPointIdList.length !== 0) {
          await customRequest.batchAddFormData(this.fcode, this.formInputName, fillPointDataList);
        }

        //  批量编辑
        if (editPointIdList.length !== 0) {
          await this.batchEditChangePointData(editPointIdList, data);
        }

        //  取消点位选取
        this.closeSelectPointIdList();
        this.closeFillPopup();
        this.hideLoading();
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS, () => {
          uni.$emit("refresh-list");
          this.reloadData();
        });
      } catch (e) {
        this.hideLoading();
        this.showError(e.msg || this.$constants.MSG.DATA_SUBMIT_FAIL);
      }
    },
    //  批量编辑修改地块字段
    async batchEditChangePointData(editPointIdList, data) {
      const list = await this.getPointInspectRecordId(editPointIdList);
      const dataIds = list.map((item) => item._id);
      customRequest.batchUpdateData(this.fcode, this.formInputName, {
        dataIds,
        data,
      });
    },
    async getPointInspectRecordId(editPointIdList) {
      const filter = ["and", ["in", "__main_form_record_uid_field__", editPointIdList], ["=", "任务_id", this.currentTaskId]];
      const { list } = await customRequest.getFormRecords(this.fcode, this.formInputName, {
        filter,
      });
      return list;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar--fixed {
  z-index: 100;
}

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .uicon-arrow-left {
  color: #fff !important;
}

/deep/ .u-checkbox {
  margin-bottom: 10rpx !important;
}

/deep/.u-form-item__body__left {
  width: 160rpx !important;
}

.area-text {
  color: #fff;
  display: flex;
  align-items: center;
}

.move-area {
  width: 100%;
  background-color: #fff;
  border-radius: 10px 10px 0 0;
  padding: 20rpx 0;
  box-sizing: border-box;
  overflow: hidden;
  position: fixed;
  bottom: 0;
  left: 0;
  transition: height 0.2s;
  z-index: 8;

  .move-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 100%;
    height: 36rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .bar {
      width: 14vw;
      height: 12rpx;
      border-radius: 68rpx;
      background-color: #333;
    }
  }

  .content-top {
    padding: 0 30rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 68rpx;
    border-bottom: 2rpx solid #f8f8f8;

    .icon {
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 150rpx;

      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }

  .list {
    transition: height 0.2s;
    position: relative;

    /deep/.u-checkbox-group--column {
      padding-bottom: 60rpx;
    }

    .card {
      box-sizing: border-box;
      display: flex;
      position: relative;
      padding: 20rpx 30rpx;
      border-bottom: 2rpx solid #f8f8f8;

      &-item {
        flex: 1;
        overflow: scroll;
        padding-right: 40rpx;
      }

      .right-icon {
        position: absolute;
        top: 50%;
        right: 10rpx;
      }

      .status {
        position: absolute;
        top: 30rpx;
        right: 80rpx;
        font-size: 24rpx;
      }

      .code {
        color: #333;
        font-size: 28rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
      }

      .info {
        width: 100%;
        display: grid;

        &-item {
          height: 70rpx;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-right: 20rpx;

          .label {
            color: #333;
            font-size: 24rpx;
            font-weight: bold;
            white-space: nowrap;
          }

          .value {
            color: #333;
            font-size: 24rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }

        .u-button {
          align-self: end;
          margin-left: auto;
          margin-right: 0;
        }
      }
    }
  }
}

.content {
  height: 100vh;
  overflow: hidden;
  position: relative;

  .check-all-container {
    height: 100rpx;
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    // padding: 0 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    position: fixed;
    z-index: 99;
    bottom: 50px;
    padding: 0 20rpx;

    .btn-list {
      width: 300rpx;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;

      /deep/.u-button--mini {
        width: 120rpx;
        height: 60rpx;
      }

      /deep/.u-button__text {
        font-size: 22rpx !important;
      }

      .close-btn {
        margin-right: 20rpx;
      }
    }

    .select-count {
      margin-right: 20rpx;
    }
  }

  .popup-c {
    width: 100%;
  }

  .point-container {
    padding: 30rpx;

    .point-code {
      font-weight: 700;
      margin-bottom: 30rpx;
    }

    .point-item-container {
      .point-item {
        margin-bottom: 30rpx;
      }

      .primary {
        color: $gt-primary-color;
      }
    }

    .btn-list {
      display: flex;
      align-items: center;
      justify-content: space-around;

      picker {
        width: 100%;
        margin-right: 20rpx;
      }

      /deep/ .u-button {
        margin-right: 20rpx;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.search-box {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  background-color: #fff;
  position: fixed;
  padding: 20rpx;
  box-sizing: border-box;
  z-index: 99;

  .filter {
    display: flex;
    align-items: center;
    margin-left: 30rpx;

    .icon {
      height: 20px;
    }

    .text {
      margin-left: 10rpx;
      text-align: center;
      font-size: 24rpx;
    }
  }

  .search-result {
    position: absolute;
    width: 100%;
    max-height: 30vh;
    top: 68rpx;
    left: 0;
    background-color: #fff;
    z-index: 99;
    box-shadow: 0 12rpx 12rpx rgba(0, 0, 0, 0.16);
    overflow-y: scroll;

    .block {
      width: 100%;
      padding: 20rpx 30rpx;
      border-bottom: 2rpx solid #f8f8f8;
    }
  }
}
</style>
