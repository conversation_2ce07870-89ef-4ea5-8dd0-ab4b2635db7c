<template>
  <view class="LogList-wrapper">
    <u-navbar
      title="监测信息"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      @leftClick="leftClickHandler"
    >
      <view
        slot="right"
        class="area-text"
        :style="{
          fontSize: regionLabel && regionLabel.length > 5 ? '24rpx' : '28rpx',
        }"
        @click="districtSelectShow = true"
        >切换[{{ regionLabel || "行政区划" }}]
      </view>
    </u-navbar>
    <view class="main">
      <scroll-view
        scroll-y
        class="scroll-content"
        @scrolltolower="scrollLowerHandler"
        :style="contentStyle"
      >
        <SelfDataList
          ref="selfDataList"
          :data="dataList"
          :fields="fields"
          :titleField="titleField"
          :operations="operations"
          @edit="editHandler"
          @navigate="navigateHandler"
          @view="viewHandler"
          @delete="deleteHandler"
          :isSelect="true"
          @select="selectHandler"
        />
        <view class="loading" v-if="dataList.length > 0">
          <u-loadmore :status="loadStatus" />
        </view>
      </scroll-view>
    </view>
    <AreaPicker
      :default="regionDefault"
      :mustSelect="mustSelect"
      class="areaPicker"
      :baseLevel="2"
      v-model="districtSelectShow"
      @confirm="confirDistrictSelct"
    ></AreaPicker>
    <u-modal
      class="delModal"
      :show="modalShow"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_DELETE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="deleteCancelHandler"
      @confirm="deleteConfirmHandler"
    />

    <view class="check-all-container" v-show="selectData.length > 0">
      <view class="select-count">已选择{{ selectData.length }}个地块</view>
      <view class="btn-list">
        <u-button
          :plain="true"
          type="info"
          size="mini"
          text="取消选择"
          @click="resetSelectPointList"
        >
        </u-button>
        <u-button
          type="error"
          size="mini"
          text="批量删除"
          @click="deleteBatchHandler"
        >
        </u-button>
        <u-button
          type="primary"
          size="mini"
          text="批量编辑"
          @click="editBatchHandler"
        >
        </u-button>
      </view>
    </view>

    <!-- 填报弹窗 -->
    <form-popup
      v-model="formInputVisible"
      :formName="formName"
      @submit="submit"
    ></form-popup>
  </view>
</template>

<script>
import customRequest from "@/pages_plowland/apis/custom";
import AreaPicker from "@/pages_plowland/components/AreaPicker/index.vue";
import SelfDataList from "@/pages_plowland/components/SelfDataList/index.vue";
import BaseDataList from "@/components/common/BaseDataList";
import Storage from "@/tools/Storage";
import FormPopup from "@/pages_plowland/components/FormPopup/index";

const DK_FORM = "地块属性表";

export default {
  name: "LogList",
  mixins: [BaseDataList],
  components: {
    SelfDataList,
    AreaPicker,
    FormPopup,
  },
  props: {
    info: {
      type: Object,
    },
  },
  computed: {
    contentStyle() {
      const system = uni.getSystemInfoSync();
      return {
        height: `${system.windowHeight - system.statusBarHeight - 44 - 50}px`,
      };
    },
  },
  created() {
    this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
    this.mustSelect = this.userInfo.role_code.includes("city_manage");
  },
  async mounted() {
    this.projectId = this.info.id;
    if (this.info.readonly) {
      this.operations = [
        {
          key: "navigate",
          label: "定位",
        },
        {
          key: "view",
          label: "查看",
        },
      ];
    }
    await this.init();
    uni.$on("refresh-project", this.refreshHandler);
  },
  data() {
    return {
      formName: "地块监测信息表",
      regionLabel: "",
      regionFilter: null,
      districtSelectShow: false,
      dataList: [],
      fields: [],
      titleField: undefined,
      operations: [
        {
          key: "view",
          label: "查看",
        },
        {
          key: "edit",
          label: "编辑",
        },
        {
          key: "navigate",
          label: "定位",
        },
        {
          key: "delete",
          label: "删除",
          type: "error",
        },
      ],
      page: {
        pageNum: 1,
        pageSize: 10,
      },
      loadStatus: "loadmore",
      dataLoading: false,
      modalShow: false,
      delItemId: undefined,
      userInfo: undefined,
      regionDefault: null,
      mustSelect: false,

      selectData: [],
      formInputVisible: false,
    };
  },
  methods: {
    async init() {
      const res = await this.$apis.formDef.getFormDef(this.formName);
      this.fields = res.listFields;
      this.titleField = res.titleField;
    },
    showHandler() {
      this.reloadData();
    },
    leftClickHandler() {
      uni.navigateBack({ delta: 1 });
    },
    confirDistrictSelct(e) {
      const { data, config } = e;
      if (this.regionFilter && this.regionFilter.value === data.value) return;
      this.regionFilter = {
        name: config.valuekey,
        value: data.value,
      };
      this.regionLabel = data.label;
      this.districtSelectShow = false;
      this.reloadData();
    },
    async getList() {
      try {
        const joins = [
          {
            type: "left",
            table: DK_FORM,
            joinedTable: this.formName,
            multiJoinFields: [
              {
                field: "地块编码",
                joinedField: "地块编码",
              },
            ],
            outFields: ["中心经度", "中心纬度", "_id"],
          },
        ];
        let filter = [];
        filter.push(["=", "项目_id", this.projectId]);
        // 区县过滤
        if (this.regionFilter && this.regionFilter.name !== "fcode") {
          const { name, value } = this.regionFilter;
          joins[0].filter = ["=", name, value];
        }
        if (filter.length > 1) {
          filter.unshift("and");
        } else {
          filter = filter.flat(1);
        }
        const res = await customRequest.joinSearchFormData(
          this.regionFilter.value.slice(0, 6),
          this.formName,
          {
            joins,
            filter,
            page: this.page,
          }
        );
        return res.list.map((i) => {
          const obj = {};
          for (const key in i) {
            if (Object.prototype.hasOwnProperty.call(i, key)) {
              const formatKey = key.split(".")[1];
              if (key.endsWith("_id")) obj.地块_id = i[key];
              if (key.startsWith(DK_FORM) && key.endsWith("_id")) continue;
              obj[formatKey] = i[key];
            }
          }
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },
    async reloadData() {
      this.page.pageNum = 1;
      this.dataLoading = true;
      try {
        const list = await this.getList();
        this.dataList = list;
        console.log(list.length, this.page.pageSize);
        if (list && list.length < this.page.pageSize)
          this.loadStatus = "nomore";
        if (list.length === this.page.pageSize) this.loadStatus = "loadmore";
        console.log(this.loadStatus);
      } catch (error) {
        console.log("加载数据出现问题", error);
      } finally {
        this.dataLoading = false;
      }
    },
    async loadMore() {
      this.loadStatus = "loading";
      this.dataLoading = true;
      try {
        const list = await this.getList();
        this.dataList.push(...list);
        if (list && list.length < this.page.pageSize)
          this.loadStatus = "nomore";
        if (list.length === this.page.pageSize) this.loadStatus = "loadmore";
      } catch (error) {
        console.log("加载数据发生错误：", error);
      } finally {
        this.dataLoading = false;
      }
    },
    scrollLowerHandler() {
      console.log(this.loadStatus, this.dataLoading);
      if (this.loadStatus === "nomore" || this.dataLoading) return;
      this.page.pageNum += 1;
      this.loadMore();
    },
    async editHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      try {
        const res = await customRequest.getFormRecord(
          this.regionFilter.value.slice(0, 6),
          this.formName,
          formRecordUid,
          {
            resolveDic: false,
            resolveSubItems: true,
          }
        );
        Storage.saveFormData(formRecordUid, res);
        const pageUrl = `/pages_plowland/pages/SelfFormPage/index?pcode=${this.regionFilter.value.slice(
          0,
          6
        )}&formUid=${
          this.formName
        }&formRecordUid=${formRecordUid}&cacheable=false`;
        uni.navigateTo({ url: pageUrl });
      } catch (error) {
        this.showError(error);
      }
    },
    async viewHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      const res = await customRequest.getFormRecord(
        this.regionFilter.value.slice(0, 6),
        this.formName,
        formRecordUid,
        {
          resolveDic: false,
          resolveSubItems: true,
        }
      );
      Storage.saveFormData(formRecordUid, res);
      const pageUrl = `${this.$constants.PAGE.VIEW_PAGE_URL}?formUid=${this.formName}&formRecordUid=${formRecordUid}`;
      uni.navigateTo({ url: pageUrl });
    },
    navigateHandler(data) {
      // let lon = data[POINT_LON_FIELD];
      // let lat = data[POINT_LAT_FIELD];
      // if (isNaN(lon) || isNaN(lat)) return;
      // Navigation.navigateTo(lon, lat);
      uni.$emit("changeTab", 0);
      uni.$emit("project-fly", data);
    },
    deleteHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      this.delItemId = formRecordUid;
      this.modalShow = true;
    },
    async deleteConfirmHandler() {
      try {
        this.showLoading(this.$constants.MSG.DELETING);
        if (Array.isArray(this.delItemId)) {
          await this.$apis.formData.batchDeleteFormData(
            `${this.formName}_${this.regionFilter.value.slice(0, 6)}`,
            this.delItemId
          );
          this.resetSelectPointList();
        } else {
          await customRequest.deleteFormRecord(
            this.regionFilter.value.slice(0, 6),
            this.formName,
            this.delItemId
          );
        }

        await this.reloadData();
        this.showSuccess(this.$constants.MSG.DELETE_SUCCESS);
      } catch (error) {
        this.showError(this.$constants.MSG.DELETE_FAIL);
      } finally {
        this.hideLoading();
      }
      this.delItemId = undefined;
      this.modalShow = false;
    },
    deleteCancelHandler() {
      this.delItemId = undefined;
      this.modalShow = false;
    },
    refreshHandler(formName) {
      if (this.formName === formName) {
        this.reloadData();
      }
    },

    resetSelectPointList() {
      this.$refs.selfDataList.resetPointList();
      this.selectData = [];
    },
    selectHandler(data) {
      this.selectData = data;
    },
    // 校验选中的数据是否可以进行编辑或删除
    checkSelectedDataPermission() {
      const selectDataSet = new Set(this.selectData);
      const selectedItems = this.dataList.filter((item) =>
        selectDataSet.has(item._id)
      );
      const { user_id } = JSON.parse(uni.getStorageSync("userInfo"));
      if (selectedItems.some((item) => item.update_by !== user_id)) {
        this.showError("只能操作该账号填报的数据！");
        return false;
      }
      return true;
    },
    deleteBatchHandler() {
      if (!this.checkSelectedDataPermission()) {
        return;
      }
      this.delItemId = this.selectData;
      this.modalShow = true;
    },

    editBatchHandler() {
      if (!this.checkSelectedDataPermission()) {
        return;
      }
      this.formInputVisible = true;
    },
    // 批量编辑提交
    async submit(data) {
      try {
        const { fcode } = JSON.parse(uni.getStorageSync("userInfo"));
        await customRequest.batchUpdateData(fcode, this.formName, {
          dataIds: this.selectData,
          data,
        });
        await this.reloadData();
        this.resetSelectPointList();
        this.showSuccess("批量编辑成功!");
        this.formInputVisible = false;
      } catch (error) {
        this.showError(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .u-icon__icon.uicon-arrow-left {
  color: #fff !important;
}

.area-text {
  color: #fff;
  display: flex;
  align-items: center;
}

.LogList-wrapper {
  display: flex;
  flex-direction: column;

  .main {
    background-color: #f8f8f8;
    padding-bottom: 50px;
    padding: 0 20rpx;

    .scroll-content {
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
    }

    .loading {
      padding-bottom: 40rpx;
    }
  }
}

.areaPicker {
  position: absolute;
}

.delModal {
  position: absolute;
}

.check-all-container {
  // height: 100rpx;
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  // padding: 0 20rpx;
  font-size: 26rpx;
  position: fixed;
  z-index: 99;
  bottom: 50px;
  padding: 30rpx;

  .btn-list {
    margin-top: 20rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    /deep/.u-button--mini {
      width: 140rpx;
      height: 68rpx;
      margin: 0 20rpx;
    }
  }

  .select-count {
    margin-right: 20rpx;
  }
}
</style>
