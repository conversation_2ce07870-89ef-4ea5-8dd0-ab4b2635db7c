<template>
  <view class="content" v-if="info">
    <u-navbar title="施工地图" :bgColor="$constants.COLOR.PRIMARY_COLOR" :placeholder="true" @leftClick="navigateBack">
      <view slot="right" class="area-text" :style="{ fontSize: areaValue.length > 5 ? '24rpx' : '28rpx' }" @click="areaSelectVisible = true"
        >切换[{{ areaValue }}]</view
      >
    </u-navbar>
    <view :style="contentStyle">
      <view class="search-box">
        <u-search placeholder="请输入地块编码" bgColor="#fff" v-model="searchValue" @custom="searchPointCode" @search="searchPointCode"></u-search>
      </view>
      <Map
        ref="map"
        :point="pointList"
        :multipleSelect="!info.readonly"
        :config="mapConfig"
        @point-click="pointClick"
        @getSelectPoint="getSelectPoint"
        @moveEnd="refreshPoint"
        @mapLoaded="mapLoaded = true"
        @pointLoaded="pointLoaded = true"
        @pointLoading="pointLoaded = false"
        @zoomChange="zoomChange"
      ></Map>
      <view class="check-all-container" v-show="selectPointList.length > 1">
        <view class="select-count">已选择{{ selectPointList.length }}个地块</view>
        <view class="btn-list">
          <u-button :plain="true" type="info" size="mini" text="取消选择" @click="resetSelectPointList"> </u-button>
          <u-button type="primary" size="mini" text="监测信息" @click="showFillPop('jcxx')"> </u-button>
          <u-button type="primary" size="mini" text="施工日志" @click="showFillPop('sgrz')"> </u-button>
        </view>
      </view>
    </view>

    <AreaPicker v-model="areaSelectVisible" :mustSelect="mustSelect" @confirm="confirDistrictSelct"></AreaPicker>

    <!-- 点位详情弹窗 -->
    <u-popup :show="pointInfoVisible" @close="pointInfoClose" closeable :overlay="false">
      <view class="point-container" v-if="selectPointInfo">
        <view class="point-code" @click="copyPointCode(selectPointInfo.地块编码)">地块编码：{{ selectPointInfo.地块编码 }} </view>
        <view class="point-item-container">
          <view class="point-item">施工日志：{{ selectPointInfo.施工日志 ? "已填报" : "未填报" }}</view>
          <view class="point-item">监测信息：{{ selectPointInfo.监测信息 ? "已填报" : "未填报" }}</view>
        </view>
        <view class="btn-list">
          <u-button type="primary" :plain="true" text="导航" @click="navigationMap()"></u-button>
          <template v-if="!info.readonly">
            <u-button type="primary" text="监测信息" @click="showFillPop('jcxx')"></u-button>
            <u-button type="primary" text="施工日志" @click="showFillPop('sgrz')"></u-button>
          </template>
        </view>
      </view>
    </u-popup>
    <!-- 填报弹窗 -->
    <form-popup v-model="formInputVisible" :formName="formInputName" @submit="submit"></form-popup>
  </view>
</template>
<script>
import customRequest from "@/pages_plowland/apis/custom";
import BasePage from "@/pages/BasePage";
import Map from "@/pages_plowland/components/Map";
import AreaPicker from "@/pages_plowland/components/AreaPicker/index";
import FormPopup from "@/pages_plowland/components/FormPopup/index";
import Navigator from "@/tools/Navigation.js";
import { MAP_CONFIG } from "./config";

export default {
  mixins: [BasePage],
  props: {
    info: {
      type: Object,
    },
  },
  components: {
    Map,
    AreaPicker,
    FormPopup,
  },
  data() {
    return {
      projectId: null,
      userInfo: {},
      // 搜索
      searchValue: "",
      // 地图相关
      list: [],
      pointList: [],
      selectPointList: [],
      pointLoaded: false,
      mapLoaded: false,
      mapZoom: 0,
      boundsInfo: null,
      fcode: null,
      // 行政区
      areaSelectVisible: false,
      areaValue: "",
      // 表单
      formInputVisible: false,
      formRecordData: null,
      formInputName: "",
      // 页面信息数据
      selectPointInfo: null,
      pointInfoVisible: false,
      mustSelect: false,
    };
  },
  watch: {
    fcode() {
      this.searchValue = null;
      this.getInit();
    },
  },
  computed: {
    mapConfig() {
      if (!this.userInfo || !this.fcode) return;
      const result = {
        layers: MAP_CONFIG.layers,
        point: MAP_CONFIG.point,
        sources: MAP_CONFIG.sources(this.userInfo.ccode, this.fcode),
      };
      return result;
    },
    contentStyle() {
      const system = uni.getSystemInfoSync();
      return {
        position: "fixed",
        width: "100vw",
        zIndex: 1,
        top: `${system.statusBarHeight + 88}px`,
        bottom: `${system.safeAreaInsets.bottom + 50}px`,
      };
    },
  },
  async mounted() {
    console.log(this.info);
    this.projectId = this.info.id;
    this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
    this.mustSelect = this.userInfo.role_code.includes("city_manage");
    uni.$off("project-fly");
    uni.$on("project-fly", (data) => {
      console.log("定位的数据", data);
      if (this.mapLoaded) {
        this.flyToMap(data.中心经度, data.中心纬度, 18);
      }
      const interval = setInterval(() => {
        if (this.pointLoaded) {
          const pointData = { ...data };
          if (pointData.地块_id) pointData._id = pointData.地块_id;
          this.$refs.map.pointClick(pointData);
          clearInterval(interval);
        }
      }, 200);
    });
  },

  methods: {
    // 初始化点位位置
    async getInit() {
      try {
        await this.getSearchFormData();
        if (this.pointList.length == 0) return;
        const data = this.pointList[0];
        if (this.mapLoaded) {
          setTimeout(() => {
            this.flyToMap(data.中心经度, data.中心纬度, 18);
          }, 1000);
        }
      } catch (error) {
        console.log(error);
      }
    },
    async searchPointCode() {
      const filter = this.getParams();
      if (this.searchValue) filter.plotCode = this.searchValue;
      const result = await customRequest.getRecordPlotInRange(this.fcode, filter);
      if (result.length == 0) return;
      uni.$emit("project-fly", result[0]);
    },
    //  取消框选点位
    resetSelectPointList() {
      this.$refs.map.resetPointList();
      this.selectPointList = [];
    },
    // 刷新数据
    reloadData() {
      this.resetSelectPointList();
      this.getSearchFormData();
    },
    async submit(data) {
      try {
        const dataList = [];

        this.selectPointList.forEach((item) => {
          const obj = {
            ...data,
            项目_id: this.projectId,
            地块编码: item.地块编码,
          };
          dataList.push(obj);
        });
        console.log(dataList);
        await customRequest.batchAddFormData(this.fcode, this.formInputName, dataList);
        this.formInputVisible = false;
        this.pointInfoClose();
        this.showSuccess("填报成功");
        this.reloadData();
        setTimeout(() => {
          uni.$emit("refresh-project", this.formInputName);
        }, 1500);
      } catch (error) {
        console.log(error);
        this.showError();
      }
    },
    copyPointCode(data) {
      uni.setClipboardData({
        data,
        success() {
          uni.showToast({
            title: "复制成功!",
            icon: "success",
          });
        },
      });
    },
    pointInfoClose() {
      this.pointInfoVisible = false;
      this.initPointInfo();
    },
    initPointInfo() {
      this.selectPointInfo = null;
      this.$refs.map.resetPointList();
      this.selectPointList = [];
    },
    navigateBack() {
      uni.navigateBack();
    },
    //  行政区划切换
    confirDistrictSelct(e) {
      // 判断编码并且查找镇级中心经纬度并且移至
      if (e.data.value.length >= 9) {
        this.queryLngLatFly(e.data.value.substring(0, 9));
      }
      this.areaValue = e.data.label;
      const level = e.config.valuekey;
      if (level === "fcode") this.fcode = e.data.value;
      if (level === "tcode" || level === "vcode") {
        this.areaFilter = {
          level,
          code: e.data.value,
        };
        this.fcode = e.data.value.slice(0, 6);
      } else {
        this.areaFilter = null;
      }
      this.areaSelectVisible = false;
      this.$refs.map.resetPointList();
    },
    async queryLngLatFly(code) {
      try {
        const filter = ["=", "tcode", code];
        const res = await this.$apis.formData.getFormRecords("admin_town", {
          filter,
        });
        const info = res.list[0];
        const min = this.$utils.proj.wgs84togcj02(info.x_min, info.y_min);
        const max = this.$utils.proj.wgs84togcj02(info.x_max, info.y_max);
        if (info.质心_x && info.质心_y) this.$refs.map.getLocation([min, max]);
      } catch (error) {
        console.log(error);
      }
    },
    flyToMap(lng, lat, zoom = 16) {
      const lnglat = this.$utils.proj.wgs84togcj02(lng, lat);
      this.$refs.map.getLocation({
        longitude: lnglat[0],
        latitude: lnglat[1],
        zoom,
      });
    },
    navigationMap(e) {
      const { 中心经度, 中心纬度, 地块编码 } = this.selectPointInfo;
      const lnglat = this.$utils.proj.wgs84togcj02(中心经度, 中心纬度);
      Navigator.navigateTo(lnglat[0], lnglat[1], `地块编码${地块编码}`);
    },
    showFillPop(type) {
      switch (type) {
        case "jcxx":
          this.formInputName = "地块监测信息表";
          break;
        case "sgrz":
          this.formInputName = "施工日志表";
          break;
		  default:
			break;
      }
      this.formInputVisible = true;
      this.pointInfoVisible = false;
    },
    // 点击点
    async pointClick(info) {
      console.log("点数据", info);
      try {
        if (this.selectPointList.length > 1) {
          this.pointInfoVisible = false;
          return;
        }
        const params = {
          page: { pageSize: 1, pageNum: 1 },
          filter: ["and", ["=", "地块编码", info.地块编码], ["=", "项目_id", this.projectId]],
        };
        const ary = await customRequest.getFormRecords(this.fcode, "施工日志表", params);
        const ary1 = await customRequest.getFormRecords(this.fcode, "地块监测信息表", params);
        console.log(ary, ary1);
        const updatedInfo = { ...info };
        updatedInfo.施工日志 = ary.total > 0;
        updatedInfo.监测信息 = ary1.total > 0;
        this.selectPointInfo = updatedInfo;
        console.log("this.selectPointInfo", this.selectPointInfo);
        this.pointInfoVisible = true;
      } catch (error) {
        console.log(error);
      }
    },
    async getSelectPoint(list) {
      console.log("选中的点列表", list);
      this.selectPointList = list;
    },
    zoomChange(e) {
      this.mapZoom = e;
    },
    refreshPoint({ bounds }) {
      const max = this.$utils.proj.gcj02towgs84(bounds._ne.lng, bounds._ne.lat);
      const min = this.$utils.proj.gcj02towgs84(bounds._sw.lng, bounds._sw.lat);
      this.boundsInfo = {
        x_max: Number(max[0]),
        y_max: Number(max[1]),
        x_min: Number(min[0]),
        y_min: Number(min[1]),
      };
      this.getSearchFormData();
    },
    //  获取过滤条件
    getParams() {
      const filter = {
        projectId: this.projectId,
      };
      if (this.areaFilter) filter[this.areaFilter.level] = this.areaFilter.code;
      if (this.boundsInfo) {
        filter.lonMax = this.boundsInfo.x_max;
        filter.lonMin = this.boundsInfo.x_min;
        filter.latMax = this.boundsInfo.y_max;
        filter.latMin = this.boundsInfo.y_min;
      }
      return filter;
    },
    async getSearchFormData() {
      const list = [];
      try {
        const filter = this.getParams();
        console.log("请求参数", filter, this.fcode);
        const data = await customRequest.getRecordPlotInRange(this.fcode, filter);
        data.forEach((item) => {
          list.push(item);
        });
      } catch (error) {
        console.log(error);
      } finally {
        this.pointList = list.map((item) => {
          const lnglat = this.$utils.proj.wgs84togcj02(item.中心经度, item.中心纬度);
          return {
            ...item,
            longitude: lnglat[0],
            latitude: lnglat[1]
          };
        });
        this.hideLoading();
      }
    },

    locationChangeHandler(e) {
      this.$refs.map.locationChangeHandler(e);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .uicon-arrow-left {
  color: #fff !important;
}

/deep/ .u-checkbox {
  margin-bottom: 10rpx !important;
}

/deep/.u-form-item__body__left {
  width: 160rpx !important;
}

.area-text {
  color: #fff;
  display: flex;
  align-items: center;
}

.move-area {
  width: 100%;
  background-color: #fff;
  border-radius: 10px 10px 0 0;
  padding: 20rpx 0;
  box-sizing: border-box;
  overflow: hidden;
  position: fixed;
  bottom: 0;
  left: 0;
  transition: height 0.2s;
  z-index: 8;

  .move-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 100%;
    height: 36rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .bar {
      width: 14vw;
      height: 12rpx;
      border-radius: 68rpx;
      background-color: #333;
    }
  }

  .content-top {
    padding: 0 30rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 68rpx;
    border-bottom: 2rpx solid #f8f8f8;

    .icon {
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 150rpx;

      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }

  .list {
    transition: height 0.2s;
    position: relative;

    /deep/.u-checkbox-group--column {
      padding-bottom: 60rpx;
    }

    .card {
      box-sizing: border-box;
      display: flex;
      position: relative;
      padding: 20rpx 30rpx;
      border-bottom: 2rpx solid #f8f8f8;

      &-item {
        flex: 1;
        overflow: scroll;
        padding-right: 40rpx;
      }

      .right-icon {
        position: absolute;
        top: 50%;
        right: 10rpx;
      }

      .status {
        position: absolute;
        top: 30rpx;
        right: 80rpx;
        font-size: 24rpx;
      }

      .code {
        color: #333;
        font-size: 28rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
      }

      .info {
        width: 100%;
        display: grid;

        &-item {
          height: 70rpx;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-right: 20rpx;

          .label {
            color: #333;
            font-size: 24rpx;
            font-weight: bold;
            white-space: nowrap;
          }

          .value {
            color: #333;
            font-size: 24rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }

        .u-button {
          align-self: end;
          margin-left: auto;
          margin-right: 0;
        }
      }
    }
  }
}

.content {
  height: 100vh;
  overflow: hidden;
  position: relative;

  .check-all-container {
    // height: 100rpx;
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    // padding: 0 20rpx;
    font-size: 26rpx;
    position: fixed;
    z-index: 99;
    bottom: 50px;
    padding: 30rpx;

    .btn-list {
      margin-top: 20rpx;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      /deep/.u-button--mini {
        width: 140rpx;
        height: 68rpx;
        margin: 0 20rpx;
      }
    }

    .select-count {
      margin-right: 20rpx;
    }
  }

  .popup-c {
    width: 100%;
  }

  .point-container {
    padding: 30rpx;

    .point-code {
      font-weight: 700;
      margin-bottom: 30rpx;
    }

    .point-item-container {
      .point-item {
        margin-bottom: 30rpx;
      }
    }

    .btn-list {
      display: flex;
      align-items: center;
      justify-content: space-around;

      /deep/ .u-button {
        margin-right: 20rpx;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.search-box {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  background-color: #fff;
  position: absolute;
  top: -44px;
  left: 0;
  padding: 0 20rpx;
  box-sizing: border-box;
  z-index: 99;

  .filter {
    display: flex;
    align-items: center;

    .text {
      width: 100rpx;
      text-align: center;
      font-size: 28rpx;
    }
  }

  .search-result {
    position: absolute;
    width: 100%;
    max-height: 30vh;
    top: 68rpx;
    left: 0;
    background-color: #fff;
    z-index: 99;
    box-shadow: 0 12rpx 12rpx rgba(0, 0, 0, 0.16);
    overflow-y: scroll;

    .block {
      width: 100%;
      padding: 20rpx 30rpx;
      border-bottom: 2rpx solid #f8f8f8;
    }
  }
}
</style>
