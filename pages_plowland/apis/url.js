export const CUSTOM_DATA_URL = {
  ADD: (fcode, formUid) => `custom/form/${fcode}/${formUid}`,
  BATCH: (fcode, formName) => `custom/form/${fcode}/${formName}/batch`,
  LIST: (fcode, formUid) => `custom/form/${fcode}/${formUid}/search`,
  GET: (fcode, formUid, formRecordUid) => `custom/form/${fcode}/${formUid}/${formRecordUid}?resolveSubItems=true`,
  UPDATE: (fcode, formUid) => `custom/form/${fcode}/${formUid}`,
  DELETE: (fcode, formUid, formRecordUid) => `custom/form/${fcode}/${formUid}/${formRecordUid}`,
  JOIN_SEARCH: (fcode, formUid) => `custom/form/${fcode}/${formUid}/joinSearch`,
  UPDATE_STATE: (fcode, formUid, dataId) => `custom/form/${fcode}/${formUid}/${dataId}/state`,
  BATCH_STATE: (fcode, formName) => `custom/form/${fcode}/${formName}/state/batchUpdate`,
  BATCH_UPDATE: (fcode, formName) => `custom/form/${fcode}/${formName}/batchUpdate`,

  BATCH_DELETE: (fcode, formName) => `custom/form/${fcode}/${formName}/batchDelete`,
};
