<template>
	<view>
		<view class="filter-popup" :class="[value?'popup-show':'popup-hide']" :style="[customStyle]">
			<view class="filter-popup__box">
				<view class="filter-popup__block" v-for="(item,index) in options" :key="item.title">
					<view class="filter-popup__title">{{item.title}}</view>
					<view class="filter-popup__options">
						<view class="filter-popup__option" v-for="(items,idx) in item.options" :key="idx">
							<u-tag :text="items.label" :plain="!items.selected" @click="selectOption(index,idx)"></u-tag>
						</view>
					</view>
				</view>
			</view>
			<view class="filter-popup__placeholder"></view>
			<view class="filter-popup__bottom">
				<u-button color="#ccc" :customStyle="buttonStyle" @click="reset">重置</u-button>
				<u-button type="primary" :customStyle="buttonStyle" @click="confirm">确认</u-button>
			</view>
		</view>
		<view class="overlay" v-show="value" @click="close"></view>
	</view>

</template>

<script>
	import _ from 'lodash'
	export default {
		props: {
			value: {
				type: Boolean,
				default: false
			},
			data: {
				type: Array,
				default: () => []
			},
			customStyle: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
				buttonStyle: {
					height: '80rpx',
					borderRadius: 0,
					border: 'none',
				},
				options: [],
			}
		},
		watch: {
			data(newVal) {
				console.log("数据变化");
				this.handleOptions()
			}
		},
		mounted() {
			this.handleOptions()
		},
		methods: {
			// 确认筛选
			confirm() {
				let result = []
				this.options.forEach(item => {
					item.value = item.multiple ? [] : ""
					item.options.map(items => {
						if (item.multiple && items.selected) {
							item.value.push(items.value)
						} else if (!item.multiple && items.selected) {
							item.value = items.value
						}
						return items
					})
					result.push({
						key: item.key,
						value: item.value
					})
				})
				this.$emit('confirm', result)
			},
			// 重置筛选项
			reset() {
				this.options.forEach(item => {
					item.value = item.multiple ? [] : ""
					item.options.map((items, index) => {
						items.selected = item.required && index == 0
					})
				})
				this.$emit('confirm', null)
			},
			// 关闭筛选弹窗
			close() {
				this.options.forEach(item => {
					item.options.map(items => {
						if (item.multiple) {
							items.selected = item.value.includes(items.value)
						} else {
							items.selected = item.value == items.value
						}
						return items
					})
				})
				this.$emit('close')
			},
			selectOption(index, i) {
				let data = _.cloneDeep(this.options)
				// 判断选择为true的取消掉
				if (data[index].options[i].selected) {
					// 判断如果为必填的话禁止取消选择
					let hasTrue = data[index].options.filter(item => item.selected)
					if (data[index].required && hasTrue.length == 1) return
					data[index].options[i].selected = false
				} else if (!data[index].multiple) {
					data[index].options.forEach(item => {
						item.selected = false
					})
					data[index].options[i].selected = true
				} else {
					data[index].options[i].selected = true
				}
				this.options = data
			},
			handleOptions() {
				let data = []
				this.options = []
				this.data.forEach(item => {
					if (!item.value) item.value = item.multiple ? [] : ""
					if (!item.key) item.key = item.title
					item.options = item.options.map(items => {
						let obj = null
						if (typeof items == 'string') {
							obj = {
								value: items,
								label: items,
								selected: false,
							}
						} else {
							items.selected = false
							obj = items
						}
						if (item.multiple) {
							obj.selected = item.value.includes(obj.value)
						} else {
							obj.selected = item.value == obj.value
						}
						return obj
					})
					data.push(_.cloneDeep(item))
				})
				this.options = data
			},
		}
	}
</script>

<style scoped lang="scss">
	.filter-popup {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		background-color: #fff;
		opacity: 0;
		overflow: hidden;
		z-index: 98;

		&__box {
			padding: 20rpx 30rpx;
			box-sizing: border-box;
			max-height: 50vh;
			overflow-y: scroll;
		}

		&__block {
			margin-bottom: 20rpx;
		}

		&__options {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
		}

		&__option {
			margin-right: 14rpx;
			margin-bottom: 14rpx;
		}

		&__title {
			font-size: 28rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
		}

		&__placeholder {
			height: 80rpx;
		}

		&__bottom {
			display: flex;
			align-items: center;
			position: fixed;
			bottom: 0;
			width: 100%;
			height: 80rpx;
		}
	}

	.overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		margin: auto;
		background-color: rgba(0, 0, 0, 0.3);
		z-index: 90;
		transition: opacity 0.5s;
	}

	.popup-show {
		animation: fade-in-top 0.6s both;
	}

	.popup-hide {
		animation: fade-out-top 0.5s both;
	}

	// 动画
	@keyframes fade-in-top {
		0% {
			transform: translateY(-50px);
			opacity: 0;
		}

		100% {
			transform: translateY(0);
			opacity: 1;
		}
	}

	@keyframes fade-out-top {
		0% {
			transform: translateY(0);
			opacity: 1;
		}

		100% {
			transform: translateY(-50px);
			opacity: 0;
			height: 0;
		}
	}
</style>