<template>
	<view class="box">
		<view v-for="layer in layers" :key="layer.label" class="layer-item">
			<view class="switch">
				<u-switch @change="(e) => change(e, layer)" v-model="layer.open" size="12"></u-switch>
			</view>
			<view class="layer-label">
				<image :src="layer.icon"></image>
				<span> {{ layer.label }} </span>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			layers: {
				type: Array,
			},
		},
		methods: {
			change(e, layer) {
				this.$emit("change", layer, e);
			},
		},
	};
</script>

<style lang="scss" scoped>
	.box {
		position: absolute;
		right: 20rpx;
		bottom: 20rpx;
		z-index: 8;
		background-color: rgba(255, 255, 255, 0.5);
		backdrop-filter: blur(5px);
		padding: 20rpx;
		border-radius: 10rpx;

		.layer-item {
			display: flex;
			margin-bottom: 10rpx;
			align-items: center;

			.switch {
				margin-right: 10rpx;
			}

			.layer-label {
				display: flex;
				color: #333;
				align-items: center;

				image {
					width: 30rpx;
					height: 30rpx;
					margin-right: 10rpx;
				}
			}

		}
	}
</style>