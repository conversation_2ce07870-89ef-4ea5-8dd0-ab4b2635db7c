<template>
	<view class="zoom-widget" :style="customStyle">
		<view class="zoom-item" @click="zoomInClick">
			<u-icon name="plus"></u-icon>
		</view>
		<u-line></u-line>
		<view class="zoom-item" @click="zoomOutClick">
			<u-icon name="minus"></u-icon>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			customStyle: {
				type: Object,
				default: () => {},
			},
		},
		methods: {
			zoomInClick() {
				uni.$emit("zoomChange", {
					type: "zoomIn",
					uid: this.$utils.uid.genUID(),
				});
			},
			zoomOutClick() {
				uni.$emit("zoomChange", {
					type: "zoomOut",
					uid: this.$utils.uid.genUID(),
				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.zoom-widget {
		position: absolute;
		display: flex;
		flex-direction: column;
		z-index: 8;
		background-color: #fff;

		.zoom-item {
			padding: 18rpx;
		}
	}
</style>