<template>
	<!-- #ifdef APP-PLUS || H5 -->
	<view id="map-box" :direction="direction" :change:direction="mapbox.setMarkerDirection" :currentLocation="currentLocation" :change:currentLocation="mapbox.setCurrentLocation" :zoom="zoom" :change:zoom="mapbox.zoomChange" :pointData="pointData" :change:pointData="mapbox.addPointData"
		:layerData="layerData" :change:layerData="mapbox.changelayerData" :centerLocation="centerLocation" :change:centerLocation="mapbox.centerLocationChange" :drawing="drawing" :change:drawing="mapbox.drawChange" :redoPointUid="redoPointUid" :change:redoPointUid="mapbox.redoCoordinates"
		:endDrawHandlerUid="endDrawHandlerUid" :change:endDrawHandlerUid="mapbox.endDraw" :selectPointList="selectPointList" :change:selectPointList="selectPointHandle" :mapConfig="config" :change:mapConfig="setSource" :flyLocation="flyLocation" :change:flyLocation="flyToLocation">
		<!-- 这里是mapbox的地图容器 -->
		<legend-widget :layers="layerConfig" @change="changeLayer"></legend-widget>
		<zoom-widget :customStyle="{top:'20px',right:'20rpx'}"></zoom-widget>
		<local-widget @click="getLocation" :customStyle="{top:'100px',right:'20rpx'}"></local-widget>
		<draw-select-widget :customStyle="{top:'150px',right:'20rpx'}" v-if="multipleSelect"></draw-select-widget>
	</view>
	<!-- #endif -->
</template>
<script module="mapbox" lang="renderjs">
	const mapboxgl = require("!mapbox-gl/dist/mapbox-gl.js");
	import "mapbox-gl/dist/mapbox-gl.css";
	import { MAP_BASE_CONFIG, LARGE_TILESET } from './config/map.config.js'
	import { pointGreen, pointBlue, pointOrange, pointRed } from './config/image.config.js'
	// mapboxgl.accessToken = "pk.eyJ1IjoidmFuamsiLCJhIjoiY2tuemR0MDlrMDI5YzJ2bGNuaThwNjg3ZiJ9.Ix5XwtD2nE4rpqZ6u1j55Q";
	import DrawTool from "./utils/mapDraw.js";
	import markerImage from "./images/marker.js";
	import { pointsWithinPolygon, polygon } from '@turf/turf'
	import _ from 'lodash'
	export default {
		data() {
			return {
				map: null,
				marker: null,

				// 保存geojson数据
				mapConfig: { layers: [], sources: [], point: [] },

				selectPointsId: [], // 选择的点id

				baseZoomLevel: 15.5,

				listenerPointClick: false

			};
		},
		mounted() {
			this.createdMap();
		},
		methods: {
			// 飞到当前定位点
			flyToLocation(data) {
				if (!this.map || !data) return
				if (Array.isArray(data)) {
					this.map.fitBounds(data, { padding: 20 })
				} else {
					this.map.flyTo({
						center: [data.longitude, data.latitude],
						zoom: data.zoom || this.baseZoomLevel,
						speed: 1.5,
						easing: (t) => {
							this.$ownerInstance.callMethod("recoverLocation");
							return t;
						},
					})
				}
			},
			// 过滤图层
			changelayerData(value) {
				if (!this.map || !value) return
				if (Array.isArray(value.key)) {
					value.key.forEach(key => {
						if (!this.map.getLayer(key)) return
						this.map.setLayoutProperty(key, "visibility", value.open ? "visible" : "none");
					})
				} else {
					if (!this.map.getLayer(value.key)) return
					this.map.setLayoutProperty(value.key, "visibility", value.open ? "visible" : "none");
				}
			},
			createdMap() {
				console.log("创建地图");
				//创建地图
				this.map = new mapboxgl.Map(MAP_BASE_CONFIG);
				this.map.on("load", this.mapLoad);
				window.map = this.map;
			},
			mapLoad() {
				// 添加标记点
				const el = document.createElement("img");
				el.className = "marker";
				el.src = markerImage;
				el.style.width = "20px";

				this.marker = new mapboxgl.Marker({
						element: el,
						pitchAlignment: "map",
						rotationAlignment: "map",
					})
					.setLngLat([108, 23])
					.addTo(this.map);
				this.addImages()
				this.initDraw()
				this.map.on('moveend', () => {
					if (this.map.getZoom() < this.baseZoomLevel) return
					let bounds = this.map.getBounds()
					let latLng = [bounds.getNorthWest(), bounds.getSouthWest(), bounds.getSouthEast(), bounds.getNorthEast(),
						bounds.getNorthWest()
					]
					this.$ownerInstance.callMethod("refreshPoint", bounds);
				});
				this.map.on('zoom', (e) => {
					let zoom = this.map.getZoom()
					this.$ownerInstance.callMethod("mapZoomChange", zoom);
				});
				this.$ownerInstance.callMethod("mapboxLoaded");
				// 禁止双指旋转地图
				this.map.touchZoomRotate.disableRotation();
			},
			// 删除所有sources和layer
			removeSources() {
				const config = this.mapConfig
				const sources = config.sources
				sources.forEach(source => {
					if (this.map.getLayer(source.id)) this.map.removeLayer(source.id);
					if (this.map.getSource(source.id)) this.map.removeSource(source.id);
				});
			},
			// 获取行政区数据设置图层
			setSource(config) {
				if (!this.map) return;
				this.removeSources()
				this.mapConfig = _.cloneDeep(config)
				console.log("地图配置参数", config);
				// 获取参数后需重新初始化监听
				this.addPointListener()
				const layers = config.layers
				// TODO
				const sources = config.sources
				sources.forEach(source => {
					if (!this.map.getSource(source.id)) {
						this.map.addSource(source.id, {
							type: "raster",
							tiles: [`${process.env.MAP_URL}api/maptiles/${source.name}/{z}/{x}/{y}.png`],
							maxzoom: 16,
						});
						this.map.addLayer({
							id: source.id,
							type: 'raster',
							source: source.id,
						});
					}
				});
				// 轮询处理隐藏或显示的图层
				layers.forEach(item => {
					this.changelayerData(item)
				})
			},
			// Draw 模块
			// 框选状态改变
			drawChange(e) {
				if (!this.map) return
				if (e) {
					this.draw.activate('polygon');
					// 关闭点位点击事件
				} else {
					this.draw.deactivate();
				}
			},
			initDraw() {
				this.draw = new DrawTool(this.map, this);
				this.map.on('click', (e) => {
					if (this.drawing) {
						this.draw.addCoordinates([e.lngLat.lng, e.lngLat.lat]);
					}
				})
			},
			drawTypeChange(e) {
				if (!this.draw) return
				if (!e) return;
				if (e == "deactivate") {
					this.draw.deactivate();
				} else {
					this.draw.activate(e);
				}
			},
			addCoordinates(e) {
				if (!e) return;
				const coord = this.map.getCenter();
				this.draw.addCoordinates([coord.lng, coord.lat]);
			},
			redoCoordinates(e) {
				if (!e) return;
				this.draw.redoCoordinates();
			},
			endDraw(e) {
				if (!this.map) return
				let ary = []
				if (this.map.getZoom() > this.baseZoomLevel) {
					if (!e || !this.draw) return;
					const val = this.draw.getGeomData();
					const empty_obj = { features: [] }
					const point_list = this.mapConfig.point
					point_list.forEach((item) => {
						const ptsWithin = this.map.getLayoutProperty(item.point, 'visibility') == 'none' ? empty_obj : pointsWithinPolygon(this[item.key], val);
						ptsWithin.features.forEach(item => ary.push(item.properties));
					});
				}
				// 返回选中的点信息
				this.$ownerInstance.callMethod("drawSelectPoint", ary);
			},
			// Point 模块
			// 参数 1.数据geojson 2.layerIs 3.图标key 4.图片缩放
			addPointLayer(geojson, id, iconName, size) {
				this.pointSourceGeoJson = geojson
				this.$nextTick(() => {
					if (this.map.getSource(id)) {
						this.map.getSource(id).setData(geojson);
					} else {
						this.map.addSource(id, {
							type: "geojson",
							data: geojson,
						});
						this.map.addLayer({
							id: id,
							type: "symbol",
							source: id,
							minzoom: this.baseZoomLevel,
							layout: {
								"icon-size": size,
								"icon-allow-overlap": true,
								"icon-image": iconName,
								"visibility": 'visible',
							},
						});
					}
				});
			},
			// 添加点数据
			addPointData(newValue) {
				this.$ownerInstance.callMethod("pointLoadStatus", 'loading');
				if (!newValue) return
				const point_list = this.mapConfig.point
				point_list.forEach(condition => {
					let filtered = newValue.filter(item => {
						let result = true
						condition.filter.forEach(items => {
							if (item[items.key] !== items.value) result = false
						})
						return result
					});
					this[condition.key] = this.getGeojson(filtered, condition.point);
					this.addPointLayer(this[condition.key], condition.point, condition.icon, 0.1);
					if (this.map.getLayer(condition.point)) this.map.moveLayer(condition.point)
				});
				if (newValue && newValue.length > 0) {
					this.$ownerInstance.callMethod("pointLoadStatus", 'loaded');
				}
			},
			addPointListener() {
				if (this.listenerPointClick) return
				const point_list = this.mapConfig.point
				this.listenerPointClick = true
				let info = null

				let lastEventState = null;
				point_list.forEach(({ point }) => {
					this.map.on('click', point, (e) => {
						if (lastEventState && Date.now() - lastEventState.timestamp < 200) return;
						// 保存这次点击的状态
						lastEventState = {
							timestamp: Date.now(),
							point: e.point,
						};
						const features = this.map.queryRenderedFeatures(e.point)
						if (features && features.length > 0) {
							console.log("触发了地图点击", features);
							this.$ownerInstance.callMethod("pointClick", features[0].properties);
						}
					})
				})
			},
			// 选点数据变化
			selectPointHandle(e) {
				this.selectPointsId = e.length > 0 ? e.map(item => item._id) : []
				this.handlePointStyle()
			},
			handlePointStyle() {
				if (!this.map) return
				let value = this.selectPointsId.length > 0 ? ['match', ['get', '_id'],
					[...this.selectPointsId], 0.15, 0.1
				] : 0.1;

				const point_list = this.mapConfig.point
				point_list.forEach((e) => {
					let icon = this.selectPointsId.length > 0 ? ['match', ['get', '_id'],
						[...this.selectPointsId], 'red', e.icon
					] : e.icon;
					this.map.setLayoutProperty(e.point, "icon-size", value);
					this.map.setLayoutProperty(e.point, "icon-image", icon);
				});
			},
			getGeojson(newValue, layerId) {
				const features = [];
				if (Array.isArray(newValue) && newValue.length > 0) {
					newValue.forEach((item) => {
						let obj = {
							type: "Feature",
							properties: {
								...item,
								layerId: layerId
							},
							geometry: {
								coordinates: [item.longitude, item.latitude],
								type: "Point",
							},
						};
						features.push(obj);
					});
				}
				const geojson = {
					type: "FeatureCollection",
					features: features,
				};
				return geojson
			},
			addImages() {
				const imagesList = [{
					key: "green",
					state: "green",
					iconPath: pointGreen
				}, {
					key: "blue",
					state: "blue",
					iconPath: pointBlue
				}, {
					key: "orange",
					state: "orange",
					iconPath: pointOrange
				}, {
					key: "red",
					state: "red",
					iconPath: pointRed
				}];
				for (const item of imagesList) {
					this.map.loadImage(item.iconPath, (error, image) => {
						if (error) throw error;
						this.map.addImage(item.key, image, {

						});
					});
				}
			},
			setMarkerDirection(e) {
				this.marker && this.marker.setRotation(e);
			},
			setCurrentLocation(val) {
				if (!Array.isArray(val) || !val.length > 0) return;
				if (this.marker) {
					this.marker.setLngLat(val);
				}
			},
			zoomChange(e) {
				if (!e) return;
				const {
					type
				} = JSON.parse(e);
				switch (type) {
					case "zoomIn":
						this.map.zoomIn();
						break;
					case "zoomOut":
						this.map.zoomOut();
						break;
					default:
						break;
				}
			},
			centerLocationChange(e) {
				if (!e) return;
				const {
					longitude,
					latitude
				} = JSON.parse(e);
				this.map.setCenter([longitude, latitude]);
			},

			showToast(e) {
				this.$ownerInstance.callMethod("showToastInfo", e);
			},
			showMessage(e) {
				this.$ownerInstance.callMethod("showMessageInfo", e);
			},
		},
	};
</script>
<script>
	import _ from 'lodash'
	import LegendWidget from '@/pages_plowland/components/MapWidget/LegendWidget'
	import localWidget from '@/pages_plowland/components/MapWidget/LocalWidget'
	import zoomWidget from '@/pages_plowland/components/MapWidget/ZoomWidget'
	import drawSelectWidget from '@/pages_plowland/components/MapWidget/DrawSelectWidget'
	export default {
		components: {
			LegendWidget,
			localWidget,
			zoomWidget,
			drawSelectWidget
		},
		props: {
			point: {
				type: Array,
				default: () => []
			},
			config: {
				type: Object,
				default: () => {}
			},
			multipleSelect: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				mapLoaded: false,
				direction: null,
				currentLocation: null,
				zoom: null,
				centerLocation: null,
				centerLonLat: null,
				flyLocation: null,
				// 触发视图层事件用
				addPointUid: null,
				redoPointUid: null,
				endDrawHandlerUid: null,

				// 点位数据
				pointData: [],
				layerData: null,

				// 框选
				drawing: false,
				selectPointList: [],
				layerConfig: [],
			};
		},
		mounted() {
			uni.$on("zoomChange", this.zoomChangeHandler);
			uni.$on("toolDeactivate", this.drawDeactivateHandler);
			uni.$on("startDraw", this.drawAddPoint);
			uni.$on("redoPoint", this.redoPointHandler);
			uni.$on("endDraw", this.endDrawHandler);
			uni.onCompassChange(this.compassChange);

			uni.startLocationUpdate({
				type: "wgs84",
				needFullAccuracy: true,
			});
			uni.onLocationChange(this.locationChange);
		},
		beforeDestroy() {
			uni.stopLocationUpdate();
			uni.offCompassChange(this.compassChange);
		},
		watch: {
			config(newVal) {
				let config = _.cloneDeep(newVal)
				if (config && config.layers) this.layerConfig = config.layers
			},
			point(newVal) {
				let interval = setInterval(() => {
					if (this.mapLoaded) {
						this.pointData = [...newVal]
						clearInterval(interval)
					}
				}, 200)
			}
		},
		computed: {},
		methods: {
			// 重置点数据
			resetPointList() {
				this.selectPointList = []
			},
			// 框选结果
			drawSelectPoint(list) {
				let array1 = list;
				let array2 = _.cloneDeep(this.selectPointList)
				let ary = [...array1, ...array2];
				let result = [];
				ary.forEach(item => {
					let index = result.findIndex(items => items._id == item._id);
					if (index >= 0) {
						result.splice(index, 1);
					} else {
						result.push(item);
					}
				});
				this.selectPointList = result
				this.drawing = false
				this.$emit('getSelectPoint', this.selectPointList)
			},
			// 点击点
			pointClick(data) {
				if (this.drawing) return
				if (!this.multipleSelect) this.selectPointList = []
				let list = _.cloneDeep(this.selectPointList)
				let index = list.findIndex(item => item._id == data._id)
				if (index >= 0) {
					list.splice(index, 1);
				} else {
					list.push(data);
				}
				this.selectPointList = list
				this.$emit('getSelectPoint', this.selectPointList)
				this.$emit('point-click', data)
			},
			// 开始框选
			drawAddPoint() {
				this.drawing = true
			},
			// 撤销一个点
			redoPointHandler() {
				this.redoPointUid = this.$utils.uid.genUID();
			},
			// 退出框选
			drawDeactivateHandler() {
				this.drawing = false
			},
			// 确认框选
			endDrawHandler() {
				this.endDrawHandlerUid = this.$utils.uid.genUID();
			},
			changeLayer(layer, e) {
				let data = layer
				data.open = e
				this.layerData = data
			},
			compassChange(e) {
				this.direction = e.direction;
			},
			// 重置定位点
			recoverLocation() {
				this.flyLocation = null;
			},
			getLocation(e) {
				if (!this.mapLoaded) return;
				this.flyLocation = e;
			},
			locationChange(res) {
				if (!this.mapLoaded) return;
				if (res.altitude === 0 && res.speed === 0 && this.currentLocation) return;
				if (!this.centerLocation) this.centerLocation = JSON.stringify(res);
				let lnglat = this.$utils.proj.wgs84togcj02(res.longitude, res.latitude)
				this.currentLocation = lnglat;
			},
			refreshPoint(bounds) {
				this.$emit('moveEnd', { bounds })
			},
			pointLoadStatus(status) {
				if (status == 'loading') {
					this.$emit("pointLoading")
				} else {
					this.$emit("pointLoaded")
				}
			},
			mapZoomChange(zoom) {
				this.$emit('zoomChange', zoom)
			},
			mapboxLoaded() {
				this.mapLoaded = true;
				this.pointData = [...this.point]
				this.$emit("mapLoaded")
			},
			zoomChangeHandler(e) {
				if (!this.mapLoaded) return;
				this.zoom = JSON.stringify(e);
			},
			setCenterLonLat(e) {
				if (!this.mapLoaded || !e) return;
				this.centerLocation = JSON.stringify(e)
			},
			showToastInfo(e) {
				uni.$u.toast(e);
			},
		},
	};
</script>

<style lang="scss" scoped>
	#map-box {
		width: 100%;
		height: 100%;
		position: relative;
	}

	.plus-info {
		position: absolute;
		right: 20rpx;
		top: 20rpx;
		background-color: rgba(0, 0, 0, 0.5);
		color: #fff;
		z-index: 99;
		padding: 10rpx 20rpx;
		border-radius: 8rpx;
	}

	/deep/.mapboxgl-popup-anchor-top-left,
	/deep/.mapboxgl-popup-anchor-top-right,
	/deep/.mapboxgl-popup-anchor-bottom-left,
	/deep/.mapboxgl-popup-anchor-bottom-right,
	/deep/.mapboxgl-popup-anchor-bottom,
	/deep/.mapboxgl-popup-anchor-top,
	/deep/.mapboxgl-popup-anchor-right,
	/deep/.mapboxgl-popup-anchor-left {
		.mapboxgl-popup-tip {
			// display: none;
			visibility: hidden;
		}

		.mapboxgl-popup-content {
			background-color: rgba(0, 0, 0, 0.5);
			color: #fff;
			font-size: 1.5vh;
			font-weight: 500;
			padding-bottom: 0 !important;
			padding-top: 0 !important;
			padding-left: 10rpx !important;
			padding-right: 10rpx !important;
		}
	}

	/deep/.mapboxgl-ctrl-logo {
		display: none !important;
	}

	/deep/.mapboxgl-ctrl-bottom-right {
		display: none !important;
	}
</style>