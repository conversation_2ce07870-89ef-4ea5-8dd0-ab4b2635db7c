import { distance, rectangle, circle } from "./helper";
import { geomTypes, getPointSourceData, getFeatureData } from "./config";
import mapboxgl from "mapbox-gl";
import * as turf from "@turf/turf";

const getSourceFuns = {
  __draw_layer_point_circle_id__: getPointSourceData,
  __draw_layer_line_point_id__: getPointSourceData,
  __draw_layer_line_id__: (coord) => {
    return getFeatureData(coord, "LineString");
  },
  __draw_layer_polygon_point_id__: getPointSourceData,
  __draw_layer_polygon_line_id__: (coord) => {
    return getFeatureData([...coord, coord[0]], "LineString");
  },
  __draw_layer_polygon_fill_id__: (coord) => {
    if (coord.length < 3) {
      return getFeatureData([], "Polygon");
    }
    return getFeatureData([[...coord, coord[0]]], "Polygon");
  },
  __draw_layer_circle_point_id__: getPointSourceData,
  __draw_layer_circle_line_id__: (coord) => {
    if (coord.length !== 2) {
      return getFeatureData([], "LineString");
    }
    const res = circle(coord[0], coord[1]);
    return getFeatureData(res, "LineString");
  },
  __draw_layer_circle_fill_id__: (coord) => {
    if (coord.length !== 2) {
      return getFeatureData([], "Polygon");
    }
    const res = circle(coord[0], coord[1]);
    return getFeatureData([res], "Polygon");
  },
};

export default class DrawTool {
  map = null;
  drawType = null;
  clickHandlers = null;
  coordinates = null;
  layers = null;
  popups = [];
  context;
  assistPopup = new mapboxgl.Popup({
    className: "draw-info-popup",
    closeOnClick: false,
    closeButton: false,
  });

  constructor(map, context) {
    this.context = context;
    this.map = map;
  }

  activate(type) {
    this.coordinates = [];
    this.drawType = type;
    this.addDrawLayer();
    if (type == "polygon") {
      this.popups.push(
        new mapboxgl.Popup({
          className: "draw-info-popup",
          closeOnClick: false,
          closeButton: false,
          // anchor: "top-left",
        })
      );
    }
  }

  deactivate() {
    this.coordinates = [];
    this.drawType = null;
    if (this.popups.length) {
      this.popups.forEach((p) => {
        p.remove();
      });
    }
    if (this.assistPopup) {
      this.assistPopup.remove();
    }
    this.popups = [];
    this.addDrawLayer();
    this.context.showMessage(null);
  }

  /**
   * 添加当前类型所需图层
   */
  addDrawLayer() {
    let arr = [];
    Object.keys(geomTypes).forEach((type) => {
      if (type == this.drawType) {
        const layers = geomTypes[type];
        layers.forEach((item) => {
          if (!this.map.getLayer(item.id)) {
            console.log(item);
            this.map.addLayer(item);
            console.log(this.map.getLayer(item.id));
          }
          arr.push(item.id);
        });
      } else {
        const layers = geomTypes[type];
        layers.forEach((item) => {
          if (this.map.getLayer(item.id)) {
            this.map.removeLayer(item.id);
            this.map.removeSource(item.id);
          }
        });
      }
    });
    this.layers = arr;
    console.log(this.layers);
  }

  /**
   * 添加coord
   * @param {*} coord 点位坐标
   */
  addCoordinates(coord) {
    if (this.drawType == "circle" && this.coordinates.length == 2) {
      this.coordinates.pop();
      const lonLat = this.map.getCenter();
      this.setMapCenter([lonLat.lng, lonLat.lat]);
    }
    if (this.drawType == "polygon" && this.coordinates.length >= 3) {
      let flag = this.checkCrosses(coord);
      if (flag) return;
    }
    this.coordinates.push(coord);
    if (this.drawType == "line" && this.coordinates.length >= 2) {
      this.addLinePopup();
      this.assistPopup.remove();
      this.map.getSource("__draw_layer_line_assist_id__").setData({
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: [],
        },
      });
    }
    if (this.drawType == "polygon" && this.coordinates.length >= 2) {
      this.addPolygonPopup();
    }

    this._updateDrawFeature();
    console.log(this.popups);
  }

  /**
   * 判断多边形是否自相交
   * @param {*} coord 新添加点位坐标
   */
  checkCrosses(coord) {
    let flag = false;
    const lastLine = turf.lineString([
      coord,
      this.coordinates[this.coordinates.length - 1],
    ]);
    for (let i = 0; i < this.coordinates.length - 2; i++) {
      const line = turf.lineString([
        this.coordinates[i],
        this.coordinates[i + 1],
      ]);
      var cross = turf.lineIntersect(lastLine, line);

      if (cross.features.length) flag = true;
      // const element = array[i];
    }
    if (flag) {
      this.context.showToast(`绘制范围存在自相交，\n请仔细核对`);
      // uni.$u.toast('当前绘制范围存在自相交，请仔细核对');
    }
    return flag;
  }

  /**
   * 撤回coord
   */
  redoCoordinates() {
    if (!this.coordinates.length) return;

    if (this.drawType == "line") {
      if (this.coordinates.length >= 2) {
        console.log(this.popups);
        this.popups[this.popups.length - 1].remove();
        this.popups.pop();
      }

      this.clearAssist("__draw_layer_line_assist_id__", "LineString");
    }

    this.coordinates.pop();

    if (this.drawType == "circle") {
      if (this.coordinates.length == 0) {
        this.clearAssist("__draw_layer_circle_r_id__", "LineString");
      }
      if (this.coordinates.length == 1) {
        const lonLat = this.map.getCenter();
        this.setMapCenter([lonLat.lng, lonLat.lat]);
      }
    }

    if (this.drawType == "polygon") {
      if (this.coordinates.length <= 2) {
        this.popups[0].remove();
      }
      if (this.coordinates.length >= 1) {
        this.popups[this.popups.length - 1].remove();
        this.popups.pop();
        if (this.coordinates.length >= 2) {
          this.addPolygonPopup(true);
        }
      }
    }

    this._updateDrawFeature();
  }

  /**
   * 更新layer数据
   */
  _updateDrawFeature() {
    console.log(this.drawType);
    switch (this.drawType) {
      case "point":
        this.layers.forEach((item) => {
          const data = getSourceFuns[item](this.coordinates);
          this.map.getSource(item).setData(data);
        });
        break;
      case "line":
        this.layers.forEach((item) => {
          if (!getSourceFuns[item]) return;
          const data = getSourceFuns[item](this.coordinates);
          this.map.getSource(item).setData(data);
        });
        this.lineInfoEmit();

        break;
      case "polygon":
        this.layers.forEach((item) => {
          const data = getSourceFuns[item](this.coordinates);
          this.map.getSource(item).setData(data);
        });
        this.polygonInfoEmit();
        break;
      case "circle":
        this.layers.forEach((item) => {
          if (!getSourceFuns[item]) return;
          const data = getSourceFuns[item](this.coordinates);
          this.map.getSource(item).setData(data);
        });
        this.circleInfoEmit();
        break;

      default:
        break;
    }
  }
  /**
   * 线绘制过程中描述popup
   */
  addLinePopup() {
    const lastCoordinates = [
      this.coordinates[this.coordinates.length - 1],
      this.coordinates[this.coordinates.length - 2],
    ];
    console.log(lastCoordinates);
    // const val = this.getDistance(this.coordinates);
    const val = this.getDistance(lastCoordinates);
    const lonLat = this.getMidpoint(lastCoordinates[0], lastCoordinates[1]);

    // const val = this.getDistance(this.coordinates);
    // const lonLat = this.coordinates[this.coordinates.length - 1];
    const popup = new mapboxgl.Popup({
      className: "draw-info-popup",
      closeOnClick: false,
      closeButton: false,
      // anchor: "top-left",
    });

    popup
      .setLngLat(lonLat)
      .setHTML(
        `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val}</text>`
      )
      .addTo(this.map);
    this.popups.push(popup);
  }
  /**
   * 多边形绘制过程中描述popup
   */
  addPolygonPopup(refresh) {
    if (!refresh) {
      const lastCoordinates = [
        this.coordinates[this.coordinates.length - 1],
        this.coordinates[this.coordinates.length - 2],
      ];
      console.log(lastCoordinates);
      // const val = this.getDistance(this.coordinates);
      const val = this.getDistance(lastCoordinates);
      const lonLat = this.getMidpoint(lastCoordinates[0], lastCoordinates[1]);

      const popup = new mapboxgl.Popup({
        className: "draw-info-popup",
        closeOnClick: false,
        closeButton: false,
        // anchor: "top-left",
      });

      popup
        .setLngLat(lonLat)
        .setHTML(
          `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val}</text>`
        )
        .addTo(this.map);
      this.popups.push(popup);
    }

    const startToEnd = [
      this.coordinates[0],
      this.coordinates[this.coordinates.length - 1],
    ];
    const val1 = this.getDistance(startToEnd);
    const lonLat1 = this.getMidpoint(startToEnd[0], startToEnd[1]);

    this.popups[0]
      .setLngLat(lonLat1)
      .setHTML(
        `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val1}</text>`
      )
      .addTo(this.map);

    // 这里是统计面积
    // if (this.coordinates.length >= 3) {
    //   const p = turf.polygon([[...this.coordinates, this.coordinates[0]]]);
    //   const lonLat2 = turf.centerOfMass(p).geometry.coordinates;
    //   const val = this.getArea([...this.coordinates, this.coordinates[0]]);

    //   // this.popups[0]
    //   //   .setLngLat(lonLat1)
    //   //   .setHTML(
    //   //     `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val1}</text>`
    //   //   )
    //   //   .addTo(this.map);
    //   this.popups[0]
    //     .setLngLat(lonLat2)
    //     .setHTML(
    //       `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val}</text>`
    //     )
    //     .addTo(this.map);
    // }
  }

  /**
   * 地图视图中心发生变化执行
   */
  setMapCenter(e) {
    if (this.drawType == "circle" && this.coordinates.length == 1) {
      const coord = [this.coordinates[0], e];
      const data = getFeatureData(coord, "LineString");
      this.map.getSource("__draw_layer_circle_r_id__").setData(data);
      const val = this.getDistance(coord);
      const midpoint = this.getMidpoint(coord[0], coord[1]);
      this.assistPopup
        .setLngLat(midpoint)
        .setHTML(
          `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">半径：${val}</text>`
        )
        .addTo(this.map);
    }
    if (this.drawType == "line" && this.coordinates.length >= 1) {
      const coord = [this.coordinates[this.coordinates.length - 1], e];
      const data = getFeatureData(coord, "LineString");
      this.map.getSource("__draw_layer_line_assist_id__").setData(data);
      const val = this.getDistance(coord);
      const midpoint = this.getMidpoint(coord[0], coord[1]);
      this.assistPopup
        .setLngLat(midpoint)
        .setHTML(
          `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val}</text>`
        )
        .addTo(this.map);
    }
  }

  /**
   * 移除绘制过程中辅助线及popup
   */
  clearAssist(layer, type) {
    this.assistPopup.remove();
    this.map.getSource(layer).setData({
      type: "Feature",
      geometry: {
        type,
        coordinates: [],
      },
    });
  }

  /**
   * 计算距离
   */
  getDistance(coord, onlyNum) {
    const length = turf.length(turf.lineString(coord));
    if (onlyNum) return length;
    let unit = "公里";
    let num = length;
    if (length < 1) {
      num = length * 1000;
      unit = "米";
    }
    num = parseFloat(num.toFixed(2));
    return `${num}${unit}`;
  }

  /**
   * 计算面积
   */
  getArea(coord) {
    const p = turf.polygon([coord]);
    const area = turf.area(p);
    let unit = "平方米";
    let num = area;
    if (num > 10000 && num < 1000000) {
      num = area / 10000;
      unit = "公顷";
    } else if (num > 1000000) {
      num = area / 1000000;
      unit = "平方千米";
    }
    num = parseFloat(num.toFixed(2));
    return `${num}${unit}`;
  }

  /**
   * 获取亮点中心点
   */
  getMidpoint(point1, point2) {
    return turf.midpoint(turf.point(point1), turf.point(point2)).geometry
      .coordinates;
  }

  lineInfoEmit() {
    if (this.coordinates.length >= 2) {
      const allVal = this.getDistance(this.coordinates);
      console.log(allVal);
      this.context.showMessage([
        {
          label: "总长",
          val: allVal,
        },
      ]);
    } else {
      this.context.showMessage(null);
    }
  }

  polygonInfoEmit() {
    if (this.coordinates.length >= 3) {
      const area = this.getArea([...this.coordinates, this.coordinates[0]]);
      const distance = this.getDistance([
        ...this.coordinates,
        this.coordinates[0],
      ]);
      this.context.showMessage([
        {
          label: "面积",
          val: area,
        },
        {
          label: "周长",
          val: distance,
        },
      ]);
    } else {
      this.context.showMessage(null);
    }
  }

  circleInfoEmit() {
    console.log(this.coordinates);
    if (this.coordinates.length == 2) {
      const radius = this.getDistance(this.coordinates, true);
      const area = Math.PI * radius * 1000 * radius * 1000;
      console.log(area);
      const circumference = Math.PI * radius * 2;
      this.context.showMessage([
        {
          label: "面积",
          val:
            area > 1000000
              ? `${parseFloat((area / 1000000).toFixed(2))}平方千米`
              : area > 10000
              ? `${parseFloat((area / 10000).toFixed(2))}公顷`
              : `${parseFloat(area.toFixed(2))}平方米`,
        },
        {
          label: "周长",
          val:
            circumference > 1
              ? `${parseFloat(circumference.toFixed(2))}公里`
              : `${parseFloat((circumference * 1000).toFixed(2))}米`,
        },
      ]);
    } else {
      this.context.showMessage(null);
    }
  }

  getGeomData() {
    console.log(this.drawType, this.coordinates);
    if (!this.drawType) return;

    switch (this.drawType) {
      case "point":
        if (!this.coordinates.length) return;
        return this.map.getSource("__draw_layer_point_circle_id__")._data;

      case "line":
        if (this.coordinates.length < 2) return;
        return this.map.getSource("__draw_layer_line_id__")._data;

      case "polygon":
        if (this.coordinates.length < 3) return;
        return this.map.getSource("__draw_layer_polygon_fill_id__")._data;

      case "circle":
        if (this.coordinates.length !== 2) return;
        return this.map.getSource("__draw_layer_circle_fill_id__")._data;
      default:
        break;
    }
  }
}
