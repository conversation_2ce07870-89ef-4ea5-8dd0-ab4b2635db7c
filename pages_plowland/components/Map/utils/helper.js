const EARTH_RADIUS = 6371008.8;
const CIRCLE_STEPS = 128;

const degreesToRadians = function (degrees) {
  const radians = degrees % 360;
  return (radians * Math.PI) / 180;
};

const radiansToLength = function (radians) {
  return radians * EARTH_RADIUS;
};

const lengthToRadians = function (radians) {
  return radians / EARTH_RADIUS;
};

const radiansToDegrees = function (radians) {
  const degrees = radians % (2 * Math.PI);
  return (degrees * 180) / Math.PI;
};

// 单位米
const distance = function (pnt1, pnt2) {
  var dLat = degreesToRadians(pnt2[1] - pnt1[1]);
  var dLon = degreesToRadians(pnt2[0] - pnt1[0]);
  var lat1 = degreesToRadians(pnt1[1]);
  var lat2 = degreesToRadians(pnt2[1]);
  var a =
    Math.pow(Math.sin(dLat / 2), 2) +
    Math.pow(Math.sin(dLon / 2), 2) * Math.cos(lat1) * Math.cos(lat2);
  return radiansToLength(2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)));
};

const destination = function (origin, distance, bearing) {
  const longitude1 = degreesToRadians(origin[0]);
  const latitude1 = degreesToRadians(origin[1]);
  const bearingRad = degreesToRadians(bearing);
  const radians = lengthToRadians(distance);
  // Main
  const latitude2 = Math.asin(
    Math.sin(latitude1) * Math.cos(radians) +
      Math.cos(latitude1) * Math.sin(radians) * Math.cos(bearingRad)
  );
  const longitude2 =
    longitude1 +
    Math.atan2(
      Math.sin(bearingRad) * Math.sin(radians) * Math.cos(latitude1),
      Math.cos(radians) - Math.sin(latitude1) * Math.sin(latitude2)
    );
  const lng = radiansToDegrees(longitude2);
  const lat = radiansToDegrees(latitude2);

  return [lng, lat];
};

const rectangle = function (pnt1, pnt2) {
  if (!pnt1 || !pnt2) return [];
  var xmin = Math.min(pnt1[0], pnt2[0]);
  var xmax = Math.max(pnt1[0], pnt2[0]);
  var ymin = Math.min(pnt1[1], pnt2[1]);
  var ymax = Math.max(pnt1[1], pnt2[1]);
  var tl = [xmin, ymax];
  var tr = [xmax, ymax];
  var br = [xmax, ymin];
  var bl = [xmin, ymin];
  return [tl, tr, br, bl, tl];
};

const circle = function (pnt1, pnt2) {
  if (!pnt1 || !pnt2) return [];
  const radius = distance(pnt1, pnt2);
  const coordinates = [];
  for (let i = 0; i < CIRCLE_STEPS; i++) {
    coordinates.push(destination(pnt1, radius, (i * -360) / CIRCLE_STEPS));
  }
  coordinates.push(coordinates[0]);
  return coordinates;
};

export { distance, circle, rectangle };
