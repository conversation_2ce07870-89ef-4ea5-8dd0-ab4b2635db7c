<template>
	<view class="gt-picture-field">
		<u-form-item :label="label" :prop="field" :required="required"
			:class="{'label-at-bottom': labelPosition === 'bottom'}">
			<u-upload :fileList="fileList" :previewImage="true" @afterRead="afterReadHandler" @delete="deleteHandler"
				:deletable="!readonly" :maxCount="max" :maxSize="maxSize" :disabled="readonly"></u-upload>
		</u-form-item>
		<view style="position: absolute; top: -999999px">
			<canvas :style="{ width: canvasWidth, height: canvasHeight }" :width="canvasWidth" :height="canvasHeight"
				:canvas-id="field"></canvas>
		</view>
		<view class="gt-text-tips" v-if="tips">{{tips}}</view>
		<view class="example" v-if="options.example">
			<view class="label">示例图片</view>
			<u-album :urls="[options.example]"></u-album>
		</view>
	</view>
</template>

<script>
	
	import BaseField from '@/components/form/BaseField'
	import WaterMarker from '@/tools/WaterMarker'
	import LocalFile from '@/tools/File'
	import Upload from '@/tools/Upload'
	import Env from '@/env'

	export default {

		name: 'PictureField',

		mixins: [BaseField],

		data() {
			return {
				fileList: [],
				value: [],
				canvasWidth: 0,
				canvasHeight: 0,
				imageList: []
			}
		},

		computed: {
			maxSize() {
				return this.options.sizeLimit * 1024
			},
			// 水印信息
			watermarkFields() {
				return this.options.watermarkFields
			},
			max() {
				return this.options.max
			},
			labelPosition() {
				return this.options.labelPosition || 'top'
			},
			path() {
				return this.options.path || ''
			},
			tips(){
				return this.options.tips || ''
			}
		},

		mounted() {
			this.updateFormData()
    },

		methods: {
			
			// 设置值
			_setValue ( value ,silently = true) {
				this.value = value === null ? undefined : value;
				if(!silently) this.updateFormData()
			},
			
			// 设置值
			setValue ( value ,silently = true) {
				this._setValue(value, silently)
				this.fileList = this.genFileListFromValue()
			},
			
			// 删除图片
			async deleteHandler(event) {
				this.fileList.splice(event.index, 1)
				const value = this.genValueFromFileList()
				this._setValue(value, false)
			},
			
			genValueFromFileList(){
				let filePaths = []
				for(let file of this.fileList){
					if(file.status === 'success' && file.path){
						filePaths.push(file.path)
					}
				}
				return filePaths
			},
			
		genFileListFromValue(){
				if(!this.value) return
				let fileList = []
				const endPoint = Env.OSS_ENDPOINT
				for(let filePath of this.value){
					const url = `${endPoint}/${filePath}`
					fileList.push({
						url,
						thumb: url,
						path: filePath,
						status: 'success'
					})
				}
				return fileList
			},

			// 新增图片
			async afterReadHandler(event) {
				const file = event.file
				if (!file) return
				// 判断是否是从相册选择的图片
				let needSaveToAlbum = file.url.indexOf('/camera/') > 0
				// 生成UUID用于图片命名
				const fileName = this.$utils.uid.genUID()
				// 判断是否符合加水印的条件
				let watermarks = []
				if (
					this.watermarkFields &&
					Array.isArray(this.watermarkFields) &&
					this.watermarkFields.length > 0
				) {
					for (let item of this.watermarkFields) {
						if (!item.label || !item.field) continue
						if (
							typeof this.formData[item.field] === 'undefined' ||
							this.formData[item.field] === null
						) {
							this.showError(this.$constants.MSG.WARNING_LOST_LOCATION)
							watermarks = []
							return
						}
						watermarks.push({
							label: item.label,
							value: this.formData[item.field]
						})
					}
					// 添加时间
					watermarks.push({
						label: '时间',
						value: moment(new Date().getTime()).format('yyyy-MM-DD HH:mm:ss')
					})
				}

				const needWatermak = watermarks.length > 0
				// 图片加水印
				if (needWatermak) {
					try {
						const watermarkedFile = await WaterMarker.watermark(
							file,
							watermarks,
							this,
							this.field
						)
						file.url = watermarkedFile
						file.thumb = watermarkedFile
					} catch (err) {
						console.log('图片打水印失败', err)
					}
				}
				// 将文件从临时目录移动到指定缓存目录，并重命名
				try {
					const newFilePath = await LocalFile.copyTo(
						file.url,
						fileName,
						this.$constants.PATH.FILE_CACHE_PATH
					)
					file.url = newFilePath
					file.thumb = newFilePath
				} catch (err) {
					console.log('文件转移失败', err)
				}
				// 将app专有目录的图片拷贝到相册备份
				if (needSaveToAlbum) {
					uni.saveImageToPhotosAlbum({
						filePath: file.url,
						fail: err => {
							console.log('图片保存到相册失败', err)
						}
					})
				}
				// 更新状态
				this.fileList.push({
					...file,
					status: 'uploading',
					message: this.$constants.MSG.UPLOADING
				})
				
				let fileIndex = this.fileList.length - 1

				try{
					const result = await Upload.upload(file, this.path)
					this.fileList.splice(fileIndex, 1, {
						...file,
						path: result,
						status: 'success',
						message: ''
					})
					const value = this.genValueFromFileList()
					this._setValue(value, false)
				} catch(e) {
					console.log('文件上传失败:', e)
					this.showError(this.$constants.MSG.UPLOAD_PICTURE_FAIL)
					this.fileList.splice(fileIndex, 1)
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.gt-picture-field {
		width: 100%;

		.u-upload {
			//align-items: center;
		}

		/deep/ .u-upload__button {
			margin: 0;
		}

		/deep/ .u-form-item__body {
			//align-items: center;
			.u-form-item__body__left {
				//width: 80px !important;
			}
		}
	}

	.label-at-bottom {
		/deep/ .u-form-item__body {
			flex-direction: column-reverse !important;
			align-items: center;
	
			.u-form-item__body__left {
				width: 100%;
				padding-top: 8px;
	
				.u-form-item__body__left__content__label {
					flex: none;
				}
			}
		}
	}

	/deep/ .u-upload__deletable{
		background-color: #dd524d;
		 width: 18px;
		 height: 18px;

		 .uicon-close{
			 font-size: 18px !important;
			 line-height: 12px !important;
			 top: 2px !important;
			 right: -2px !important;
		 }
	}
	.example{
		.label{
			font-size: 28rpx;
			margin-bottom: 10rpx;
		}
	}

	.gt-text-tips {
		font-size: 24rpx;
		color: #dd524d;
		position: relative;
		top: -10rpx;
	}
</style>
