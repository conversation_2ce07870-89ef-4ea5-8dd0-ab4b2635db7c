import Storage from '@/tools/Storage'

/**
 * 共享数据操作
 */
export default class Share {

  constructor(expression) {
		if(!expression) return
    const { read, write } = expression
		this.readExp = read
		this.writeExp = write
  }

	/**
	 * 从共享数据池读取数据，存储到shareDataPool(组件内部存储获取到的共享数据)
	 * @param {Object} shareDataPool
	 */
  read(shareDataPool){
		if(!this.readExp || !shareDataPool) return
		const shareKey = this.readExp[0];
		const dataKey = this.readExp[1];
		const shareData = Storage.getShareData(shareKey)
		shareDataPool[dataKey] = shareData
	}
	
	/**
	 * 向共享数据池写入数据
	 * @param {Object} data
	 */
	write(data){
		if(!this.writeExp) return
		const shareKey = this.writeExp[0];
		Storage.setShareData(shareKey, data)
	}
	
	/**
	 * 移除共享数据
	 */
	clear(){
		if(!this.writeExp) return
		const shareKey = this.writeExp[0];
		Storage.removeShareData(shareKey)
	}
}
