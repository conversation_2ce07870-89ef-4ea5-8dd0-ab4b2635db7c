import Comparator from "./Comparator"
import createComparator from "./Factory"

export default class AllComparator extends Comparator {
  constructor( expression, formData, parentFormData ) {
    super( expression, formData, parentFormData )
  }

  compare () {
    let res = true
    this.expression.forEach( ( item, index ) => {
      if ( !res ) return
      if ( index > 0 ) {
        res = createComparator.createComparator( item, this.formData, this.parentFormData ).compare()
      }
    } )
    return res
  }
}
