import Comparator from "./Comparator"

export default class ContainsComparator extends Comparator {
  constructor( expression, formData, parentFormData ) {
    super( expression, formData, parentFormData )
  }

  compare() {
    if (!this.formData) return false;
    const field = this.expression[1];
    let value = this.expression.slice(2);
    const fieldValue = this.getCompareValue(field);
    if (!fieldValue || !Array.isArray(fieldValue)) return false;
    return value.some((item) => fieldValue.indexOf(item) >= 0);
  }
}
