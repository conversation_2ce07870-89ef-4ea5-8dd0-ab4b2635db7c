
const PARENT_FORM_DATA_KEY = 'parentFormData.'

export default class Comparator {
  /**
   *
   * @param {*} expression   计算表达式
   * @param {*} formData     form表单数据
   */
  constructor(expression, formData, parentFormData) {
    if (!expression) return;
    // expression 数组长度小于3则表达式不完整
    if (!Array.isArray(expression) || expression.length < 3) return;
    this.expression = expression;
    this.formData = formData;
		this.parentFormData = parentFormData;
  }

  /**
   * 根据表达式计算
   */
  compare() {
    console.warn("Concrete Compare should override calc function");
  }
	
	/**
	 * 获取比较值
	 */
	getCompareValue(key){
		if(!key) return
		if(key.indexOf(PARENT_FORM_DATA_KEY) === 0 && this.parentFormData){
			const field = key.substr(PARENT_FORM_DATA_KEY.length)
			return this.parentFormData[field]
		}
		return this.formData[key]
	}
}
