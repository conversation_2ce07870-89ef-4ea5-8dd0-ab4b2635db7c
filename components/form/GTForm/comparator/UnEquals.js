import Comparator from "./Comparator";

export default class UnEqualsComparator extends Comparator {
  constructor(expression, formData, parentFormData) {
    super(expression, formData, parentFormData);
  }

  compare() {
    if (!this.formData) return false;
    const field = this.expression[1];
    const value = this.expression[2];
    const fieldValue = this.getCompareValue(field);
    return value !== fieldValue;
  }
}
