import Calculator from "./Calculator";
import Factory from "./Factory";
export default class PlusCalculator extends Calculator {
  constructor(expression, formData, fieldData) {
    super(expression, formData, fieldData);
  }

  /**
   * 根据表达式计算
   */
  calc() {
    // 获取所有参与计算的字段名称
    const fields = this.expression.slice(1);
    // 从formData中获取字段值
    const values = fields.map((v) => this.getValue(v));
    // 字段值加和
    // null会作为0参与计算
    const array = values.filter((num) => !isNaN(num) && num !== null);
    return array.length > 0 ? array.reduce((acr, cur) => acr + cur) : undefined;
  }

  getValue(key) {
    if (!key) return;
    // 如果是数组视为表达式，通过计算器计算
    if (Array.isArray(key)) {
      const calculator = Factory.createCalculator(key, this.formData);
      if (!calculator) return;
      return calculator.calc();
    }
    // 如果不是数组视为字段名称，从fromData中获取
    return this.formData[key];
  }

  getFields() {
    if (!this.expression || !Array.isArray(this.expression)) return;
    const items = this.expression.slice(1);
    let fields = [];
    items.forEach((item) => {
      if (Array.isArray(item)) {
        const calculator = Factory.createCalculator(item, this.formData);
        if (calculator) {
          fields.push(...calculator.getFields());
        }
      } else {
        fields.push(item);
      }
    });
    return fields;
  }
}
