import Calculator from "./Calculator";
import Factory from "./Factory";
export default class SumCalculator extends Calculator {
  constructor(expression, formData, fieldData) {
    super(expression, formData, fieldData);
  }

  /**
   * 用于对子表求和
   * 1. 对子表的单个字段求和
   * ["sum", "sub_form", "field"]
   * 2. 对子表字段进行计算之后再求和
   * ["sum", "sub_form", ["*", "field1", "field2"]]
   */
  calc() {
    // 获取子表单名称
    const subForm = this.expression[1];
    // 从formData中获取子表单的值
    const subFormValue = this.formData[subForm];
    if (!subFormValue) return;
    const dataList = subFormValue.data;
    if (!dataList) return;
    // 对子表字段进行计算
    const sumItem = this.expression[2];
    let sum = 0;
    if (Array.isArray(sumItem)) {
      sum = this.getSumByCalculator(dataList, sumItem);
    } else {
      sum = this.getSumByField(dataList, sumItem);
    }
    return parseFloat((sum / dataList.length).toFixed(2));
  }

  // 从子表中获取字段值加和
  getSumByField(dataList, field) {
    if (!field || !dataList || !Array.isArray(dataList)) return;
    const dataItems = dataList
      .map((d) => d[field])
      .filter((d) => !isNaN(d) && d !== null);
    return dataItems.length > 0 ? dataItems.reduce((acr, cur) => acr + cur) : 0;
  }

  // 对子表中的单条记录按照公司计算，将计算结果加和
  getSumByCalculator(dataList, expression) {
    if (
      !dataList ||
      !Array.isArray(dataList) ||
      !expression ||
      !Array.isArray(expression)
    )
      return;
    let dataItems = dataList.map((item) => {
      const calc = Factory.createCalculator(expression, item);
      if (calc) {
        return calc.calc();
      }
    });
    dataItems = dataItems.filter((d) => !isNaN(d) && d !== null);
    return dataItems.length > 0 ? dataItems.reduce((acr, cur) => acr + cur) : 0;
  }

  getFields() {
    if (!this.expression || !Array.isArray(this.expression)) return;
    return [this.expression[1]];
  }
}
