import PlusCalculator from "./Plus"
import MultiplyCalculator from "./Multiply"
import ConcatCalculator from "./Concat"
import SumCalculator from "./Sum"
import GetCalculator from "./Get"
import SumMean from "./SumMean"

const CALCULATOR_TYPES = {
  PLUS: "+",
  MULTIPLY: "*",
  CONCAT: "concat",
  SUM: "sum",
  GET: "get",
  SUM_MEAN: "sum_mean",
}


const createCalculator = (expression, formData, fieldData) => {
  if (
    !expression ||
    !Array.isArray(expression) ||
    expression.length < 3 ||
    !formData
  )
    return;
  const type = expression[0];
  switch (type) {
    case CALCULATOR_TYPES.PLUS:
      return new PlusCalculator(expression, formData, fieldData);
    case CALCULATOR_TYPES.MULTIPLY:
      return new MultiplyCalculator(expression, formData, fieldData);
    case CALCULATOR_TYPES.CONCAT:
      return new ConcatCalculator(expression, formData, fieldData);
    case CALCULATOR_TYPES.SUM:
      return new SumCalculator(expression, formData, fieldData);
    case CALCULATOR_TYPES.GET:
      return new GetCalculator(expression, formData, fieldData);
    case CALCULATOR_TYPES.SUM_MEAN:
      return new SumMean(expression, formData, fieldData);

  }
};

export default { createCalculator };

