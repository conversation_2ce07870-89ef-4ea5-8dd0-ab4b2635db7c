export default class Calculator {
  /**
   *
   * @param {*} expression   计算表达式
   * @param {*} formData     form表单数据
   * @param {*} fieldData     字段数据，通常是字段发生变化时，FIELD_CHANGE事件中携带的数据，包含了该字段的所有信息，比如下拉框中携带了label、code之外的信心
   */
  constructor(expression, formData, fieldData) {
    if (!expression || !formData) return;
    // expression 数组长度小于3则表达式不完整
    if (!Array.isArray(expression) || expression.length < 3) return;
    this.expression = expression;
    this.formData = formData;
    this.fieldData = fieldData;
  }

  /**
   * 根据表达式计算
   */
  calc() {
    console.warn("Concrete Calculator should override calc function");
  }

  /**
   * 获取字段列表
   */
  getFields() {
    console.warn("Concrete Calculator should override getFields function");
  }
}
