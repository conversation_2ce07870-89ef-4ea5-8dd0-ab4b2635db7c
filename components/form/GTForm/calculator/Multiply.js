import Calculator from "./Calculator";
export default class MultiplyCalculator extends Calculator {
  constructor(expression, formData, fieldData) {
    super(expression, formData, fieldData);
  }

  /**
   * 根据表达式计算
   */
  calc() {
    // 获取所有参与计算的字段名称
    const fields = this.expression.slice(1);
    // 从formData中获取字段值
    const values = fields.map((v) => this.getValue(v));
    // 字段值加和
    const array = values.filter((num) => !isNaN(num) && num !== null);
    if (values.length !== array.length) {
      return;
    }
    return array.length > 0 ? array.reduce((acr, cur) => acr * cur) : undefined;
  }

  getValue(key) {
    return typeof key === "number" && !isNaN(key) ? key : this.formData[key];
  }

  getFields() {
    if (!this.expression || !Array.isArray(this.expression)) return;
    return this.expression
      .slice(1)
      .filter((item) => !(typeof item === "number" && !isNaN(item)));
  }
}
