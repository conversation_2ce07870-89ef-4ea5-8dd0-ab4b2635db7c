import Validator from "./Validator";
export default class TextRangeValidator extends Validator {
  constructor(fieldLabel, def, formData) {
    super(fieldLabel, def, formData);
  }

  /**
   * 生成校验规则
   */
  getRule() {
    const { min, max } = this.def?.options;
    return {
      type: "string",
      min,
      max,
      message:
        this.def?.options?.message ||
        `请输入正确的${this.fieldLabel}，长度在 ${min} 到 ${max} 之间`,
    };
  }
}
