export default class Validator {
  /**
   *
   * @param {*} fieldLabel    字段标题
   * @param {*} def          校验器定义
   * @param {*} formData     表单数据
   */
  constructor(fieldLabel, def, formData) {
    if (!fieldLabel || !def || !formData) return;
    this.fieldLabel = fieldLabel;
    this.def = def;
    this.formData = formData;
  }

  /**
   * 生成校验规则
   */
  getRule() {
    console.warn("Concrete Validator should override genRule function");
  }
}
