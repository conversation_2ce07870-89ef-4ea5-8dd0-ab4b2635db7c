import CalculatorFactory from "../calculator/Factory";
import Validator from "./Validator";
export default class CompareValidator extends Validator {
  constructor(fieldLabel, def, formData) {
    super(fieldLabel, def, formData);
  }

  /**
   * 生成校验规则
   */
  getRule() {
    const { expression } = this.def?.options;
    if (!expression || !Array.isArray(expression) || expression.length < 3)
      return;
    const operator = expression[0],
      valueExpression = expression[1],
      compareExpression = expression[2];
    return {
      message: this.def?.options?.message || `请输入正确的${this.fieldLabel}`,
      validator: () => {
        const value = this.getValue(valueExpression);
        const compareValue = this.getValue(compareExpression);
        if (isNaN(value) || isNaN(compareValue)) return true;
        switch (operator) {
          case ">=":
            return value >= compareValue;
          case ">":
            return value > compareValue;
          case "==":
            return value == compareValue;
          case "<=":
            return value <= compareValue;
          case "<":
            return value < compareValue;
          case "!=":
            return value != compareValue;
        }
      },
    };
  }

  getValue(expression) {
    if (!expression) return;
    if (Array.isArray(expression)) {
      const calculator = CalculatorFactory.createCalculator(
        expression,
        this.formData
      );
      if (!calculator) return;
      return calculator.calc();
    }
    return this.formData[expression];
  }
}
