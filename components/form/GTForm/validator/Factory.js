import Required<PERSON><PERSON><PERSON><PERSON> from "./Required"
import NumberRangeValidator from "./NumberRange"
import TextRangeValidator from "./TextRange"
import RegExpValidator from "./RegExp"
import CompareValidator from "./Compare"
import CountValidator from "./Count"

const VALIDATOR_TYPES = {
  REQUIRED: "required",
  NUMBER_RANGE: "number-range",
  TEXT_RANGE: "text-range",
  REGEX: "regex",
  COMPARE: "compare",
  COUNT: "count"
}

const createValidator = ( fieldLabel, validator, formData ) => {
  if ( !fieldLabel || !validator || !formData ) return
  switch ( validator.type ) {
    case VALIDATOR_TYPES.REQUIRED:
      return new RequiredValidator( fieldLabel, validator )
    case VALIDATOR_TYPES.NUMBER_RANGE:
      return new NumberRangeValidator( fieldLabel, validator, formData )
    case VALIDATOR_TYPES.TEXT_RANGE:
      return new TextRangeValidator( fieldLabel, validator, formData )
    case VALIDATOR_TYPES.REGEX:
      return new RegExpValidator( fieldLabel, validator, formData )
    case VALIDATOR_TYPES.COMPARE:
      return new CompareValidator( fieldLabel, validator, formData )
    case VALIDATOR_TYPES.COUNT:
      return new CountValidator(fieldLabel, validator, formData)
  }
}

export default { createValidator }
