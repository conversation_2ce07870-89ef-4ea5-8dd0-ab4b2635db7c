import Validator from "./Validator"
export default class CountValidator extends Validator {
  constructor( fieldLabel, def, formData ) {
    super( fieldLabel, def, formData )
  }

  /**
   * 生成校验规则
   */
  getRule () {
    const { count } = this.def?.options
    return {
      message: this.def?.options?.message || `请补充${ this.fieldLabel }，至少${count}条`,
      trigger: ['blur', 'change'],
      validator ( rule, value ) {
        return value.data.length >= count
      }
    }
  }
}
