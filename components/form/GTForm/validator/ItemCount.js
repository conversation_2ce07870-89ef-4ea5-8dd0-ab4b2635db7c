import Validator from "./Validator"
export default class TextRangeValidator extends Validator {
  constructor( fieldLabel, def, formData ) {
    super( fieldLabel, def, formData )
  }

  /**
   * 生成校验规则
   */
  getRule () {
    const { min, max, size, message } = this.def?.options || {}
    if ( size ) {
      return {
        required: true,
        trigger: ['blur', 'change'],
        message: message ?? `${ this.fieldLabel }数据条数为${ size }条`,
        validator ( rule, value ) {
          return value.data.length == size
        }
      }
    } else {
      return {
        required: true,
        trigger: ['blur', 'change'],
        message: message ?? `${ this.fieldLabel }数据条数在${ min }-${ max }之间`,
        validator ( rule, value ) {
          return value.data.length <= max && value.data.length >= min
        }
      }
    }
  }
}
