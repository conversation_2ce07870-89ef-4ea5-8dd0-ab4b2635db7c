import ComparatorFactory from "./GTForm/comparator/Factory";
import CalculatorFactory from "./GTForm/calculator/Factory";
import Share from "./GTForm/share/Share";
import Storage from "@/tools/Storage";

const VALUE_PLACEHOLDER_REGEXP = /\{.+?\}/g;

const VALUE_PLACEHOLDER = {
  DATE_NOW: "now",
  USER_NICKNAME: "user.nickname",
  USER_CONTACT: "user.contact",
};

export default {
  // 注入表单级EventBus
  inject: ["bus", "parentFormData"],

  props: {
    options: {
      type: Object,
    },
  },

  data() {
    return {
      value: undefined,
      customUnit: undefined,
      visible: true,
      shareDataPool: {},
    };
  },

  computed: {
    required() {
      return this.options.required === true;
    },

    field() {
      return this.options.field;
    },

    label() {
      return this.options.label;
    },

    unit() {
      if (!this.options.unit) return;
      const match = this.options.unit.match(/\{(.+?)\}/g);
      // 如果没有匹配{xxx}则返回原始内容作为标题
      if (match) {
        const field = `${match[0]}`.substr(1, match[0].length - 2);
        if (this.formData[field]) {
          return this.options.unit.replace(match[0], this.formData[field]);
        } else {
          return "";
        }
      }
      return this.customUnit || this.options.unit;
    },

    placeholder() {
      return this.options.placeholder || `请输入${this.options.label}`;
    },

    readonly() {
      return this.options.readonly;
    },

    depends() {
      return this.options.depends;
    },

    calculator() {
      return this.options.calculator;
    },

    formData() {
      return this.options.formData;
    },

    default() {
      return this.options.default;
    },

    share() {
      return this.options.share;
    },

    primaryColor() {
      return this.$constants.COLOR.PRIMARY_COLOR;
    },
  },

  mounted() {
    if (this.formData && this.field) {
      // 如果formData有field对应的值则对组件进行初始化
      if (this.field in this.formData) {
        // 初始化赋值时，无需派发数据变化事件
        this.setValue(this.formData[this.field]);
      } else if (this.default) {
        this.setValue(this.default);
        this.updateFormData();
      } else {
        this.$set(this.formData, this.field, undefined);
      }
    }
    // 监听整个表单字段值变化事件
    this.bus.$on(this.$events.form.FIELD_CHANGE, this.formChangeHandler);
    // 派发已完成初始化事件
    this.bus.$emit(this.$events.form.FIELD_INITIALIZED, {
      field: this.field,
      fieldRef: this,
    });

    if (this.depends) {
      this.updateVisibility();
    }

    // 处理共享数据
    if (this.share) {
      this.readShareData();
    }
  },

  beforeDestroy() {
    if (this.share) {
      this.clearShareData();
    }
  },

  methods: {
    // 更新表单值
    updateFormData() {
      const value = this.getValue();
      // 派发字段值变化事件
      this.bus.$emit(this.$events.form.FIELD_CHANGE, {
        field: this.field,
        value,
      });
      // 处理共享数据
      if (this.share) {
        this.writeShareData();
      }
    },

    //
    formChangeHandler(payload) {
      const { field, data, value } = payload;
      this.formData[field] = value;
      // 处理deponds逻辑
      if (this.depends && this.fieldInDepands(field, this.depends)) {
        this.updateVisibility();
      }

      if (this.calculator) {
        this.calcValue(field, data);
      }
      this._formChangeHandler(payload);
    },

    _formChangeHandler() {
      // 子类按需实现其他对表单数据变化的响应，比如SelectField中对数据进行过滤
    },

    fieldInDepands(field, depends) {
      let res = false;
      for (let i = 0; i < depends.length; i++) {
        if (Array.isArray(depends[i])) {
          // 判断是否为多维数组
          res = this.fieldInDepands(field, depends[i]);
          if (res) return true;
        } else if (depends[i] === field) return true;
      }
      return res;
    },

    // 自动显示或隐藏
    updateVisibility() {
      const comparator = ComparatorFactory.createComparator(this.depends, this.formData, this.parentFormData);
      this.visible = comparator.compare();
      if (this.formData && this.visible === false) {
        this._invisibleHandler();
      }
      this.$set(this.options, "visible", this.visible);
    },

    _invisibleHandler() {
      delete this.formData[this.field];
    },

    // 自动计算
    calcValue(field, fieldData) {
      // 处理自动计算的逻辑
      const calc = CalculatorFactory.createCalculator(this.calculator, this.formData, fieldData);
      if (!calc) return;
      const fields = calc.getFields();
      if (!fields) return;
      // 判断field是否在计算器表达式内
      if (fields.indexOf(field) >= 0) {
        const value = calc.calc();
        this.setValue(value, false);
      }
    },

    // 获取值
    getValue() {
      // 不给formData赋undefined，向后端发请求时会被删除
      return this.value === undefined || this.value === "" ? null : this.value;
    },

    // 设置值
    setValue(value, silently = true) {
      this.value = value === null ? undefined : value;
      if (!silently) this.updateFormData();
      // 处理共享数据
      this.writeShareData();
    },

    // 设置单位
    setUnit(unit) {
      this.customUnit = unit;
    },

    // 重置
    reset() {
      this.value = undefined;
    },

    // 生成默认值
    genPlaceholderValue(placeholder) {
      if (typeof placeholder === "undefined" || placeholder === null) return;
      const items = `${placeholder}`.match(VALUE_PLACEHOLDER_REGEXP);
      if (!items) return placeholder;
      const key = items[0].substr(1, items[0].length - 2);
      switch (key) {
        case VALUE_PLACEHOLDER.DATE_NOW:
          return new Date();
          break;
        case VALUE_PLACEHOLDER.USER_NICKNAME:
        case VALUE_PLACEHOLDER.USER_CONTACT:
          return Storage.getUserInfo(key);
          break;
      }
    },

    writeShareData() {
      if (!this.share) return;
      const share = new Share(this.share);
      if (share) {
        share.write(this.getValue());
      }
    },

    readShareData() {
      if (!this.share) return;
      const share = new Share(this.share);
      if (share) {
        share.read(this.shareDataPool);
      }
    },

    clearShareData() {
      if (!this.share) return;
      const share = new Share(this.share);
      if (share) {
        share.clear();
      }
    },

    showError(msg) {
      uni.showToast({
        title: msg,
        icon: "error",
        duration: 2000,
      });
    },
  },
};
