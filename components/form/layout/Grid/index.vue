<template>
  <view class="form-group-wrapper">
    <u-grid :border="false" :col="options.columns">
      <u-grid-item
        v-for="(component, index) in options.children"
        v-if="!component.options || component.options.visible !== false"
        :key="index"
      >
        <component
          :is="component.type"
          :options="component.options"
          :key="index"
        />
      </u-grid-item>
    </u-grid>
  </view>
</template>

<script>

import BaseLayout from '@/components/form/BaseLayout'

export default {
	
  name: 'Grid',
	
	mixins: [ BaseLayout ]
	
}

</script>

<style lang="less" scoped>
.form-group-wrapper {
  border: 1px dashed #ccc;
  margin: 4px -8px;
}
/deep/.u-grid-item {
  padding: 0 20rpx;
}
/deep/.u-grid-item--hover-class {
  opacity: 1;
}
</style>