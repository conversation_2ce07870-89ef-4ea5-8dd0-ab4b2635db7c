<template>
  <u-collapse-item :title="options.title" :name="options.title" icon="list-dot">
    <view class="content">
      <component
        v-if="!component.options || component.options.visible !== false"
        v-for="(component, index) in options.children"
        :is="component.type"
        :options="component.options"
        :key="index"
      />
    </view>
  </u-collapse-item>
</template>

<script>
	
import BaseLayout from '@/components/form/BaseLayout'

export default {
	
  name: 'CollapseItem',
	
	mixins: [ BaseLayout ],
	
  computed: {
		
    title() {
      return this.options.title
    }
		
  }
}
</script>

<style lang="less" scoped>
</style>