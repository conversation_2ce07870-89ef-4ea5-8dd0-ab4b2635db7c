<template>
  <view class="form-group-wrapper">
    <u-collapse
      :value="currentValue"
      accordion
      @change="change"
      ref="collapse">
      <CollapseItem
        v-for="(component, index) in options.children"
        v-if="!component || component.visible !== false"
        :options="component"
        :key="index"
      />
    </u-collapse>
  </view>
</template>

<script>
	
import CollapseItem from './CollapseItem'
import BaseLayout from '@/components/form/BaseLayout'

export default {
	
  name: 'Collapse',
	
	mixins: [ BaseLayout ],

  components: {
		CollapseItem
	},
	
  data() {
    return {
      visible: true,
      currentValue: this.options.children[0].title
    }
  },
	
  computed: {
    title() {
      return this.options.title
    }
  },

  methods: {

    change(e) {
      this.currentValue = e.find(item => item.status == 'open')?.name
    }
		
  }
}
</script>

<style lang="less" scoped>
</style>