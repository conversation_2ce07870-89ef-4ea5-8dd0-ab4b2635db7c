<template>
	<view class="gt-daterange-field">
		<u-form-item :label="label" :prop="startField" labelWidth="auto" :required="required" @click="handleFocus">
			<u-input disabled disabledColor="#ffffff" border="surround" :placeholder="placeholder" v-model="textValue">
			</u-input>
			<u-icon slot="right" size="22" color="#ccc" name="calendar"></u-icon>
		</u-form-item>
		<u-calendar v-if="calendarCreated" :show="show" :mode="mode" @close="handleClose" :defaultDate="value" @confirm="handleConfirm">
		</u-calendar>
	</view>
</template>

<script>
	import moment from 'moment'
	import BaseField from '@/components/form/BaseField'

	export default {
		
		name: 'DateRangeField',

		mixins: [BaseField],

		data() {
			return {
				value: undefined,
				textValue: undefined,
				show: false,
				mode: 'range',
				calendarCreated: false
			}
		},

		computed: {
			placeholder() {
				return `请选择${this.label}`
			},
			startField() {
				return this.options.startField.field
			},
			endField() {
				return this.options.endField.field
			},
			type() {
				return this.options.mode
			}
		},

		mounted() {
			if (this.formData && this.startField && this.endField) {
				// 如果formData有field对应的值则对组件进行初始化
				if (this.startField in this.formData || this.endField in this.formData) {
					this.setValue(
						this.formData[this.startField],
						this.formData[this.endField]
					)
				}
				// 否则赋空值
				else {
					this.$set(this.formData, this.startField, undefined)
					this.$set(this.formData, this.endField, undefined)
				}
			}
			// 监听整个表单字段值变化事件
			this.bus.$on(this.$events.form.FIELD_CHANGE, this.formChangeHandler)
		},

		methods: {
			handleFocus() {
				if (this.readonly) return
				this.show = true
				this.calendarCreated = true
			},

			handleClose() {
				this.show = false
			},

			handleConfirm(e) {
				this.show = false
				this.value = e
				this.textValue = e[0] + ' ~ ' + e[e.length - 1]
				this.rangeUpdateFormData(this.startField, e[0])
				this.rangeUpdateFormData(this.endField, e[e.length - 1])
			},

			setValue(start, end) {
				if (start && end) {
					this.textValue = moment(start).format('YYYY-MM-DD') + ' ~ ' + moment(end).format(
						'YYYY-MM-DD')
				}
				this.value = [moment(start).format('YYYY-MM-DD'), moment(end).format('YYYY-MM-DD')]
			},

			rangeUpdateFormData(field, value) {
				let valueS = moment(value, 'YYYY-MM-DD').valueOf()
				this.$set(
					this.formData,
					field,
					!value ? undefined : value
				)
				this.bus.$emit(this.$events.form.FIELD_CHANGE, {
					field,
					value: valueS
				})
			}
		}
	}
</script>

<style scoped lang="less">

	/deep/.item__body__right__content__icon {
		position: absolute;
		right: 44rpx;
	}
</style>
