<template>
  <view class="gt-integer-field">
    <u-form-item :label="label" :prop="field" :required="required">
      <u-input
        ref="input"
        v-model="value"
        :placeholder="placeholder"
        @blur="blurHandler"
        @input="changeHandler"
        border="surround"
        clearable
        :suffixIcon="unit"
        type="number"
      >
      </u-input>
    </u-form-item>
  </view>
</template>

<script>
import BaseField from '@/components/form/BaseField'

export default {
	
  name: 'IntegerField',

  mixins: [BaseField],

  data() {
    return {
      value: null
    }
  },

  methods: {
    changeHandler(e) {
      this.$nextTick(() => {
        // const value = String(e).replace(/[^\d]/g, '')
        // this.value = isNaN(value) || value=== '' ? undefined : Number(value)
        const values = e.split('.')
        this.value = values[0]
      })
      this.updateFormData()

    },

    blurHandler() {
      this.updateFormData()
    },

    getValue() {
      return isNaN(this.value) || this.value === ''
        ? undefined
        : this.type === 'text'
        ? this.value
        : Number(this.value)
    }
  }
}
</script>
<style lang="less" scoped>
.gt-integer-field {
  width: 100%;
}
</style>