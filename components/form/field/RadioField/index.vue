<template>
  <view class="gt-radio-field">
    <u-form-item
      :label="label"
      :prop="field"
      :required="required"
      labelWidth="auto"
    >
      <u-radio-group
        v-model="value"
        placement="row"
        @change="changeHandler"
        :disabled="readonly"
      >
        <template v-for="(item, index) in items">
          <u-radio
            :label="item.label"
            :name="item.value"
            :key="index"
						:activeColor="primaryColor"
          ></u-radio>
        </template>
      </u-radio-group>
    </u-form-item>
  </view>
</template>

<script>
	
import BaseField from '@/components/form/BaseField'

export default {
	
  name: 'RadioField',

  mixins: [BaseField],

  data() {
    return {
      value: null,
      items: []
    }
  },

  mounted() {
    this.init()
  },

  methods: {
		
    async init() {
      this.items = await this.$utils.dict.parse(this.options.dict)
    },

    changeHandler() {
      this.updateFormData()
    }
  }
}
</script>

<style lang="less" scoped>
.gt-radio-field {
  width: 100%;
}

/deep/.u-radio-group--row {
  flex-wrap: wrap;
}
</style>