<template>
	<view class="gt-select-field">
		<u-form-item :label="label" :prop="field" :required="required" labelWidth="auto" @click="clickHandler">
			<u-input border="surround" :placeholder="placeholder" v-model="valueLabel" disabled
				:disabledColor="readonly ? '#f5f7fa' : '#ffffff'">
			</u-input>
			<u-icon slot="right" name="arrow-down"></u-icon>
		</u-form-item>
		<view class="picker-search-wrapper" v-if="pickerVisible && search">
			<u-input disabledColor="#ffffff" :placeholder="placeholder" v-model="searchText" @confirm="searchHandler">
				<template slot="suffix">
					<view @click="searchHandler">
						<u-icon name="search" size="22px" color="#909399" @click="searchHandler"></u-icon>
					</view>
				</template>
			</u-input>
		</view>
		<u-picker v-if="pickerCreated" :show="pickerVisible" :showToolbar="true" :defaultIndex="defaultIndex" :title="label" :columns="columns" keyName="label"
			confirmColor="$constants.COLOR.PRIMARY_COLOR" :closeOnClickOverlay="true"
			@cancel="pickerCancelHandler"
			@confirm="pickerConfirmHandler"
			@close="pickerCancelHandler">
		</u-picker>
	</view>
</template>

<script>
	import BaseField from '@/components/form/BaseField'

	export default {

		name: 'SelectField',

		mixins: [BaseField],

		data() {
			return {
				items: undefined,
				itemsMap: undefined,
				columns: undefined,
				defaultIndex: [0],
				value: undefined,
				valueLabel: undefined,
				pickerCreated: false,
				pickerVisible: false,
				searchText: undefined,
				// 过滤字段
				filterField: undefined,
				// 关注的字段
				focusField: undefined,
				// 过滤器的值
				filterValue: undefined
			}
		},

		computed: {

			placeholder() {
				return `请选择${this.label}`
			},

			search() {
				return this.options.showSearch
			},

			filter() {
				return this.options.filter
			}
			
		},

		async mounted() {
			this.getFilter()
			await this.initDict()
		},

		methods: {
			
			getFilter(){
				if ((!this.filter && !Array.isArray(this.filter)) || this.filter.length < 3) return
				const operator = this.filter[0]
				if (operator !== "=") return
				this.filterField = this.filter[1]
				this.focusField = this.filter[2]
				// 初始时从formData中获取依赖的字段值
				this.filterValue = this.formData[this.focusField]
			},

			async initDict() {
				this.options.lazy === true ? await this.lazyLoadDict() : await this.loadDict()
			},
			
			getColumns(){
				// 如果定义了filter，且有过滤值，则进行过滤
				if (this.filterValue !== null && this.filterValue !== undefined) {
					const items = this.items.filter(
						(item) => item.data[this.filterField] === this.filterValue
					)
					return [ items ]
				} else if (this.filterField) {
					// 如果定义了filter，没有过滤值，则置空
					return [ ]
				} else {
					return [ this.items ]
				}
			},
			
			// 直接加载字典
			async loadDict(){
				this.items = await this.$utils.dict.parse(this.options.dict)
				this.columns = this.getColumns()
				this.setLabelAndIndex()
			},
			
			// 字典懒加载
			async lazyLoadDict(){
				if(this.filterValue !== null && this.filterValue !== undefined){
					const filter = ['=', this.filterField, this.filterValue]
					this.items = await this.$utils.dict.parse(this.options.dict, filter)
					this.columns = [ this.items ]
				}else{
					this.items = this.columns = []
				}
				this.setLabelAndIndex()
			},
			
			// 获取字典数据后处理
			setLabelAndIndex(){
				if(!this.items) return
				this.itemsMap = this.$utils.array.toMap(this.items, 'value')
				// 如果有初始值，选中对应的数据
				const selectedItem = this.itemsMap[this.value]
				if(selectedItem){
					// 设置显示label
					this.valueLabel = selectedItem.label
					// 选中数据
					this.defaultIndex = [ selectedItem.__index__ ]
				} else {
					this.valueLabel = undefined
					this.defaultIndex = [0]
				}
			},

			clickHandler() {
				if (this.readonly) return
				this.pickerVisible = true
				this.pickerCreated = true
			},

			pickerConfirmHandler(e) {
				// 记录选中
				this.defaultIndex = e.indexs
				// 设置显示label和值
				const selectedItem = e.value && e.value[0]
				this.valueLabel = selectedItem?.label
				this.setValue(selectedItem?.value)
				this.updateFormData()
				//
				this.resetPicker()
			},

			pickerCancelHandler() {
				this.resetPicker()
			},

			// 重置
			resetPicker(){
				this.pickerVisible = false
				this.searchText = undefined
				// 恢复数据列表
				this.columns = this.getColumns()
			},

			updateFormData() {
				const value = this.getValue()
				const data = !value ? undefined : this.itemsMap[value]
				// 派发字段值变化事件
				this.bus.$emit(this.$events.form.FIELD_CHANGE, {
					field: this.field,
					value,
					data
				})
			},

			searchHandler() {
				if(!this.searchText){
					this.columns = [ this.items ]
					return
				}
				// 根据过滤文本对数据列表进行过滤
				const items = this.items.filter(item => {
					return item.label.indexOf(this.searchText) >= 0
				})
				this.columns = [ items ]
			},
			
			async _formChangeHandler({ field, value }) {
				if (!this.filterField || this.focusField !== field) return
				// 记录过滤器的值
				this.filterValue = value
				if(this.filterValue === null){
					// 如果过滤字段的值为空, 值清空
					this.value = undefined
				}
				// 对字典进行过滤
				if(this.options.lazy === true){
					await this.lazyLoadDict()
				}else{
					this.columns = this.getColumns()
				}
				//
				this.setLabelAndIndex()
				this.updateFormData()
			},
			
			// 设置值
			setValue ( value, silently = true) {
				this.value = value === null ? undefined : value;
				this.setLabelAndIndex()
				if(!silently) this.updateFormData()
			},
		}
	}
</script>

<style scoped lang="scss">

	@import '@/static/styles/common.scss';

	.gt-select-field {
		width: 100%;
	}

	.picker-chunk,
	.picker-content {
		width: 100%;
	}

	/deep/ .u-form-item__body__right {
		position: relative !important;
	}

	/deep/ .item__body__right__content__icon {
		position: absolute;
		right: 12px;

		.u-icon__icon {
			color: $gt-border-color !important;
		}
	}

	.picker-search-wrapper {
		position: fixed;
		bottom: 262px;
		left: 0;
		right: 0;
		z-index: 99999;
		background: #ffffff;

		.u-input {
			padding: 10px !important;
			border-radius: 0;
			border-left: none;
			border-right: none;
			border-top: none;
		}

	}
</style>
