<template>
  <view class="gt-subform-field">
    <u-form-item
      :label="label"
      :prop="field"
      :required="required"
      labelWidth="auto"
    >
    <view class="form-wrapper" v-for="(item, index) in dataList" :key="index">
			{{ item.物种名称 }}
			<GTForm :ref="index" :options="optionsList[index]"/>
		</view>
    </u-form-item>
  </view>
</template>

<script>
	
import BaseField from '@/components/form/BaseField'
import GTForm from '@/components/form/GTForm'

const DATA_LIST_KEY = '样方物种列表'

export default {
	
  name: 'TiledSubForm',

  mixins: [BaseField],
	
	components: {
		GTForm
	},
	
	data(){
		return {
			dataList: [],
			optionsList: undefined
		}
	},
	
	computed: {
		formUid() {
		  return this.options.formUid
		}
	},
	
	mounted(){
		this.initSubForm()
	},

  methods: {
		
		async initSubForm(){
			const formDef = await this.$apis.formDef.getFormDef(this.formUid)
			let dataList = this.shareDataPool[DATA_LIST_KEY]
			if(!dataList) return
			// 去重
			const dataMap = this.$utils.array.toMap(dataList, '物种编码')
			dataList = Object.values(dataMap)
			// 和初始化数据对比，生成最终数据
			const initValue = this.getValue()
			if(initValue && initValue.data){
				let initDataMap = this.$utils.array.toMap(initValue.data, '物种编码')
				for(let item of dataList){
					if(initDataMap[item.物种编码]){
						// 将已有的记录数据覆盖物种列表中的数据
						dataMap[item.物种编码] = initDataMap[item.物种编码]
					}
				}
				dataList = Object.values(dataMap)
			}
			// 生成子表单数据
			let optionsList = []
			dataList.forEach(item => {
				optionsList.push({
					formDef,
					formRecordData: item
				})
			})
			this.optionsList = optionsList
			this.dataList = dataList
		},
		
		async submit(){
			let validList = []
			let dataList = []
			const formRefs = Object.keys(this.$refs)
			for(let formKey of formRefs){
				const formRef = this.$refs[formKey][0]
				validList.push(await formRef.submit())
				dataList.push(formRef.getFormData())
			}
			const valid = validList.every(v => v)
			if(valid){
				// 将数据写入到formData
				this.setValue({
					data: dataList,
					form: this.formUid
				}, false)
			}
			return valid
		}
		
  }
}
</script>

<style lang="less" scoped>
	
.gt-subform-field {
	
  width: 100%;
	
	.form-wrapper{
		border: 1px dashed #ccc;
		padding: 8px;
		margin: 4px -8px 4px -8px;
		font-size: 15px;
	}
	
	/deep/ .u-form-item__body__right__content__slot{
		display: block;
	}
	
}
</style>