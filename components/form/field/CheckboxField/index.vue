<template>
  <view class="gt-checkbox-field">
    <u-form-item
      :label="label"
      :prop="field"
      :required="required"
      labelWidth="auto"
    >
      <u-checkbox-group
        v-model="value"
        @change="changeHandler"
        size="20"
      >
        <u-checkbox
          v-for="(item, index) in items"
          :key="index"
          :name="item.value"
          :disabled="readonly"
          :label="item.label"
					:activeColor="primaryColor">
        </u-checkbox>
      </u-checkbox-group>
    </u-form-item>
  </view>
</template>

<script>

import BaseField from '@/components/form/BaseField'

export default {

  name: 'CheckboxField',

  mixins: [ BaseField ],

  data() {
    return {
      value: undefined,
      items: [],
			itemsMap: {}
    }
  },

  async mounted() {
    await this.init()
  },

  methods: {

    async init() {
			// 获取字典
			this.items = await this.$utils.dict.parse(this.options.dict)
			if(this.items){
				this.itemsMap = this.$utils.array.toMap(this.items, 'value')
			}
		},

    change<PERSON><PERSON><PERSON>(value) {
      this.value = value
      this.updateFormData()
    },

		// 更新表单值
		updateFormData () {
			const value = this.getValue()
			const data = !value ? undefined : value.map( item => this.itemsMap[item].data)
			// 派发字段值变化事件
			this.bus.$emit( this.$events.form.FIELD_CHANGE, {
				field: this.field,
				value,
				data
			})
		},

    // 设置值
    getValue() {
      if (!this.value || this.value.length == 0) {
        return undefined
      }
      return this.value
    }

  }
}
</script>

<style lang="less" scoped>
.gt-checkbox-field {
  /deep/.u-checkbox-group {
    width: 100%;
  }
  /deep/.u-checkbox-group--row{
    flex-wrap: wrap;
    gap:20rpx;
  }
}
</style>
