<template>
  <view class="search-wrap">
    <view class="plus-select" v-if="searchConfig">
      <text>选择</text> <u-icon name="arrow-down" color="#ccc"></u-icon>
    </view>
    <u-search
    v-model="keyword"
      :placeholder="placeholder"
      clearabled
      @search="searchHandle"
      @custom="customHandle"
      @clear="clearHandle"
      @change="changeHandle"
    ></u-search>
  </view>
</template>

<script>
export default {
  data() {
    return {
      keyword: ""
    }
  },
  props: {
    value: String | Number,
    searchConfig: {
      type: Object
    },
    placeholder: {
      type: String
    }
  },
  mounted () {
    // TODO！ 组件重构
    this.setValue()
  },
  methods: {
    setValue () {
      this.keyword = this.value || ""
    },
    changeHandle(e) {
      this.$emit('change', e)
    },
    searchHandle(e) {
      this.$emit('search', e)
    },
    customHandle(e) {
      this.$emit('custom', e)
    },
    clearHandle(e) {
      this.keyword = ""
      this.$emit('clear', e)
    }
  }
}
</script>

<style lang="less" scoped>
.search-wrap {
  display: flex;
  padding: 20rpx 20rpx 0 20rpx;
  .plus-select {
    color: #ccc;
    display: flex;
    align-items: center;
    margin-left: -15rpx;
    margin-right: 15rpx;
  }
}
</style>