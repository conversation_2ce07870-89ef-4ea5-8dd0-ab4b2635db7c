<template>
  <view class="data-list">
    <view v-if="!data || data.length === 0">
      <u-empty text="没有数据" icon="/static/images/empty.png"></u-empty>
    </view>
    <view
      class="data-item"
      v-for="(item, index) of data"
      :key="index"
      @click="clickHandler(data)"
    >
      <view class="data-item-fields">
        <view
          class="data-item-field"
          v-for="(field, index) of fields"
          :key="index"
        >
          <view class="title">{{ field.label }}：</view>
          <view class="value">{{ setValue(item, field) }}</view>
        </view>
      </view>
      <view class="data-item-operations">
        <view
          class="btn-wrapper"
          :style="{ width: operationWidth }"
          v-if="operationShow(operation, item)"
          v-for="(operation, index) of operations"
          @click.stop="operationBtnClickHandler(operation.key, item)"
          :key="index"
        >
          <u-button :type="operation.type || 'primary'">{{
            operation.label
          }}</u-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 数据
    data: {
      type: Array,
    },
    // 显示字段
    fields: {
      type: Array,
    },
    // 操作按钮
    operations: {
      type: Array,
    },
    cache: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {};
  },

  computed: {
    operationWidth() {
      const count = this.operations.length;
      return `calc(${100 / count}% - ${((count - 1) * 6) / count}px)`;
    },
  },

  methods: {
    operationShow(operation, item) {
      if (!operation.depends) return true;
      const type = operation.depends[0];
      const field = operation.depends[1];
      const value = operation.depends[2];
      switch (type) {
        case "=":
          return item[field] == value;
        default:
          return true;
      }
    },
    setValue(item, field) {
      let value;
      if (this.cache) {
        value = item.content[field.name];
      } else {
        value = item[field.name];
      }

      switch (field.type) {
        case "text":
          return value;
        case "date":
          return this.$utils.date.format(value);
        default:
          return value;
      }
    },

    operationBtnClickHandler(key, item) {
      this.$emit(key, item);
    },

    clickHandler(item) {
      this.$emit(this.$events.list.ITEM_CLICK, item);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

.data-list {
  .data-item {
    border: 1px solid $gt-border-color;
    padding: 8px;
    border-radius: $gt-border-radius;
    background-color: #fff;

    &:not(:first-child) {
      margin-top: 8px;
    }

    .data-item-field {
      padding: 2px 0;

      .title {
        display: inline-block;
      }

      .value {
        display: inline-block;
      }
    }

    .data-item-operations {
      padding: 6px 0 2px 0;
      display: flex;
      column-gap: 6px;

      .btn-wrapper {
        width: 100%;
      }
      .u-button {
        height: $gt-btn-height;
      }
    }
  }
}
</style>
