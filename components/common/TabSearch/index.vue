<template>
  <view class="tab-search-content">
    <view class="tab-wrapper" v-if="showTab">
      <u-subsection
        class="subsection"
        :list="sectionList"
        :current="currentSection"
        @change="sectionChangeHandler"
        :activeColor="$constants.COLOR.PRIMARY_COLOR"
        :fontSize="fontSize"
      ></u-subsection>
    </view>
    <view class="search-wrapper" v-if="showSearch">
      <u-search
        v-model="keyword"
        :placeholder="`请输入${placeholder}`"
        clearabled
        @custom="customHandler"
        @clear="clearHandler"
      ></u-search>
    </view>
  </view>
</template>

<script>
const TAB_FILTER_CHANGE = "tabFilterChange";
const TAB_SEARCH_FILTER_CHANGE = "tabSearchFilterChange";

export default {
  name: "TabSearch",
  data() {
    return {
      keyword: null,
      currentSection: 0,
    };
  },
  mounted () {
    this.$emit(TAB_FILTER_CHANGE, this.tabFilter);
  },
  computed: {
    sectionList() {
      return (
        (Array.isArray(this.tabSectionList) &&
          this.tabSectionList.map((f) => f.section)) ||
        []
      );
    },
    placeholder() {
      return (
        (this.searchItem && Object.keys(this.searchItem).length > 0 && this.searchItem.value) ||
        this.searchItem?.key || "关键字"
      );
    },
    searchField() {
      return Object.keys(this.searchItem).length > 0 && this.searchItem.key;
    },
    searchFilter() {
      return this.searchField && this.showSearch && this.keyword
        ? ["like", this.searchField, `%${this.keyword}%`]
        : [];
    },
    tabFilter() {
      return Array.isArray(this.tabSectionList[this.currentSection]?.filter) &&
        this.showTab
        ? this.tabSectionList[this.currentSection].filter
        : this.tabSectionList[0].filter;
    },
  },
  props: {
    // 传入tab对应的字段的key,value数组
    tabSectionList: {
      type: Array,
      default: () => [],
    },
    // tab字号
    fontSize: {
      type: String | Number,
      default: () => 15,
    },

    // 搜索的键值对
    searchItem: {
      type: Object,
    },
    // 是否展示搜索框
    showSearch: {
      type: Boolean,
      default: () => true,
    },

    // 是否展示tab
    showTab: {
      type: Boolean,
      default: () => true,
    },
  },
  watch: {},
  methods: {
    // tab切换的响应
    sectionChangeHandler(index) {
      this.currentSection = index;
      this.clearHandler();
      this.$emit(TAB_FILTER_CHANGE, this.tabFilter);
    },

    // 清除搜索的响应
    clearHandler() {
      this.keyword = "";
    },
    // 搜索的响应
    customHandler() {
      const genFilter = this.getGenFilter();
      this.$emit(TAB_SEARCH_FILTER_CHANGE, genFilter);
    },
    getGenFilter() {
      let genFilter = [];
      if (Array.isArray(this.searchFilter) && !this.searchFilter.length > 0) {
        genFilter = [...this.tabFilter];
        return genFilter
      }
      if (Array.isArray(this.tabFilter) && !this.tabFilter.length > 0) {
        genFilter = [...this.searchFilter];
        return genFilter
      }
      if (Array.isArray(this.tabFilter[1]) && this.tabFilter[0] === "and") {
        genFilter.push(...this.tabFilter, this.searchFilter);
      } else {
        genFilter.push("and", this.tabFilter, this.searchFilter);
      }

      return genFilter;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

.tab-search-content {
  .tab-wrapper {
    // padding-top: 20rpx
  }

  .search-wrapper {
    display: flex;
    padding: 20rpx 20rpx 0 20rpx;
  }
}

/deep/ .u-subsection {
  height: 84rpx !important;
  background-color: #fff !important;
  .u-subsection--button {
  display: flex;
  justify-content: center;
  align-items: center;
}
}

/deep/ .u-subsection--button__bar{
  width: 3em !important;
  left: calc(25% - 1.5em) !important;
  bottom: 5rpx;
  height: 7rpx !important;
  background-color: $gt-primary-color !important;
}

</style>