<template>
  <view class="tab-search-list-wrapper">
    <TabSearch
      ref="tabSearch"
      class="tab-search-wrapper"
      :tabSectionList="tabSectionList"
      :fontSize="tabFontSize"
      :searchItem="searchItem"
      :showSearch="genShowSearch"
      :showTab="showTab"
      @tabFilterChange="tabFilterChangeHandler"
      @tabSearchFilterChange="tabSearchFilterChangeHandler"
    ></TabSearch>
    <view class="scroll-wrapper">
      <scroll-view
        scroll-y
        class="scroll-content"
        @scrolltolower="scrollToLowerHandler"
      >
        <SearchList
          ref="SearchList"
          class="search-list-wrapper"
          :style="{ marginTop: tabSearchHeight }"
          :data="dataList"
          :fields="fields"
          :showTab="showTab"
          :operations="operations"
          @buttonClickEvent="buttonClickEventHandler"
          @itemClick="itemClickHandler"
        ></SearchList>
        <view class="loading" v-if="dataList.length > 0">
          <u-loadmore :status="loadStatus" />
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import TabSearch from "@/components/common/TabSearch";
import SearchList from "@/components/common/SearchList";
import BaseDataList from "@/components/common/BaseDataList";

const PAGE_PARAM = {
  pageNum: 1,
  pageSize: 100,
};

export default {
  components: { TabSearch, SearchList },
  mixins: [BaseDataList],

  name: "TabSearchList",
  data() {
    return {
      formDef: null,
      tabSearchHeight: null,
      page: PAGE_PARAM,
      loadStatus: "loadmore",
      dataList: [],
      tabSearchFilter: [],
      fields: [],
    };
  },
  props: {
    // 表单Uid
    formUid: {
      type: String,
      default: () => "",
    },

    // tabSearch的数据数组 section是显示的名称，filter是过滤条件
    tabSectionList: {
      type: Array,
    },
    // tab的字号
    tabFontSize: {
      type: String | Number,
    },

    // 是否展示tab
    showTab: {
      type: Boolean,
      default: () => true,
    },

    // 是否展示search,如果表单定义里配置了searchFields，showSearch权重高
    showSearch: {
      type: Boolean,
      default: () => true,
    },

    // TODO !数据的初始过滤条件
    initFilter: {
      type: Array,
    },

    // 数据列表操作数组
    operations: {
      type: Array,
      default: () => [],
    },
  },

  computed: {
    // TODO 空数组情况
    genShowSearch() {
      return this.showSearch ?? !!this.formDef?.list.searchFields;
    },
    // TODO 支持多条件查询，暂时只选择第一个
    searchItem() {
      return Array.isArray(this.formDef?.list.searchFields) &&
        this.formDef?.list.searchFields
        ? this.formDef?.list.searchFields[0]
        : {};
    },
  },
  async mounted() {
    await this.getFormDesign();
    this.getTabSearchHeight();
  },

  watch: {
    async tabSearchFilter() {
      await this.reloadData();
    },
  },
  methods: {
    // 刷新数据
    refreshData() {
      this.$refs["tabSearch"].customHandler();
    },
    // 切换tab
    tabFilterChangeHandler(filter) {
      this.tabSearchFilter = filter;
    },

    // 更改搜索条件
    tabSearchFilterChangeHandler(filter) {
      this.tabSearchFilter = filter;
    },

    async getFormDesign() {
      this.formDef = await this.$apis.formDef.getFormDef(this.formUid);
      this.fields = this.formDef.list.listFields;
    },

    // 加载数据
    async reloadData() {
      this.page.pageNum = 1;
      const params = {
        filter: this.tabSearchFilter,
        page: this.page,
      };
      try {
        let res = await this.$apis.formData.getFormRecords(
          this.formUid,
          params
        );
        this.dataList = res.list;
        // this.$emit("updateList", this.dataList);
        if (res.list && res.list.length < this.page.pageSize) {
          this.loadStatus = "nomore";
        }
      } catch (e) {
        console.log("加载数据发生错误：", e);
      }
    },

    // 获取tabSearch的高度
    getTabSearchHeight() {
      this.$nextTick(() => {
        let height = 0;
        uni.getSystemInfo({
          success: (res) => {
            let info = uni.createSelectorQuery().select(".tab-search-wrapper");
            info
              .boundingClientRect((data) => {
                height = data.height; // 获取元素高度
                this.tabSearchHeight = `${height + 2}px`;
              })
              .exec();
          },
        });
      });
    },

    async scrollToLowerHandler() {
      this.page.pageNum += 1;
      await this.loadMoreData();
    },
    // 加载更多数据
    async loadMoreData() {
      this.loadStatus = "loading";
      const params = {
        filter: this.tabSearchFilter,
        page: this.page,
      };
      try {
        let res = await this.$apis.formData.getFormRecords(
          this.formUid,
          params
        );
        this.dataList.push(...res.list);
        // this.$emit("updateList", this.dataList);
        if (res.list && res.list.length < this.page.pageSize) {
          this.loadStatus = "nomore";
        }
      } catch (e) {
        console.log("加载数据发生错误：", e);
      }
    },

    // 操作按钮点击事件
    buttonClickEventHandler(payload) {
      this.$emit("operate", payload);
    },

    // 数据列表点击事件
    itemClickHandler(payload) {
      this.$emit("operate", payload);
    },
  },
};
</script>

<style lang="less" scoped>
.tab-search-list-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  .tab-search-wrapper {
    box-sizing: border-box;
    background: #fff;
    z-index: 999;
    padding: 0 20rpx 20rpx 20rpx;
    position: fixed;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .scroll-wrapper {
    height: 100%;
    .scroll-content {
      height: 100%;
      .search-list-wrapper {
        width: calc(100% - 40rpx);
        padding: 0 20rpx 20rpx 20rpx;
      }

      .loading {
        padding: 30rpx 100rpx 80rpx 100rpx;
      }
    }
  }
}
</style>
