<template>
  <view class="tabbar" v-if="config.length > 0">
    <u-tabbar :value="value" @change="(name) => (value6 = name)" :fixed="true" :activeColor="$constants.COLOR.PRIMARY_COLOR" :placeholder="true" :safeAreaInsetBottom="true">
      <u-tabbar-item v-for="(item, index) in config" :key="index" :text="item.name" :icon="item.icon" @click="toPath(item.path)"></u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script>
export default {
  props: {
    config: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Number,
      default: () => 0,
    },
  },
  data() {
    return {};
  },
  methods: {
    toPath(path) {
      uni.redirectTo({
        url: path,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tabbar {
  /deep/ .u-tabbar {
    flex: none !important;
  }
}
</style>
