<template>
  <view class="scroll-wrap">
    <scroll-view
      scroll-y
      class="scroll-content"
      @scrolltolower="scrollToLowerHandler"
    >
      <DataList
        :data="dataList"
        :fields="fields"
        :operations="operations"
        @edit="editHandler"
        @navigate="navigateHandler"
        @delete="deleteHandler"
        @view="viewHandler"
        @itemClick="viewHandler"
      />
      <view class="loading" v-if="dataList.length > 0">
        <u-loadmore :status="loadStatus" />
      </view>
    </scroll-view>
    <u-modal
      :show="modalShow"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_DELETE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="deleteCancelHandler"
      @confirm="deleteConfirmHandler"
    >
    </u-modal>
  </view>
</template>

<script>
import BaseDataList from "@/components/common/BaseDataList";
import DataList from "@/components/common/DataList";
import Storage from "@/tools/Storage";
import Navigation from "@/tools/Navigation";

const OPERATIONS = [
  {
    key: "view",
    label: "查看",
  },
  {
    key: "edit",
    label: "编辑",
    depends: ["=", "state", "fill_in"],
  },
  {
    key: "navigate",
    label: "导航",
  },
  {
    key: "delete",
    label: "删除",
    type: "info",
    depends: ["=", "state", "fill_in"],
  },
];

const POINT_LON_FIELD = "经度";
const POINT_LAT_FIELD = "纬度";

const PAGE_PARAM = {
  pageNum: 1,
  pageSize: 10,
};

export default {
  mixins: [BaseDataList],

  components: {
    DataList,
  },

  props: {
    formUid: {
      type: String,
    },
  },

  data() {
    return {
      dataList: [],
      fields: [],
      operations: OPERATIONS,
      page: PAGE_PARAM,
      loadStatus: "loadmore",
      modalShow: false,
      delItemId: undefined,
    };
  },

  async mounted() {
    await this.getFormDesign();
    await this.loadMoreData();
  },

  methods: {
    async getFormDesign() {
      const res = await this.$apis.formDef.getFormDef(this.formUid);
      this.fields = res.list.listFields;
    },

    async loadMoreData() {
      this.loadStatus = "loading";
      const params = {
        filter: this.filter,
        page: this.page,
      };
      try {
        const res = await this.$apis.formData.getFormRecords(
          this.formUid,
          params
        );
        this.dataList.push(...res.list);
        if (res.list && res.list.length < this.page.pageSize) {
          this.loadStatus = "nomore";
        }
      } catch (e) {
        console.log("加载数据发生错误：", e);
      }
    },

    async reloadData() {
      this.page.pageNum = 1;
      const params = {
        filter: this.filter,
        page: this.page,
      };
      try {
        const res = await this.$apis.formData.getFormRecords(
          this.formUid,
          params
        );
        this.dataList = res.list;
        if (res.list && res.list.length < this.page.pageSize) {
          this.loadStatus = "nomore";
        }
      } catch (e) {
        console.log("加载数据发生错误：", e);
      }
    },

    scrollToLowerHandler() {
      this.page.pageNum += 1;
      this.loadMoreData();
    },

    editHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      Storage.saveFormData(formRecordUid, data);
      const pageUrl = `${this.$constants.PAGE.FORM_PAGE_URL}?formUid=${this.formUid}&formRecordUid=${formRecordUid}`;
      this.navigateTo(pageUrl);
    },

    navigateHandler(data) {
      const lon = data[POINT_LON_FIELD];
      const lat = data[POINT_LAT_FIELD];
      if (isNaN(lon) || isNaN(lat)) return;
      Navigation.navigateTo(lon, lat);
    },

    deleteHandler(data) {
      this.delItemId = data._id;
      if (!this.delItemId) return;
      this.modalShow = true;
    },

    async deleteConfirmHandler() {
      try {
        this.showLoading(this.$constants.MSG.DELETING);
        await this.$apis.formData.deleteFormRecord(
          this.formUid,
          this.delItemId
        );
        await this.reloadData();
        this.showSuccess(this.$constants.MSG.DELETE_SUCCESS);
      } catch (e) {
        this.showError(this.$constants.MSG.DELETE_FAIL);
      } finally {
        this.hideLoading();
      }
      this.delItemId = undefined;
      this.modalShow = false;
    },

    deleteCancelHandler() {
      this.delItemId = undefined;
      this.modalShow = false;
    },

    viewHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      Storage.saveFormData(formRecordUid, data);
      const pageUrl = `${this.$constants.PAGE.VIEW_PAGE_URL}?formUid=${this.formUid}&formRecordUid=${formRecordUid}`;
      this.navigateTo(pageUrl);
    },
  },
};
</script>

<style lang="less" scoped>
.scroll-wrap {
  height: calc(100% - 56px);
  padding-top: 12px;
  .scroll-content {
    height: 100%;
  }
}
</style>
