<template>
  <view class="scroll-wrap">
    <scroll-view scroll-y class="scroll-content">
      <DataList
        :data="dataList"
        :fields="fields"
        :operations="operations"
        @edit="editHandler"
        @delete="deleteHandler"
        @view="viewHandler"
        cache
      >
      </DataList>
    </scroll-view>
    <u-modal
      :show="modalShow"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_DELETE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="deleteCancelHandler"
      @confirm="deleteConfirmHandler"
    >
    </u-modal>
  </view>
</template>

<script>
import BaseDataList from "@/components/common/BaseDataList";
import DataList from "@/components/common/DataList";
import Storage from "@/tools/Storage";

const OPERATIONS = [
  {
    key: "view",
    label: "查看",
  },
  {
    key: "edit",
    label: "编辑",
  },
  {
    key: "delete",
    type: "info",
    label: "删除",
  },
];

export default {
  mixins: [BaseDataList],

  components: {
    DataList,
  },

  props: {
    formUid: {
      type: String,
    },
  },

  data() {
    return {
      dataList: [],
      fields: [],
      operations: OPERATIONS,
      modalShow: false,
      delItemId: undefined,
    };
  },

  async mounted() {
    await this.getFormDesign();
    await this.loadData();
  },

  methods: {
    async getFormDesign() {
      const res = await this.$apis.formDef.getFormDef(this.formUid);
      this.fields = res.list.listFields;
    },

    async loadData() {
      this.dataList = await this.$apis.formDataLocal.getCacheRecords(
        this.formUid
      );
    },

    editHandler(data) {
      const cacheRecordId = data.id;
      if (!cacheRecordId) return;
      Storage.saveFormData(cacheRecordId, data.content);
      const pageUrl = `${this.$constants.PAGE.FORM_PAGE_URL}?formUid=${this.formUid}&cacheRecordId=${cacheRecordId}`;
      this.navigateTo(pageUrl);
    },

    deleteHandler(data) {
      this.delItemId = data.id;
      if (!this.delItemId) return;
      this.modalShow = true;
    },

    async deleteConfirmHandler() {
      try {
        await this.$apis.formDataLocal.delCacheRecord(
          this.formUid,
          this.delItemId
        );
        await this.loadData();
      } catch (e) {
        this.showError(this.$constants.MSG.DELETE_FAIL);
      }
      this.delItemId = undefined;
      this.modalShow = false;
    },

    deleteCancelHandler() {
      this.delItemId = undefined;
      this.modalShow = false;
    },

    viewHandler(data) {
      const cacheRecordId = data.id;
      if (!cacheRecordId) return;
      Storage.saveFormData(cacheRecordId, data.content);
      const pageUrl = `${this.$constants.PAGE.VIEW_PAGE_URL}?formUid=${this.formUid}&cacheRecordId=${cacheRecordId}`;

      this.navigateTo(pageUrl);
    },
  },
};
</script>

<style lang="less" scoped>
.scroll-wrap {
  height: calc(100% - 56px);
  padding-top: 12px;
  .scroll-content {
    height: 100%;
  }
}
</style>
