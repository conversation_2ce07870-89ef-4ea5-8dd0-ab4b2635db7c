<script>
import FormDataLocal from "@/apis/formDataLocal";

export default {
  async onLaunch() {
    console.log("App Launch");
    uni.getStorage({
      key: "refresh_token",
      success: (res) => {
        console.log(res);
      },
      fail: (err) => {
        console.log(err);
        uni.reLaunch({
          url: "/pages/Workspace/index",
        });
      },
    });
    // 关闭本地缓存数据库
    await FormDataLocal.closeDB();
    // await this.checkNewVersion();
  },
  onShow() {
    console.log("App Show");
  },
  onHide() {
    console.log("App Hide");
  },
  onLoad() {},
  methods: {
    async checkNewVersion() {
      const result = await this.$apis.version.checkVersion();
      if (!result) {
        return;
      }
      const { upgrade, version, url } = result;
      console.log(upgrade, version, url);
      if (upgrade === true) {
        uni.showModal({
          title: "版本更新",
          content: this.$constants.VERSION.NEW_VERSION,
          success: (res) => {
            if (res.confirm) {
              plus.runtime.openURL(url);
            }
          },
        });
      }
    },
  },
};
</script>

<style lang="scss">
@import "uview-ui/index.scss";

html,
body {
  background-color: #fff;
  overflow-x: hidden;
}

body::-webkit-scrollbar {
  display: none;
}

::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}
</style>
