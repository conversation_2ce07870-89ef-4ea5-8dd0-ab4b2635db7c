Arguments: 
  /usr/local/bin/node /Users/<USER>/.yarn/bin/yarn.js add qiun-data-charts

PATH: 
  /Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.yarn/bin:/Users/<USER>/.config/yarn/global/node_modules/.bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/Trae CN.app/Contents/Resources/app/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.yarn/bin:/Users/<USER>/.config/yarn/global/node_modules/.bin

Yarn version: 
  1.22.19

Node version: 
  20.11.1

Platform: 
  darwin x64

Trace: 
  Error: http://registry.npm.taobao.org/qiun-data-charts: Not found
      at params.callback [as _callback] (/Users/<USER>/.yarn/lib/cli.js:66145:18)
      at self.callback (/Users/<USER>/.yarn/lib/cli.js:140890:22)
      at Request.emit (node:events:518:28)
      at Request.<anonymous> (/Users/<USER>/.yarn/lib/cli.js:141862:10)
      at Request.emit (node:events:518:28)
      at IncomingMessage.<anonymous> (/Users/<USER>/.yarn/lib/cli.js:141784:12)
      at Object.onceWrapper (node:events:632:28)
      at IncomingMessage.emit (node:events:530:35)
      at endReadableNT (node:internal/streams/readable:1696:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:82:21)

npm manifest: 
  {
    "name": "gt-mis-uniapp",
    "version": "1.0.0",
    "main": "main.js",
    "scripts": {
      "test": "echo \"Error: no test specified\" && exit 1",
      "prepare": "husky install"
    },
    "repository": {
      "type": "git",
      "url": "http://**************/mis/gt-mis-uniapp.git"
    },
    "author": "",
    "license": "ISC",
    "dependencies": {
      "@turf/turf": "^6.5.0",
      "axios": "^0.26.1",
      "axios-miniprogram-adapter": "^0.3.2",
      "crypto-js": "^4.1.1",
      "dayjs": "^1.11.0",
      "dotenv": "^16.0.1",
      "gt-mis-app-components": "^1.2.8",
      "lodash": "^4.17.21",
      "mapbox-gl": "1.13.1",
      "moment": "^2.29.1",
      "omit-deep-lodash": "^1.1.7",
      "uid": "^2.0.0",
      "uview-ui": "^2.0.35"
    },
    "eslintConfig": {
      "root": true,
      "env": {
        "node": true
      },
      "extends": [
        "plugin:vue/essential",
        "eslint:recommended"
      ],
      "parserOptions": {
        "parser": "babel-eslint"
      },
      "rules": {}
    },
    "description": "",
    "–minimize": {
      "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize"
    },
    "devDependencies": {
      "@babel/core": "^7.21.0",
      "@babel/eslint-parser": "^7.19.1",
      "eslint": "^8.36.0",
      "eslint-config-airbnb-base": "^15.0.0",
      "eslint-config-ali": "^14.0.2",
      "eslint-config-prettier": "^8.7.0",
      "eslint-plugin-import": "^2.27.5",
      "eslint-plugin-prettier": "^4.2.1",
      "eslint-plugin-vue": "^9.9.0",
      "prettier": "^2.8.4",
      "vue-eslint-parser": "^9.1.0"
    },
    "lint-staged": {
      "*.js": "eslint --cache --fix"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@ampproject/remapping@^2.2.0":
    version "2.2.1"
    resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.1.tgz"
    integrity sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==
    dependencies:
      "@jridgewell/gen-mapping" "^0.3.0"
      "@jridgewell/trace-mapping" "^0.3.9"
  
  "@babel/code-frame@^7.18.6", "@babel/code-frame@^7.21.4":
    version "7.21.4"
    resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.21.4.tgz"
    integrity sha512-LYvhNKfwWSPpocw8GI7gpK2nq3HSDuEPC/uSYaALSJu9xjsalaaYFOq0Pwt5KmVqwEbZlDu81aLXwBOmD/Fv9g==
    dependencies:
      "@babel/highlight" "^7.18.6"
  
  "@babel/compat-data@^7.21.5":
    version "7.21.7"
    resolved "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.21.7.tgz"
    integrity sha512-KYMqFYTaenzMK4yUtf4EW9wc4N9ef80FsbMtkwool5zpwl4YrT1SdWYSTRcT94KO4hannogdS+LxY7L+arP3gA==
  
  "@babel/core@^7.21.0":
    version "7.21.8"
    resolved "https://registry.npmjs.org/@babel/core/-/core-7.21.8.tgz"
    integrity sha512-YeM22Sondbo523Sz0+CirSPnbj9bG3P0CdHcBZdqUuaeOaYEFbOLoGU7lebvGP6P5J/WE9wOn7u7C4J9HvS1xQ==
    dependencies:
      "@ampproject/remapping" "^2.2.0"
      "@babel/code-frame" "^7.21.4"
      "@babel/generator" "^7.21.5"
      "@babel/helper-compilation-targets" "^7.21.5"
      "@babel/helper-module-transforms" "^7.21.5"
      "@babel/helpers" "^7.21.5"
      "@babel/parser" "^7.21.8"
      "@babel/template" "^7.20.7"
      "@babel/traverse" "^7.21.5"
      "@babel/types" "^7.21.5"
      convert-source-map "^1.7.0"
      debug "^4.1.0"
      gensync "^1.0.0-beta.2"
      json5 "^2.2.2"
      semver "^6.3.0"
  
  "@babel/eslint-parser@^7.19.1":
    version "7.21.8"
    resolved "https://registry.npmjs.org/@babel/eslint-parser/-/eslint-parser-7.21.8.tgz"
    integrity sha512-HLhI+2q+BP3sf78mFUZNCGc10KEmoUqtUT1OCdMZsN+qr4qFeLUod62/zAnF3jNQstwyasDkZnVXwfK2Bml7MQ==
    dependencies:
      "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
      eslint-visitor-keys "^2.1.0"
      semver "^6.3.0"
  
  "@babel/generator@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.21.5.tgz"
    integrity sha512-SrKK/sRv8GesIW1bDagf9cCG38IOMYZusoe1dfg0D8aiUe3Amvoj1QtjTPAWcfrZFvIwlleLb0gxzQidL9w14w==
    dependencies:
      "@babel/types" "^7.21.5"
      "@jridgewell/gen-mapping" "^0.3.2"
      "@jridgewell/trace-mapping" "^0.3.17"
      jsesc "^2.5.1"
  
  "@babel/helper-compilation-targets@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.5.tgz"
    integrity sha512-1RkbFGUKex4lvsB9yhIfWltJM5cZKUftB2eNajaDv3dCMEp49iBG0K14uH8NnX9IPux2+mK7JGEOB0jn48/J6w==
    dependencies:
      "@babel/compat-data" "^7.21.5"
      "@babel/helper-validator-option" "^7.21.0"
      browserslist "^4.21.3"
      lru-cache "^5.1.1"
      semver "^6.3.0"
  
  "@babel/helper-environment-visitor@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.21.5.tgz"
    integrity sha512-IYl4gZ3ETsWocUWgsFZLM5i1BYx9SoemminVEXadgLBa9TdeorzgLKm8wWLA6J1N/kT3Kch8XIk1laNzYoHKvQ==
  
  "@babel/helper-function-name@^7.21.0":
    version "7.21.0"
    resolved "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz"
    integrity sha512-HfK1aMRanKHpxemaY2gqBmL04iAPOPRj7DxtNbiDOrJK+gdwkiNRVpCpUJYbUT+aZyemKN8brqTOxzCaG6ExRg==
    dependencies:
      "@babel/template" "^7.20.7"
      "@babel/types" "^7.21.0"
  
  "@babel/helper-hoist-variables@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz"
    integrity sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==
    dependencies:
      "@babel/types" "^7.18.6"
  
  "@babel/helper-module-imports@^7.21.4":
    version "7.21.4"
    resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.21.4.tgz"
    integrity sha512-orajc5T2PsRYUN3ZryCEFeMDYwyw09c/pZeaQEZPH0MpKzSvn3e0uXsDBu3k03VI+9DBiRo+l22BfKTpKwa/Wg==
    dependencies:
      "@babel/types" "^7.21.4"
  
  "@babel/helper-module-transforms@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.5.tgz"
    integrity sha512-bI2Z9zBGY2q5yMHoBvJ2a9iX3ZOAzJPm7Q8Yz6YeoUjU/Cvhmi2G4QyTNyPBqqXSgTjUxRg3L0xV45HvkNWWBw==
    dependencies:
      "@babel/helper-environment-visitor" "^7.21.5"
      "@babel/helper-module-imports" "^7.21.4"
      "@babel/helper-simple-access" "^7.21.5"
      "@babel/helper-split-export-declaration" "^7.18.6"
      "@babel/helper-validator-identifier" "^7.19.1"
      "@babel/template" "^7.20.7"
      "@babel/traverse" "^7.21.5"
      "@babel/types" "^7.21.5"
  
  "@babel/helper-simple-access@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.21.5.tgz"
    integrity sha512-ENPDAMC1wAjR0uaCUwliBdiSl1KBJAVnMTzXqi64c2MG8MPR6ii4qf7bSXDqSFbr4W6W028/rf5ivoHop5/mkg==
    dependencies:
      "@babel/types" "^7.21.5"
  
  "@babel/helper-split-export-declaration@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz"
    integrity sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==
    dependencies:
      "@babel/types" "^7.18.6"
  
  "@babel/helper-string-parser@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.21.5.tgz"
    integrity sha512-5pTUx3hAJaZIdW99sJ6ZUUgWq/Y+Hja7TowEnLNMm1VivRgZQL3vpBY3qUACVsvw+yQU6+YgfBVmcbLaZtrA1w==
  
  "@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
    version "7.19.1"
    resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz"
    integrity sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==
  
  "@babel/helper-validator-option@^7.21.0":
    version "7.21.0"
    resolved "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz"
    integrity sha512-rmL/B8/f0mKS2baE9ZpyTcTavvEuWhTTW8amjzXNvYG4AwBsqTLikfXsEofsJEfKHf+HQVQbFOHy6o+4cnC/fQ==
  
  "@babel/helpers@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.21.5.tgz"
    integrity sha512-BSY+JSlHxOmGsPTydUkPf1MdMQ3M81x5xGCOVgWM3G8XH77sJ292Y2oqcp0CbbgxhqBuI46iUz1tT7hqP7EfgA==
    dependencies:
      "@babel/template" "^7.20.7"
      "@babel/traverse" "^7.21.5"
      "@babel/types" "^7.21.5"
  
  "@babel/highlight@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz"
    integrity sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==
    dependencies:
      "@babel/helper-validator-identifier" "^7.18.6"
      chalk "^2.0.0"
      js-tokens "^4.0.0"
  
  "@babel/parser@^7.20.7", "@babel/parser@^7.21.5", "@babel/parser@^7.21.8":
    version "7.21.8"
    resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.21.8.tgz"
    integrity sha512-6zavDGdzG3gUqAdWvlLFfk+36RilI+Pwyuuh7HItyeScCWP3k6i8vKclAQ0bM/0y/Kz/xiwvxhMv9MgTJP5gmA==
  
  "@babel/template@^7.20.7":
    version "7.20.7"
    resolved "https://registry.npmjs.org/@babel/template/-/template-7.20.7.tgz"
    integrity sha512-8SegXApWe6VoNw0r9JHpSteLKTpTiLZ4rMlGIm9JQ18KiCtyQiAMEazujAHrUS5flrcqYZa75ukev3P6QmUwUw==
    dependencies:
      "@babel/code-frame" "^7.18.6"
      "@babel/parser" "^7.20.7"
      "@babel/types" "^7.20.7"
  
  "@babel/traverse@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.21.5.tgz"
    integrity sha512-AhQoI3YjWi6u/y/ntv7k48mcrCXmus0t79J9qPNlk/lAsFlCiJ047RmbfMOawySTHtywXhbXgpx/8nXMYd+oFw==
    dependencies:
      "@babel/code-frame" "^7.21.4"
      "@babel/generator" "^7.21.5"
      "@babel/helper-environment-visitor" "^7.21.5"
      "@babel/helper-function-name" "^7.21.0"
      "@babel/helper-hoist-variables" "^7.18.6"
      "@babel/helper-split-export-declaration" "^7.18.6"
      "@babel/parser" "^7.21.5"
      "@babel/types" "^7.21.5"
      debug "^4.1.0"
      globals "^11.1.0"
  
  "@babel/types@^7.18.6", "@babel/types@^7.20.7", "@babel/types@^7.21.0", "@babel/types@^7.21.4", "@babel/types@^7.21.5":
    version "7.21.5"
    resolved "https://registry.npmjs.org/@babel/types/-/types-7.21.5.tgz"
    integrity sha512-m4AfNvVF2mVC/F7fDEdH2El3HzUg9It/XsCxZiOTTA3m3qYfcSVSbTfM6Q9xG+hYDniZssYhlXKKUMD5m8tF4Q==
    dependencies:
      "@babel/helper-string-parser" "^7.21.5"
      "@babel/helper-validator-identifier" "^7.19.1"
      to-fast-properties "^2.0.0"
  
  "@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.3.0":
    version "4.4.0"
    resolved "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
    integrity sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==
    dependencies:
      eslint-visitor-keys "^3.3.0"
  
  "@eslint-community/regexpp@^4.4.0":
    version "4.5.1"
    resolved "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.5.1.tgz"
    integrity sha512-Z5ba73P98O1KUYCCJTUeVpja9RcGoMdncZ6T49FCUl2lN38JtCJ+3WgIDBv0AuY4WChU5PmtJmOCTlN6FZTFKQ==
  
  "@eslint/eslintrc@^2.0.3":
    version "2.0.3"
    resolved "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.0.3.tgz"
    integrity sha512-+5gy6OQfk+xx3q0d6jGZZC3f3KzAkXc/IanVxd1is/VIIziRqqt3ongQz0FiTUXqTk0c7aDB3OaFuKnuSoJicQ==
    dependencies:
      ajv "^6.12.4"
      debug "^4.3.2"
      espree "^9.5.2"
      globals "^13.19.0"
      ignore "^5.2.0"
      import-fresh "^3.2.1"
      js-yaml "^4.1.0"
      minimatch "^3.1.2"
      strip-json-comments "^3.1.1"
  
  "@eslint/js@8.40.0":
    version "8.40.0"
    resolved "https://registry.npmjs.org/@eslint/js/-/js-8.40.0.tgz"
    integrity sha512-ElyB54bJIhXQYVKjDSvCkPO1iU1tSAeVQJbllWJq1XQSmmA4dgFk8CbiBGpiOPxleE48vDogxCtmMYku4HSVLA==
  
  "@humanwhocodes/config-array@^0.11.8":
    version "0.11.8"
    resolved "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.8.tgz"
    integrity sha512-UybHIJzJnR5Qc/MsD9Kr+RpO2h+/P1GhOwdiLPXK5TWk5sgTdu88bTD9UP+CKbPPh5Rni1u0GjAdYQLemG8g+g==
    dependencies:
      "@humanwhocodes/object-schema" "^1.2.1"
      debug "^4.1.1"
      minimatch "^3.0.5"
  
  "@humanwhocodes/module-importer@^1.0.1":
    version "1.0.1"
    resolved "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
    integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==
  
  "@humanwhocodes/object-schema@^1.2.1":
    version "1.2.1"
    resolved "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz"
    integrity sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==
  
  "@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
    version "0.3.3"
    resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz"
    integrity sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==
    dependencies:
      "@jridgewell/set-array" "^1.0.1"
      "@jridgewell/sourcemap-codec" "^1.4.10"
      "@jridgewell/trace-mapping" "^0.3.9"
  
  "@jridgewell/resolve-uri@3.1.0":
    version "3.1.0"
    resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz"
    integrity sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==
  
  "@jridgewell/set-array@^1.0.1":
    version "1.1.2"
    resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz"
    integrity sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==
  
  "@jridgewell/sourcemap-codec@1.4.14":
    version "1.4.14"
    resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz"
    integrity sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==
  
  "@jridgewell/sourcemap-codec@^1.4.10":
    version "1.4.15"
    resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
    integrity sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==
  
  "@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
    version "0.3.18"
    resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.18.tgz"
    integrity sha512-w+niJYzMHdd7USdiH2U6869nqhD2nbfZXND5Yp93qIbEmnDNk7PD48o+YchRVpzMU7M6jVCbenTR7PA1FLQ9pA==
    dependencies:
      "@jridgewell/resolve-uri" "3.1.0"
      "@jridgewell/sourcemap-codec" "1.4.14"
  
  "@lukeed/csprng@^1.0.0":
    version "1.0.1"
    resolved "https://registry.npmjs.org/@lukeed/csprng/-/csprng-1.0.1.tgz"
    integrity sha512-uSvJdwQU5nK+Vdf6zxcWAY2A8r7uqe+gePwLWzJ+fsQehq18pc0I2hJKwypZ2aLM90+Er9u1xn4iLJPZ+xlL4g==
  
  "@mapbox/geojson-rewind@^0.5.0":
    version "0.5.2"
    resolved "https://registry.npmmirror.com/@mapbox/geojson-rewind/-/geojson-rewind-0.5.2.tgz#591a5d71a9cd1da1a0bf3420b3bea31b0fc7946a"
    integrity sha512-tJaT+RbYGJYStt7wI3cq4Nl4SXxG8W7JDG5DMJu97V25RnbNg3QtQtf+KD+VLjNpWKYsRvXDNmNrBgEETr1ifA==
    dependencies:
      get-stream "^6.0.1"
      minimist "^1.2.6"
  
  "@mapbox/geojson-types@^1.0.2":
    version "1.0.2"
    resolved "https://registry.npmmirror.com/@mapbox/geojson-types/-/geojson-types-1.0.2.tgz#9aecf642cb00eab1080a57c4f949a65b4a5846d6"
    integrity sha512-e9EBqHHv3EORHrSfbR9DqecPNn+AmuAoQxV6aL8Xu30bJMJR1o8PZLZzpk1Wq7/NfCbuhmakHTPYRhoqLsXRnw==
  
  "@mapbox/jsonlint-lines-primitives@^2.0.2":
    version "2.0.2"
    resolved "https://registry.npmmirror.com/@mapbox/jsonlint-lines-primitives/-/jsonlint-lines-primitives-2.0.2.tgz#ce56e539f83552b58d10d672ea4d6fc9adc7b234"
    integrity sha512-rY0o9A5ECsTQRVhv7tL/OyDpGAoUB4tTvLiW1DSzQGq4bvTPhNw1VpSNjDJc5GFZ2XuyOtSWSVN05qOtcD71qQ==
  
  "@mapbox/mapbox-gl-supported@^1.5.0":
    version "1.5.0"
    resolved "https://registry.npmmirror.com/@mapbox/mapbox-gl-supported/-/mapbox-gl-supported-1.5.0.tgz#f60b6a55a5d8e5ee908347d2ce4250b15103dc8e"
    integrity sha512-/PT1P6DNf7vjEEiPkVIRJkvibbqWtqnyGaBz3nfRdcxclNSnSdaLU5tfAgcD7I8Yt5i+L19s406YLl1koLnLbg==
  
  "@mapbox/point-geometry@0.1.0", "@mapbox/point-geometry@^0.1.0", "@mapbox/point-geometry@~0.1.0":
    version "0.1.0"
    resolved "https://registry.npmmirror.com/@mapbox/point-geometry/-/point-geometry-0.1.0.tgz#8a83f9335c7860effa2eeeca254332aa0aeed8f2"
    integrity sha512-6j56HdLTwWGO0fJPlrZtdU/B13q8Uwmo18Ck2GnGgN9PCFyKTZ3UbXeEdRFh18i9XQ92eH2VdtpJHpBD3aripQ==
  
  "@mapbox/tiny-sdf@^1.1.1":
    version "1.2.5"
    resolved "https://registry.npmmirror.com/@mapbox/tiny-sdf/-/tiny-sdf-1.2.5.tgz#424c620a96442b20402552be70a7f62a8407cc59"
    integrity sha512-cD8A/zJlm6fdJOk6DqPUV8mcpyJkRz2x2R+/fYcWDYG3oWbG7/L7Yl/WqQ1VZCjnL9OTIMAn6c+BC5Eru4sQEw==
  
  "@mapbox/unitbezier@^0.0.0":
    version "0.0.0"
    resolved "https://registry.npmmirror.com/@mapbox/unitbezier/-/unitbezier-0.0.0.tgz#15651bd553a67b8581fb398810c98ad86a34524e"
    integrity sha512-HPnRdYO0WjFjRTSwO3frz1wKaU649OBFPX3Zo/2WZvuRi6zMiRGui8SnPQiQABgqCf8YikDe5t3HViTVw1WUzA==
  
  "@mapbox/vector-tile@^1.3.1":
    version "1.3.1"
    resolved "https://registry.npmmirror.com/@mapbox/vector-tile/-/vector-tile-1.3.1.tgz#d3a74c90402d06e89ec66de49ec817ff53409666"
    integrity sha512-MCEddb8u44/xfQ3oD+Srl/tNcQoqTw3goGk2oLsrFxOTc3dUp+kAnby3PvAeeBYSMSjSPD1nd1AJA6W49WnoUw==
    dependencies:
      "@mapbox/point-geometry" "~0.1.0"
  
  "@mapbox/whoots-js@^3.1.0":
    version "3.1.0"
    resolved "https://registry.npmmirror.com/@mapbox/whoots-js/-/whoots-js-3.1.0.tgz#497c67a1cef50d1a2459ba60f315e448d2ad87fe"
    integrity sha512-Es6WcD0nO5l+2BOQS4uLfNPYQaNDfbot3X1XUoloz+x0mPDS3eeORZJl06HXjwBG1fOGwCRnzK88LMdxKRrd6Q==
  
  "@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
    version "5.1.1-v1"
    resolved "https://registry.npmjs.org/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz"
    integrity sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==
    dependencies:
      eslint-scope "5.1.1"
  
  "@nodelib/fs.scandir@2.1.5":
    version "2.1.5"
    resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
    integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
    dependencies:
      "@nodelib/fs.stat" "2.0.5"
      run-parallel "^1.1.9"
  
  "@nodelib/fs.stat@2.0.5":
    version "2.0.5"
    resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
    integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==
  
  "@nodelib/fs.walk@^1.2.8":
    version "1.2.8"
    resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
    integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
    dependencies:
      "@nodelib/fs.scandir" "2.1.5"
      fastq "^1.6.0"
  
  "@turf/along@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/along/-/along-6.5.0.tgz#ab12eec58a14de60fe243a62d31a474f415c8fef"
    integrity sha512-LLyWQ0AARqJCmMcIEAXF4GEu8usmd4Kbz3qk1Oy5HoRNpZX47+i5exQtmIWKdqJ1MMhW26fCTXgpsEs5zgJ5gw==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/angle@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/angle/-/angle-6.5.0.tgz#985934171284e109d41e19ed48ad91cf9709a928"
    integrity sha512-4pXMbWhFofJJAOvTMCns6N4C8CMd5Ih4O2jSAG9b3dDHakj3O4yN1+Zbm+NUei+eVEZ9gFeVp9svE3aMDenIkw==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
  
  "@turf/area@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/area/-/area-6.5.0.tgz#1d0d7aee01d8a4a3d4c91663ed35cc615f36ad56"
    integrity sha512-xCZdiuojokLbQ+29qR6qoMD89hv+JAgWjLrwSEWL+3JV8IXKeNFl6XkEJz9HGkVpnXvQKJoRz4/liT+8ZZ5Jyg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/bbox-clip@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bbox-clip/-/bbox-clip-6.5.0.tgz#8e07d51ef8c875f9490d5c8699a2e51918587c94"
    integrity sha512-F6PaIRF8WMp8EmgU/Ke5B1Y6/pia14UAYB5TiBC668w5rVVjy5L8rTm/m2lEkkDMHlzoP9vNY4pxpNthE7rLcQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/bbox-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bbox-polygon/-/bbox-polygon-6.5.0.tgz#f18128b012eedfa860a521d8f2b3779cc0801032"
    integrity sha512-+/r0NyL1lOG3zKZmmf6L8ommU07HliP4dgYToMoTxqzsWzyLjaj/OzgQ8rBmv703WJX+aS6yCmLuIhYqyufyuw==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/bbox@*":
    version "7.2.0"
    resolved "https://registry.npmmirror.com/@turf/bbox/-/bbox-7.2.0.tgz#9db338d6407380f66a72050657f1998c5c5ccc4a"
    integrity sha512-wzHEjCXlYZiDludDbXkpBSmv8Zu6tPGLmJ1sXQ6qDwpLE1Ew3mcWqt8AaxfTP5QwDNQa3sf2vvgTEzNbPQkCiA==
    dependencies:
      "@turf/helpers" "^7.2.0"
      "@turf/meta" "^7.2.0"
      "@types/geojson" "^7946.0.10"
      tslib "^2.8.1"
  
  "@turf/bbox@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bbox/-/bbox-6.5.0.tgz#bec30a744019eae420dac9ea46fb75caa44d8dc5"
    integrity sha512-RBbLaao5hXTYyyg577iuMtDB8ehxMlUqHEJiMs8jT1GHkFhr6sYre3lmLsPeYEi/ZKj5TP5tt7fkzNdJ4GIVyw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/bearing@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bearing/-/bearing-6.5.0.tgz#462a053c6c644434bdb636b39f8f43fb0cd857b0"
    integrity sha512-dxINYhIEMzgDOztyMZc20I7ssYVNEpSv04VbMo5YPQsqa80KO3TFvbuCahMsCAW5z8Tncc8dwBlEFrmRjJG33A==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/bezier-spline@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bezier-spline/-/bezier-spline-6.5.0.tgz#d1b1764948b0fa3d9aa6e4895aebeba24048b11f"
    integrity sha512-vokPaurTd4PF96rRgGVm6zYYC5r1u98ZsG+wZEv9y3kJTuJRX/O3xIY2QnTGTdbVmAJN1ouOsD0RoZYaVoXORQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-clockwise@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-clockwise/-/boolean-clockwise-6.5.0.tgz#34573ecc18f900080f00e4ff364631a8b1135794"
    integrity sha512-45+C7LC5RMbRWrxh3Z0Eihsc8db1VGBO5d9BLTOAwU4jR6SgsunTfRWR16X7JUwIDYlCVEmnjcXJNi/kIU3VIw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-contains@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-contains/-/boolean-contains-6.5.0.tgz#f802e7432fb53109242d5bf57393ef2f53849bbf"
    integrity sha512-4m8cJpbw+YQcKVGi8y0cHhBUnYT+QRfx6wzM4GI1IdtYH3p4oh/DOBJKrepQyiDzFDaNIjxuWXBh0ai1zVwOQQ==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/boolean-point-on-line" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-crosses@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-crosses/-/boolean-crosses-6.5.0.tgz#4a1981475b9d6e23b25721f9fb8ef20696ff1648"
    integrity sha512-gvshbTPhAHporTlQwBJqyfW+2yV8q/mOTxG6PzRVl6ARsqNoqYQWkd4MLug7OmAqVyBzLK3201uAeBjxbGw0Ng==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/polygon-to-line" "^6.5.0"
  
  "@turf/boolean-disjoint@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-disjoint/-/boolean-disjoint-6.5.0.tgz#e291d8f8f8cce7f7bb3c11e23059156a49afc5e4"
    integrity sha512-rZ2ozlrRLIAGo2bjQ/ZUu4oZ/+ZjGvLkN5CKXSKBcu6xFO6k2bgqeM8a1836tAW+Pqp/ZFsTA5fZHsJZvP2D5g==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/polygon-to-line" "^6.5.0"
  
  "@turf/boolean-equal@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-equal/-/boolean-equal-6.5.0.tgz#b1c0ce14e9d9fb7778cddcf22558c9f523fe9141"
    integrity sha512-cY0M3yoLC26mhAnjv1gyYNQjn7wxIXmL2hBmI/qs8g5uKuC2hRWi13ydufE3k4x0aNRjFGlg41fjoYLwaVF+9Q==
    dependencies:
      "@turf/clean-coords" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      geojson-equality "0.1.6"
  
  "@turf/boolean-intersects@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-intersects/-/boolean-intersects-6.5.0.tgz#df2b831ea31a4574af6b2fefe391f097a926b9d6"
    integrity sha512-nIxkizjRdjKCYFQMnml6cjPsDOBCThrt+nkqtSEcxkKMhAQj5OO7o2CecioNTaX8EayqwMGVKcsz27oP4mKPTw==
    dependencies:
      "@turf/boolean-disjoint" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/boolean-overlap@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-overlap/-/boolean-overlap-6.5.0.tgz#f27c85888c3665d42d613a91a83adf1657cd1385"
    integrity sha512-8btMIdnbXVWUa1M7D4shyaSGxLRw6NjMcqKBcsTXcZdnaixl22k7ar7BvIzkaRYN3SFECk9VGXfLncNS3ckQUw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/line-overlap" "^6.5.0"
      "@turf/meta" "^6.5.0"
      geojson-equality "0.1.6"
  
  "@turf/boolean-parallel@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-parallel/-/boolean-parallel-6.5.0.tgz#4e8a9dafdccaf18aca95f1265a5eade3f330173f"
    integrity sha512-aSHJsr1nq9e5TthZGZ9CZYeXklJyRgR5kCLm5X4urz7+MotMOp/LsGOsvKvK9NeUl9+8OUmfMn8EFTT8LkcvIQ==
    dependencies:
      "@turf/clean-coords" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
  
  "@turf/boolean-point-in-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-point-in-polygon/-/boolean-point-in-polygon-6.5.0.tgz#6d2e9c89de4cd2e4365004c1e51490b7795a63cf"
    integrity sha512-DtSuVFB26SI+hj0SjrvXowGTUCHlgevPAIsukssW6BG5MlNSBQAo70wpICBNJL6RjukXg8d2eXaAWuD/CqL00A==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-point-on-line@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-point-on-line/-/boolean-point-on-line-6.5.0.tgz#a8efa7bad88760676f395afb9980746bc5b376e9"
    integrity sha512-A1BbuQ0LceLHvq7F/P7w3QvfpmZqbmViIUPHdNLvZimFNLo4e6IQunmzbe+8aSStH9QRZm3VOflyvNeXvvpZEQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-within@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-within/-/boolean-within-6.5.0.tgz#31a749d3be51065da8c470a1e5613f4d2efdee06"
    integrity sha512-YQB3oU18Inx35C/LU930D36RAVe7LDXk1kWsQ8mLmuqYn9YdPsDQTMTkLJMhoQ8EbN7QTdy333xRQ4MYgToteQ==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/boolean-point-on-line" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/buffer@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/buffer/-/buffer-6.5.0.tgz#22bd0d05b4e1e73eaebc69b8f574a410ff704842"
    integrity sha512-qeX4N6+PPWbKqp1AVkBVWFerGjMYMUyencwfnkCesoznU6qvfugFHNAngNqIBVnJjZ5n8IFyOf+akcxnrt9sNg==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/center" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/projection" "^6.5.0"
      d3-geo "1.7.1"
      turf-jsts "*"
  
  "@turf/center-mean@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/center-mean/-/center-mean-6.5.0.tgz#2dc329c003f8012ba9ae7812a61b5647e1ae86a2"
    integrity sha512-AAX6f4bVn12pTVrMUiB9KrnV94BgeBKpyg3YpfnEbBpkN/znfVhL8dG8IxMAxAoSZ61Zt9WLY34HfENveuOZ7Q==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/center-median@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/center-median/-/center-median-6.5.0.tgz#1b68e3f288af47f76c247d6bf671f30d8c25c974"
    integrity sha512-dT8Ndu5CiZkPrj15PBvslpuf01ky41DEYEPxS01LOxp5HOUHXp1oJxsPxvc+i/wK4BwccPNzU1vzJ0S4emd1KQ==
    dependencies:
      "@turf/center-mean" "^6.5.0"
      "@turf/centroid" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/center-of-mass@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/center-of-mass/-/center-of-mass-6.5.0.tgz#f9e6988bc296b7f763a0137ad6095f54843cf06a"
    integrity sha512-EWrriU6LraOfPN7m1jZi+1NLTKNkuIsGLZc2+Y8zbGruvUW+QV7K0nhf7iZWutlxHXTBqEXHbKue/o79IumAsQ==
    dependencies:
      "@turf/centroid" "^6.5.0"
      "@turf/convex" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/center@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/center/-/center-6.5.0.tgz#3bcb6bffcb8ba147430cfea84aabaed5dbdd4f07"
    integrity sha512-T8KtMTfSATWcAX088rEDKjyvQCBkUsLnK/Txb6/8WUXIeOZyHu42G7MkdkHRoHtwieLdduDdmPLFyTdG5/e7ZQ==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/centroid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/centroid/-/centroid-6.5.0.tgz#ecaa365412e5a4d595bb448e7dcdacfb49eb0009"
    integrity sha512-MwE1oq5E3isewPprEClbfU5pXljIK/GUOMbn22UM3IFPDJX0KeoyLNwghszkdmFp/qMGL/M13MMWvU+GNLXP/A==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/circle@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/circle/-/circle-6.5.0.tgz#****************************************"
    integrity sha512-oU1+Kq9DgRnoSbWFHKnnUdTmtcRUMmHoV9DjTXu9vOLNV5OWtAAh1VZ+mzsioGGzoDNT/V5igbFOkMfBQc0B6A==
    dependencies:
      "@turf/destination" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/clean-coords@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clean-coords/-/clean-coords-6.5.0.tgz#6690adf764ec4b649710a8a20dab7005efbea53f"
    integrity sha512-EMX7gyZz0WTH/ET7xV8MyrExywfm9qUi0/MY89yNffzGIEHuFfqwhcCqZ8O00rZIPZHUTxpmsxQSTfzJJA1CPw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/clone@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clone/-/clone-6.5.0.tgz#895860573881ae10a02dfff95f274388b1cda51a"
    integrity sha512-mzVtTFj/QycXOn6ig+annKrM6ZlimreKYz6f/GSERytOpgzodbQyOgkfwru100O1KQhhjSudKK4DsQ0oyi9cTw==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/clusters-dbscan@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clusters-dbscan/-/clusters-dbscan-6.5.0.tgz#e01f854d24fac4899009fc6811854424ea8f0985"
    integrity sha512-SxZEE4kADU9DqLRiT53QZBBhu8EP9skviSyl+FGj08Y01xfICM/RR9ACUdM0aEQimhpu+ZpRVcUK+2jtiCGrYQ==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
      density-clustering "1.3.0"
  
  "@turf/clusters-kmeans@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clusters-kmeans/-/clusters-kmeans-6.5.0.tgz#aca6f66858af6476b7352a2bbbb392f9ddb7f5b4"
    integrity sha512-DwacD5+YO8kwDPKaXwT9DV46tMBVNsbi1IzdajZu1JDSWoN7yc7N9Qt88oi+p30583O0UPVkAK+A10WAQv4mUw==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      skmeans "0.9.7"
  
  "@turf/clusters@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clusters/-/clusters-6.5.0.tgz#a5ee7b62cdf345db2f1eafe2eb382adc186163e1"
    integrity sha512-Y6gfnTJzQ1hdLfCsyd5zApNbfLIxYEpmDibHUqR5z03Lpe02pa78JtgrgUNt1seeO/aJ4TG1NLN8V5gOrHk04g==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/collect@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/collect/-/collect-6.5.0.tgz#3749ca7d4b91fbcbe1b9b8858ed70df8b6290910"
    integrity sha512-4dN/T6LNnRg099m97BJeOcTA5fSI8cu87Ydgfibewd2KQwBexO69AnjEFqfPX3Wj+Zvisj1uAVIZbPmSSrZkjg==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      rbush "2.x"
  
  "@turf/combine@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/combine/-/combine-6.5.0.tgz#e0f3468ac9c09c24fa7184ebbd8a79d2e595ef81"
    integrity sha512-Q8EIC4OtAcHiJB3C4R+FpB4LANiT90t17uOd851qkM2/o6m39bfN5Mv0PWqMZIHWrrosZqRqoY9dJnzz/rJxYQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/concave@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/concave/-/concave-6.5.0.tgz#19ab1a3f04087c478cebc5e631293f3eeb2e7ce4"
    integrity sha512-I/sUmUC8TC5h/E2vPwxVht+nRt+TnXIPRoztDFvS8/Y0+cBDple9inLSo9nnPXMXidrBlGXZ9vQx/BjZUJgsRQ==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/tin" "^6.5.0"
      topojson-client "3.x"
      topojson-server "3.x"
  
  "@turf/convex@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/convex/-/convex-6.5.0.tgz#a7613e0d3795e2f5b9ce79a39271e86f54a3d354"
    integrity sha512-x7ZwC5z7PJB0SBwNh7JCeCNx7Iu+QSrH7fYgK0RhhNop13TqUlvHMirMLRgf2db1DqUetrAO2qHJeIuasquUWg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
      concaveman "*"
  
  "@turf/destination@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/destination/-/destination-6.5.0.tgz#30a84702f9677d076130e0440d3223ae503fdae1"
    integrity sha512-4cnWQlNC8d1tItOz9B4pmJdWpXqS0vEvv65bI/Pj/genJnsL7evI0/Xw42RvEGROS481MPiU80xzvwxEvhQiMQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/difference@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/difference/-/difference-6.5.0.tgz#677b0d5641a93bba2e82f2c683f0d880105b3197"
    integrity sha512-l8iR5uJqvI+5Fs6leNbhPY5t/a3vipUF/3AeVLpwPQcgmedNXyheYuy07PcMGH5Jdpi5gItOiTqwiU/bUH4b3A==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/dissolve@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/dissolve/-/dissolve-6.5.0.tgz#65debed7ef185087d842b450ebd01e81cc2e80f6"
    integrity sha512-WBVbpm9zLTp0Bl9CE35NomTaOL1c4TQCtEoO43YaAhNEWJOOIhZMFJyr8mbvYruKl817KinT3x7aYjjCMjTAsQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/distance-weight@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/distance-weight/-/distance-weight-6.5.0.tgz#fe1fb45b5ae5ca4e09a898cb0a15c6c79ed0849e"
    integrity sha512-a8qBKkgVNvPKBfZfEJZnC3DV7dfIsC3UIdpRci/iap/wZLH41EmS90nM+BokAJflUHYy8PqE44wySGWHN1FXrQ==
    dependencies:
      "@turf/centroid" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/distance@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/distance/-/distance-6.5.0.tgz#21f04d5f86e864d54e2abde16f35c15b4f36149a"
    integrity sha512-xzykSLfoURec5qvQJcfifw/1mJa+5UwByZZ5TZ8iaqjGYN0vomhV9aiSLeYdUGtYRESZ+DYC/OzY+4RclZYgMg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/ellipse@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/ellipse/-/ellipse-6.5.0.tgz#1e20cc9eb968f35ab891572892a0bffcef5e552a"
    integrity sha512-kuXtwFviw/JqnyJXF1mrR/cb496zDTSbGKtSiolWMNImYzGGkbsAsFTjwJYgD7+4FixHjp0uQPzo70KDf3AIBw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
      "@turf/transform-rotate" "^6.5.0"
  
  "@turf/envelope@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/envelope/-/envelope-6.5.0.tgz#73e81b9b7ed519bd8a614d36322d6f9fbeeb0579"
    integrity sha512-9Z+FnBWvOGOU4X+fMZxYFs1HjFlkKqsddLuMknRaqcJd6t+NIv5DWvPtDL8ATD2GEExYDiFLwMdckfr1yqJgHA==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/bbox-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/explode@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/explode/-/explode-6.5.0.tgz#02c292cc143dd629643da5b70bb5b19b9f0f1c6b"
    integrity sha512-6cSvMrnHm2qAsace6pw9cDmK2buAlw8+tjeJVXMfMyY+w7ZUi1rprWMsY92J7s2Dar63Bv09n56/1V7+tcj52Q==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/flatten@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/flatten/-/flatten-6.5.0.tgz#0bd26161f4f1759bbad6ba9485e8ee65f3fa72a7"
    integrity sha512-IBZVwoNLVNT6U/bcUUllubgElzpMsNoCw8tLqBw6dfYg9ObGmpEjf9BIYLr7a2Yn5ZR4l7YIj2T7kD5uJjZADQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/flip@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/flip/-/flip-6.5.0.tgz#04b38eae8a78f2cf9240140b25401b16b37d20e2"
    integrity sha512-oyikJFNjt2LmIXQqgOGLvt70RgE2lyzPMloYWM7OR5oIFGRiBvqVD2hA6MNw6JewIm30fWZ8DQJw1NHXJTJPbg==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/great-circle@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/great-circle/-/great-circle-6.5.0.tgz#****************************************"
    integrity sha512-7ovyi3HaKOXdFyN7yy1yOMa8IyOvV46RC1QOQTT+RYUN8ke10eyqExwBpL9RFUPvlpoTzoYbM/+lWPogQlFncg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/helpers@6.x", "@turf/helpers@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/helpers/-/helpers-6.5.0.tgz#f79af094bd6b8ce7ed2bd3e089a8493ee6cae82e"
    integrity sha512-VbI1dV5bLFzohYYdgqwikdMVpe7pJ9X3E+dlr425wa2/sMJqYDhTO++ec38/pcPvPE6oD9WEEeU3Xu3gza+VPw==
  
  "@turf/helpers@^7.2.0":
    version "7.2.0"
    resolved "https://registry.npmmirror.com/@turf/helpers/-/helpers-7.2.0.tgz#5771308108c98d608eb8e7f16dcd0eb3fb8a3417"
    integrity sha512-cXo7bKNZoa7aC7ydLmUR02oB3IgDe7MxiPuRz3cCtYQHn+BJ6h1tihmamYDWWUlPHgSNF0i3ATc4WmDECZafKw==
    dependencies:
      "@types/geojson" "^7946.0.10"
      tslib "^2.8.1"
  
  "@turf/hex-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/hex-grid/-/hex-grid-6.5.0.tgz#aa5ee46e291839d4405db74b7516c6da89ee56f7"
    integrity sha512-Ln3tc2tgZT8etDOldgc6e741Smg1CsMKAz1/Mlel+MEL5Ynv2mhx3m0q4J9IB1F3a4MNjDeVvm8drAaf9SF33g==
    dependencies:
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/intersect" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/interpolate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/interpolate/-/interpolate-6.5.0.tgz#9120def5d4498dd7b7d5e92a263aac3e1fd92886"
    integrity sha512-LSH5fMeiGyuDZ4WrDJNgh81d2DnNDUVJtuFryJFup8PV8jbs46lQGfI3r1DJ2p1IlEJIz3pmAZYeTfMMoeeohw==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/centroid" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/hex-grid" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/point-grid" "^6.5.0"
      "@turf/square-grid" "^6.5.0"
      "@turf/triangle-grid" "^6.5.0"
  
  "@turf/intersect@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/intersect/-/intersect-6.5.0.tgz#a14e161ddd0264d0f07ac4e325553c70c421f9e6"
    integrity sha512-2legGJeKrfFkzntcd4GouPugoqPUjexPZnOvfez+3SfIMrHvulw8qV8u7pfVyn2Yqs53yoVCEjS5sEpvQ5YRQg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/invariant@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/invariant/-/invariant-6.5.0.tgz#970afc988023e39c7ccab2341bd06979ddc7463f"
    integrity sha512-Wv8PRNCtPD31UVbdJE/KVAWKe7l6US+lJItRR/HOEW3eh+U/JwRCSUl/KZ7bmjM/C+zLNoreM2TU6OoLACs4eg==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/isobands@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/isobands/-/isobands-6.5.0.tgz#5e0929d9d8d53147074a5cfe4533768782e2a2ce"
    integrity sha512-4h6sjBPhRwMVuFaVBv70YB7eGz+iw0bhPRnp+8JBdX1UPJSXhoi/ZF2rACemRUr0HkdVB/a1r9gC32vn5IAEkw==
    dependencies:
      "@turf/area" "^6.5.0"
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/explode" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      object-assign "*"
  
  "@turf/isolines@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/isolines/-/isolines-6.5.0.tgz#3435c7cb5a79411207a5657aa4095357cfd35831"
    integrity sha512-6ElhiLCopxWlv4tPoxiCzASWt/jMRvmp6mRYrpzOm3EUl75OhHKa/Pu6Y9nWtCMmVC/RcWtiiweUocbPLZLm0A==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      object-assign "*"
  
  "@turf/kinks@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/kinks/-/kinks-6.5.0.tgz#80e7456367535365012f658cf1a988b39a2c920b"
    integrity sha512-ViCngdPt1eEL7hYUHR2eHR662GvCgTc35ZJFaNR6kRtr6D8plLaDju0FILeFFWSc+o8e3fwxZEJKmFj9IzPiIQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/length@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/length/-/length-6.5.0.tgz#ff4e9072d5f997e1c32a1311d214d184463f83fa"
    integrity sha512-5pL5/pnw52fck3oRsHDcSGrj9HibvtlrZ0QNy2OcW8qBFDNgZ4jtl6U7eATVoyWPKBHszW3dWETW+iLV7UARig==
    dependencies:
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/line-arc@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-arc/-/line-arc-6.5.0.tgz#5ca35516ccf1f3a01149889d9facb39a77b07431"
    integrity sha512-I6c+V6mIyEwbtg9P9zSFF89T7QPe1DPTG3MJJ6Cm1MrAY0MdejwQKOpsvNl8LDU2ekHOlz2kHpPVR7VJsoMllA==
    dependencies:
      "@turf/circle" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/line-chunk@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-chunk/-/line-chunk-6.5.0.tgz#02cefa74564b9cf533a3ac8a5109c97cb7170d10"
    integrity sha512-i1FGE6YJaaYa+IJesTfyRRQZP31QouS+wh/pa6O3CC0q4T7LtHigyBSYjrbjSLfn2EVPYGlPCMFEqNWCOkC6zg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/length" "^6.5.0"
      "@turf/line-slice-along" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/line-intersect@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-intersect/-/line-intersect-6.5.0.tgz#dea48348b30c093715d2195d2dd7524aee4cf020"
    integrity sha512-CS6R1tZvVQD390G9Ea4pmpM6mJGPWoL82jD46y0q1KSor9s6HupMIo1kY4Ny+AEYQl9jd21V3Scz20eldpbTVA==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/meta" "^6.5.0"
      geojson-rbush "3.x"
  
  "@turf/line-offset@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-offset/-/line-offset-6.5.0.tgz#2bbd8fcf9ff82009b72890863da444b190e53689"
    integrity sha512-CEXZbKgyz8r72qRvPchK0dxqsq8IQBdH275FE6o4MrBkzMcoZsfSjghtXzKaz9vvro+HfIXal0sTk2mqV1lQTw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/line-overlap@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-overlap/-/line-overlap-6.5.0.tgz#10ebb805c2d047463379fc1f997785fa8f3f4cc1"
    integrity sha512-xHOaWLd0hkaC/1OLcStCpfq55lPHpPNadZySDXYiYjEz5HXr1oKmtMYpn0wGizsLwrOixRdEp+j7bL8dPt4ojQ==
    dependencies:
      "@turf/boolean-point-on-line" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/nearest-point-on-line" "^6.5.0"
      deep-equal "1.x"
      geojson-rbush "3.x"
  
  "@turf/line-segment@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-segment/-/line-segment-6.5.0.tgz#ee73f3ffcb7c956203b64ed966d96af380a4dd65"
    integrity sha512-jI625Ho4jSuJESNq66Mmi290ZJ5pPZiQZruPVpmHkUw257Pew0alMmb6YrqYNnLUuiVVONxAAKXUVeeUGtycfw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/line-slice-along@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-slice-along/-/line-slice-along-6.5.0.tgz#6e7a861d72c6f80caba2c4418b69a776f0292953"
    integrity sha512-KHJRU6KpHrAj+BTgTNqby6VCTnDzG6a1sJx/I3hNvqMBLvWVA2IrkR9L9DtsQsVY63IBwVdQDqiwCuZLDQh4Ng==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/line-slice@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-slice/-/line-slice-6.5.0.tgz#7b6e0c8e8e93fdb4e65c3b9a123a2ec93a21bdb0"
    integrity sha512-vDqJxve9tBHhOaVVFXqVjF5qDzGtKWviyjbyi2QnSnxyFAmLlLnBfMX8TLQCAf2GxHibB95RO5FBE6I2KVPRuw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/nearest-point-on-line" "^6.5.0"
  
  "@turf/line-split@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-split/-/line-split-6.5.0.tgz#116d7fbf714457878225187f5820ef98db7b02c2"
    integrity sha512-/rwUMVr9OI2ccJjw7/6eTN53URtGThNSD5I0GgxyFXMtxWiloRJ9MTff8jBbtPWrRka/Sh2GkwucVRAEakx9Sw==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/nearest-point-on-line" "^6.5.0"
      "@turf/square" "^6.5.0"
      "@turf/truncate" "^6.5.0"
      geojson-rbush "3.x"
  
  "@turf/line-to-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-to-polygon/-/line-to-polygon-6.5.0.tgz#c919a03064a1cd5cef4c4e4d98dc786e12ffbc89"
    integrity sha512-qYBuRCJJL8Gx27OwCD1TMijM/9XjRgXH/m/TyuND4OXedBpIWlK5VbTIO2gJ8OCfznBBddpjiObLBrkuxTpN4Q==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/mask@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/mask/-/mask-6.5.0.tgz#a97f355ee071ac60d8d3782ae39e5bb4b4e26857"
    integrity sha512-RQha4aU8LpBrmrkH8CPaaoAfk0Egj5OuXtv6HuCQnHeGNOQt3TQVibTA3Sh4iduq4EPxnZfDjgsOeKtrCA19lg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/meta@6.x", "@turf/meta@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/meta/-/meta-6.5.0.tgz#b725c3653c9f432133eaa04d3421f7e51e0418ca"
    integrity sha512-RrArvtsV0vdsCBegoBtOalgdSOfkBrTJ07VkpiCnq/491W67hnMWmDu7e6Ztw0C3WldRYTXkg3SumfdzZxLBHA==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/meta@^7.2.0":
    version "7.2.0"
    resolved "https://registry.npmmirror.com/@turf/meta/-/meta-7.2.0.tgz#6a6b1918890b4d9d2b5ff10b3ad47e2fd7470912"
    integrity sha512-igzTdHsQc8TV1RhPuOLVo74Px/hyPrVgVOTgjWQZzt3J9BVseCdpfY/0cJBdlSRI4S/yTmmHl7gAqjhpYH5Yaw==
    dependencies:
      "@turf/helpers" "^7.2.0"
      "@types/geojson" "^7946.0.10"
  
  "@turf/midpoint@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/midpoint/-/midpoint-6.5.0.tgz#5f9428959309feccaf3f55873a8de70d4121bdce"
    integrity sha512-MyTzV44IwmVI6ec9fB2OgZ53JGNlgOpaYl9ArKoF49rXpL84F9rNATndbe0+MQIhdkw8IlzA6xVP4lZzfMNVCw==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/moran-index@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/moran-index/-/moran-index-6.5.0.tgz#456264bfb014a7b5f527807c9dcf25df3c6b2efd"
    integrity sha512-ItsnhrU2XYtTtTudrM8so4afBCYWNaB0Mfy28NZwLjB5jWuAsvyV+YW+J88+neK/ougKMTawkmjQqodNJaBeLQ==
    dependencies:
      "@turf/distance-weight" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/nearest-point-on-line@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/nearest-point-on-line/-/nearest-point-on-line-6.5.0.tgz#8e1cd2cdc0b5acaf4c8d8b3b33bb008d3cb99e7b"
    integrity sha512-WthrvddddvmymnC+Vf7BrkHGbDOUu6Z3/6bFYUGv1kxw8tiZ6n83/VG6kHz4poHOfS0RaNflzXSkmCi64fLBlg==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/nearest-point-to-line@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/nearest-point-to-line/-/nearest-point-to-line-6.5.0.tgz#5549b48690d523f9af4765fe64a3cbebfbc6bb75"
    integrity sha512-PXV7cN0BVzUZdjj6oeb/ESnzXSfWmEMrsfZSDRgqyZ9ytdiIj/eRsnOXLR13LkTdXVOJYDBuf7xt1mLhM4p6+Q==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/point-to-line-distance" "^6.5.0"
      object-assign "*"
  
  "@turf/nearest-point@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/nearest-point/-/nearest-point-6.5.0.tgz#2f1781c26ff3f054005d4ff352042973318b92f1"
    integrity sha512-fguV09QxilZv/p94s8SMsXILIAMiaXI5PATq9d7YWijLxWUj6Q/r43kxyoi78Zmwwh1Zfqz9w+bCYUAxZ5+euA==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/planepoint@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/planepoint/-/planepoint-6.5.0.tgz#5cb788670c31a6b064ae464180d51b4d550d87de"
    integrity sha512-R3AahA6DUvtFbka1kcJHqZ7DMHmPXDEQpbU5WaglNn7NaCQg9HB0XM0ZfqWcd5u92YXV+Gg8QhC8x5XojfcM4Q==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/point-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/point-grid/-/point-grid-6.5.0.tgz#f628d30afe29d60dcbf54b195e46eab48a4fbfaa"
    integrity sha512-Iq38lFokNNtQJnOj/RBKmyt6dlof0yhaHEDELaWHuECm1lIZLY3ZbVMwbs+nXkwTAHjKfS/OtMheUBkw+ee49w==
    dependencies:
      "@turf/boolean-within" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/point-on-feature@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/point-on-feature/-/point-on-feature-6.5.0.tgz#37d07afeb31896e53c0833aa404993ba7d500f0c"
    integrity sha512-bDpuIlvugJhfcF/0awAQ+QI6Om1Y1FFYE8Y/YdxGRongivix850dTeXCo0mDylFdWFPGDo7Mmh9Vo4VxNwW/TA==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/center" "^6.5.0"
      "@turf/explode" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/nearest-point" "^6.5.0"
  
  "@turf/point-to-line-distance@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/point-to-line-distance/-/point-to-line-distance-6.5.0.tgz#bc46fe09ea630aaf73f13c40b38a7df79050fff8"
    integrity sha512-opHVQ4vjUhNBly1bob6RWy+F+hsZDH9SA0UW36pIRzfpu27qipU18xup0XXEePfY6+wvhF6yL/WgCO2IbrLqEA==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/projection" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
      "@turf/rhumb-distance" "^6.5.0"
  
  "@turf/points-within-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/points-within-polygon/-/points-within-polygon-6.5.0.tgz#d49f4d7cf19b7a440bf1e06f771ff4e1df13107f"
    integrity sha512-YyuheKqjliDsBDt3Ho73QVZk1VXX1+zIA2gwWvuz8bR1HXOkcuwk/1J76HuFMOQI3WK78wyAi+xbkx268PkQzQ==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/polygon-smooth@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/polygon-smooth/-/polygon-smooth-6.5.0.tgz#00ca366871cb6ea3bee44ff3ea870aaf75711733"
    integrity sha512-LO/X/5hfh/Rk4EfkDBpLlVwt3i6IXdtQccDT9rMjXEP32tRgy0VMFmdkNaXoGlSSKf/1mGqLl4y4wHd86DqKbg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/polygon-tangents@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/polygon-tangents/-/polygon-tangents-6.5.0.tgz#dc025202727ba2f3347baa95dbca4e0ffdb2ddf5"
    integrity sha512-sB4/IUqJMYRQH9jVBwqS/XDitkEfbyqRy+EH/cMRJURTg78eHunvJ708x5r6umXsbiUyQU4eqgPzEylWEQiunw==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-within" "^6.5.0"
      "@turf/explode" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/nearest-point" "^6.5.0"
  
  "@turf/polygon-to-line@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/polygon-to-line/-/polygon-to-line-6.5.0.tgz#4dc86db66168b32bb83ce448cf966208a447d952"
    integrity sha512-5p4n/ij97EIttAq+ewSnKt0ruvuM+LIDzuczSzuHTpq4oS7Oq8yqg5TQ4nzMVuK41r/tALCk7nAoBuw3Su4Gcw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/polygonize@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/polygonize/-/polygonize-6.5.0.tgz#8aa0f1e386e96c533a320c426aaf387020320fa3"
    integrity sha512-a/3GzHRaCyzg7tVYHo43QUChCspa99oK4yPqooVIwTC61npFzdrmnywMv0S+WZjHZwK37BrFJGFrZGf6ocmY5w==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/envelope" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/projection@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/projection/-/projection-6.5.0.tgz#d2aad862370bf03f2270701115464a8406c144b2"
    integrity sha512-/Pgh9mDvQWWu8HRxqpM+tKz8OzgauV+DiOcr3FCjD6ubDnrrmMJlsf6fFJmggw93mtVPrZRL6yyi9aYCQBOIvg==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/random@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/random/-/random-6.5.0.tgz#b19672cf4549557660034d4a303911656df7747e"
    integrity sha512-8Q25gQ/XbA7HJAe+eXp4UhcXM9aOOJFaxZ02+XSNwMvY8gtWSCBLVqRcW4OhqilgZ8PeuQDWgBxeo+BIqqFWFQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/rectangle-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rectangle-grid/-/rectangle-grid-6.5.0.tgz#c3ef38e8cfdb763012beb1f22e2b77288a37a5cf"
    integrity sha512-yQZ/1vbW68O2KsSB3OZYK+72aWz/Adnf7m2CMKcC+aq6TwjxZjAvlbCOsNUnMAuldRUVN1ph6RXMG4e9KEvKvg==
    dependencies:
      "@turf/boolean-intersects" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/rewind@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rewind/-/rewind-6.5.0.tgz#bc0088f8ec56f00c8eacd902bbe51e3786cb73a0"
    integrity sha512-IoUAMcHWotBWYwSYuYypw/LlqZmO+wcBpn8ysrBNbazkFNkLf3btSDZMkKJO/bvOzl55imr/Xj4fi3DdsLsbzQ==
    dependencies:
      "@turf/boolean-clockwise" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/rhumb-bearing@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rhumb-bearing/-/rhumb-bearing-6.5.0.tgz#8c41ad62b44fb4e57c14fe790488056684eee7b9"
    integrity sha512-jMyqiMRK4hzREjQmnLXmkJ+VTNTx1ii8vuqRwJPcTlKbNWfjDz/5JqJlb5NaFDcdMpftWovkW5GevfnuzHnOYA==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/rhumb-destination@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rhumb-destination/-/rhumb-destination-6.5.0.tgz#12da8c85e674b182e8b0ec8ea9c5fe2186716dae"
    integrity sha512-RHNP1Oy+7xTTdRrTt375jOZeHceFbjwohPHlr9Hf68VdHHPMAWgAKqiX2YgSWDcvECVmiGaBKWus1Df+N7eE4Q==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/rhumb-distance@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rhumb-distance/-/rhumb-distance-6.5.0.tgz#ed068004b1469512b857070fbf5cb7b7eabbe592"
    integrity sha512-oKp8KFE8E4huC2Z1a1KNcFwjVOqa99isxNOwfo4g3SUABQ6NezjKDDrnvC4yI5YZ3/huDjULLBvhed45xdCrzg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/sample@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/sample/-/sample-6.5.0.tgz#00cca024514989448e57fb1bf34e9a33ed3f0755"
    integrity sha512-kSdCwY7el15xQjnXYW520heKUrHwRvnzx8ka4eYxX9NFeOxaFITLW2G7UtXb6LJK8mmPXI8Aexv23F2ERqzGFg==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/sector@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/sector/-/sector-6.5.0.tgz#599a87ebbe6ee613b4e04c5928e0ef1fc78fc16c"
    integrity sha512-cYUOkgCTWqa23SOJBqxoFAc/yGCUsPRdn/ovbRTn1zNTm/Spmk6hVB84LCKOgHqvSF25i0d2kWqpZDzLDdAPbw==
    dependencies:
      "@turf/circle" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-arc" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/shortest-path@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/shortest-path/-/shortest-path-6.5.0.tgz#e1fdf9b4758bd20caf845fdc03d0dc2eede2ff0e"
    integrity sha512-4de5+G7+P4hgSoPwn+SO9QSi9HY5NEV/xRJ+cmoFVRwv2CDsuOPDheHKeuIAhKyeKDvPvPt04XYWbac4insJMg==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/bbox-polygon" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/clean-coords" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/transform-scale" "^6.5.0"
  
  "@turf/simplify@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/simplify/-/simplify-6.5.0.tgz#ec435460bde0985b781618b05d97146c32c8bc16"
    integrity sha512-USas3QqffPHUY184dwQdP8qsvcVH/PWBYdXY5am7YTBACaQOMAlf6AKJs9FT8jiO6fQpxfgxuEtwmox+pBtlOg==
    dependencies:
      "@turf/clean-coords" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/square-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/square-grid/-/square-grid-6.5.0.tgz#3a517301b42ed98aa62d727786dc5290998ddbae"
    integrity sha512-mlR0ayUdA+L4c9h7p4k3pX6gPWHNGuZkt2c5II1TJRmhLkW2557d6b/Vjfd1z9OVaajb1HinIs1FMSAPXuuUrA==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/rectangle-grid" "^6.5.0"
  
  "@turf/square@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/square/-/square-6.5.0.tgz#ab43eef99d39c36157ab5b80416bbeba1f6b2122"
    integrity sha512-BM2UyWDmiuHCadVhHXKIx5CQQbNCpOxB6S/aCNOCLbhCeypKX5Q0Aosc5YcmCJgkwO5BERCC6Ee7NMbNB2vHmQ==
    dependencies:
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/standard-deviational-ellipse@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/standard-deviational-ellipse/-/standard-deviational-ellipse-6.5.0.tgz#775c7b9a2be6546bf64ea8ac08cdcd80563f2935"
    integrity sha512-02CAlz8POvGPFK2BKK8uHGUk/LXb0MK459JVjKxLC2yJYieOBTqEbjP0qaWhiBhGzIxSMaqe8WxZ0KvqdnstHA==
    dependencies:
      "@turf/center-mean" "^6.5.0"
      "@turf/ellipse" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/points-within-polygon" "^6.5.0"
  
  "@turf/tag@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/tag/-/tag-6.5.0.tgz#13eae85f36f9fd8c4e076714a894cb5b7716d381"
    integrity sha512-XwlBvrOV38CQsrNfrxvBaAPBQgXMljeU0DV8ExOyGM7/hvuGHJw3y8kKnQ4lmEQcmcrycjDQhP7JqoRv8vFssg==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/tesselate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/tesselate/-/tesselate-6.5.0.tgz#de45b778f8e6a45535d8eb2aacea06f86c6b73fb"
    integrity sha512-M1HXuyZFCfEIIKkglh/r5L9H3c5QTEsnMBoZOFQiRnGPGmJWcaBissGb7mTFX2+DKE7FNWXh4TDnZlaLABB0dQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      earcut "^2.0.0"
  
  "@turf/tin@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/tin/-/tin-6.5.0.tgz#b77bebb48237e6613ac6bc0e37a6658be8c17a09"
    integrity sha512-YLYikRzKisfwj7+F+Tmyy/LE3d2H7D4kajajIfc9mlik2+esG7IolsX/+oUz1biguDYsG0DUA8kVYXDkobukfg==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/transform-rotate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/transform-rotate/-/transform-rotate-6.5.0.tgz#e50e96a8779af91d58149eedb00ffd7f6395c804"
    integrity sha512-A2Ip1v4246ZmpssxpcL0hhiVBEf4L8lGnSPWTgSv5bWBEoya2fa/0SnFX9xJgP40rMP+ZzRaCN37vLHbv1Guag==
    dependencies:
      "@turf/centroid" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
      "@turf/rhumb-distance" "^6.5.0"
  
  "@turf/transform-scale@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/transform-scale/-/transform-scale-6.5.0.tgz#dcccd8b0f139de32e32225a29c107a1279137120"
    integrity sha512-VsATGXC9rYM8qTjbQJ/P7BswKWXHdnSJ35JlV4OsZyHBMxJQHftvmZJsFbOqVtQnIQIzf2OAly6rfzVV9QLr7g==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/center" "^6.5.0"
      "@turf/centroid" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
      "@turf/rhumb-distance" "^6.5.0"
  
  "@turf/transform-translate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/transform-translate/-/transform-translate-6.5.0.tgz#631b13aca6402898029e03fc2d1f4bc1c667fc3e"
    integrity sha512-NABLw5VdtJt/9vSstChp93pc6oel4qXEos56RBMsPlYB8hzNTEKYtC146XJvyF4twJeeYS8RVe1u7KhoFwEM5w==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
  
  "@turf/triangle-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/triangle-grid/-/triangle-grid-6.5.0.tgz#75664e8b9d9c7ca4c845673134a1e0d82b5e6887"
    integrity sha512-2jToUSAS1R1htq4TyLQYPTIsoy6wg3e3BQXjm2rANzw4wPQCXGOxrur1Fy9RtzwqwljlC7DF4tg0OnWr8RjmfA==
    dependencies:
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/intersect" "^6.5.0"
  
  "@turf/truncate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/truncate/-/truncate-6.5.0.tgz#c3a16cad959f1be1c5156157d5555c64b19185d8"
    integrity sha512-pFxg71pLk+eJj134Z9yUoRhIi8vqnnKvCYwdT4x/DQl/19RVdq1tV3yqOT3gcTQNfniteylL5qV1uTBDV5sgrg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/turf@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/turf/-/turf-6.5.0.tgz#49cd07b942a757f3ebbdba6cb294bbb864825a83"
    integrity sha512-ipMCPnhu59bh92MNt8+pr1VZQhHVuTMHklciQURo54heoxRzt1neNYZOBR6jdL+hNsbDGAECMuIpAutX+a3Y+w==
    dependencies:
      "@turf/along" "^6.5.0"
      "@turf/angle" "^6.5.0"
      "@turf/area" "^6.5.0"
      "@turf/bbox" "^6.5.0"
      "@turf/bbox-clip" "^6.5.0"
      "@turf/bbox-polygon" "^6.5.0"
      "@turf/bearing" "^6.5.0"
      "@turf/bezier-spline" "^6.5.0"
      "@turf/boolean-clockwise" "^6.5.0"
      "@turf/boolean-contains" "^6.5.0"
      "@turf/boolean-crosses" "^6.5.0"
      "@turf/boolean-disjoint" "^6.5.0"
      "@turf/boolean-equal" "^6.5.0"
      "@turf/boolean-intersects" "^6.5.0"
      "@turf/boolean-overlap" "^6.5.0"
      "@turf/boolean-parallel" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/boolean-point-on-line" "^6.5.0"
      "@turf/boolean-within" "^6.5.0"
      "@turf/buffer" "^6.5.0"
      "@turf/center" "^6.5.0"
      "@turf/center-mean" "^6.5.0"
      "@turf/center-median" "^6.5.0"
      "@turf/center-of-mass" "^6.5.0"
      "@turf/centroid" "^6.5.0"
      "@turf/circle" "^6.5.0"
      "@turf/clean-coords" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/clusters" "^6.5.0"
      "@turf/clusters-dbscan" "^6.5.0"
      "@turf/clusters-kmeans" "^6.5.0"
      "@turf/collect" "^6.5.0"
      "@turf/combine" "^6.5.0"
      "@turf/concave" "^6.5.0"
      "@turf/convex" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/difference" "^6.5.0"
      "@turf/dissolve" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/distance-weight" "^6.5.0"
      "@turf/ellipse" "^6.5.0"
      "@turf/envelope" "^6.5.0"
      "@turf/explode" "^6.5.0"
      "@turf/flatten" "^6.5.0"
      "@turf/flip" "^6.5.0"
      "@turf/great-circle" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/hex-grid" "^6.5.0"
      "@turf/interpolate" "^6.5.0"
      "@turf/intersect" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/isobands" "^6.5.0"
      "@turf/isolines" "^6.5.0"
      "@turf/kinks" "^6.5.0"
      "@turf/length" "^6.5.0"
      "@turf/line-arc" "^6.5.0"
      "@turf/line-chunk" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/line-offset" "^6.5.0"
      "@turf/line-overlap" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/line-slice" "^6.5.0"
      "@turf/line-slice-along" "^6.5.0"
      "@turf/line-split" "^6.5.0"
      "@turf/line-to-polygon" "^6.5.0"
      "@turf/mask" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/midpoint" "^6.5.0"
      "@turf/moran-index" "^6.5.0"
      "@turf/nearest-point" "^6.5.0"
      "@turf/nearest-point-on-line" "^6.5.0"
      "@turf/nearest-point-to-line" "^6.5.0"
      "@turf/planepoint" "^6.5.0"
      "@turf/point-grid" "^6.5.0"
      "@turf/point-on-feature" "^6.5.0"
      "@turf/point-to-line-distance" "^6.5.0"
      "@turf/points-within-polygon" "^6.5.0"
      "@turf/polygon-smooth" "^6.5.0"
      "@turf/polygon-tangents" "^6.5.0"
      "@turf/polygon-to-line" "^6.5.0"
      "@turf/polygonize" "^6.5.0"
      "@turf/projection" "^6.5.0"
      "@turf/random" "^6.5.0"
      "@turf/rewind" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
      "@turf/rhumb-distance" "^6.5.0"
      "@turf/sample" "^6.5.0"
      "@turf/sector" "^6.5.0"
      "@turf/shortest-path" "^6.5.0"
      "@turf/simplify" "^6.5.0"
      "@turf/square" "^6.5.0"
      "@turf/square-grid" "^6.5.0"
      "@turf/standard-deviational-ellipse" "^6.5.0"
      "@turf/tag" "^6.5.0"
      "@turf/tesselate" "^6.5.0"
      "@turf/tin" "^6.5.0"
      "@turf/transform-rotate" "^6.5.0"
      "@turf/transform-scale" "^6.5.0"
      "@turf/transform-translate" "^6.5.0"
      "@turf/triangle-grid" "^6.5.0"
      "@turf/truncate" "^6.5.0"
      "@turf/union" "^6.5.0"
      "@turf/unkink-polygon" "^6.5.0"
      "@turf/voronoi" "^6.5.0"
  
  "@turf/union@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/union/-/union-6.5.0.tgz#82d28f55190608f9c7d39559b7f543393b03b82d"
    integrity sha512-igYWCwP/f0RFHIlC2c0SKDuM/ObBaqSljI3IdV/x71805QbIvY/BYGcJdyNcgEA6cylIGl/0VSlIbpJHZ9ldhw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/unkink-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/unkink-polygon/-/unkink-polygon-6.5.0.tgz#9e54186dcce08d7e62f608c8fa2d3f0342ebe826"
    integrity sha512-8QswkzC0UqKmN1DT6HpA9upfa1HdAA5n6bbuzHy8NJOX8oVizVAqfEPY0wqqTgboDjmBR4yyImsdPGUl3gZ8JQ==
    dependencies:
      "@turf/area" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
      rbush "^2.0.1"
  
  "@turf/voronoi@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/voronoi/-/voronoi-6.5.0.tgz#afe6715a5c7eff687434010cde45cd4822489434"
    integrity sha512-C/xUsywYX+7h1UyNqnydHXiun4UPjK88VDghtoRypR9cLlb7qozkiLRphQxxsCM0KxyxpVPHBVQXdAL3+Yurow==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      d3-voronoi "1.1.2"
  
  "@types/geojson@7946.0.8":
    version "7946.0.8"
    resolved "https://registry.npmmirror.com/@types/geojson/-/geojson-7946.0.8.tgz#30744afdb385e2945e22f3b033f897f76b1f12ca"
    integrity sha512-1rkryxURpr6aWP7R786/UQOkJ3PcpQiWkAXBmdWc7ryFWqN6a4xfK7BtjXvFBKO9LjQ+MWQSWxYeZX1OApnArA==
  
  "@types/geojson@^7946.0.10":
    version "7946.0.16"
    resolved "https://registry.npmmirror.com/@types/geojson/-/geojson-7946.0.16.tgz#8ebe53d69efada7044454e3305c19017d97ced2a"
    integrity sha512-6C8nqWur3j98U6+lXDfTUWIfgvZU+EumvpHKcYjujKH7woYyLj2sUmff0tRhrqM7BohUw7Pz3ZB1jj2gW9Fvmg==
  
  "@types/json5@^0.0.29":
    version "0.0.29"
    resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
    integrity sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==
  
  acorn-jsx@^5.3.2:
    version "5.3.2"
    resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
    integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==
  
  acorn@^8.8.0:
    version "8.8.2"
    resolved "https://registry.npmjs.org/acorn/-/acorn-8.8.2.tgz"
    integrity sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw==
  
  ajv@^6.10.0, ajv@^6.12.4:
    version "6.12.6"
    resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
    integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
    dependencies:
      fast-deep-equal "^3.1.1"
      fast-json-stable-stringify "^2.0.0"
      json-schema-traverse "^0.4.1"
      uri-js "^4.2.2"
  
  ansi-regex@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
    integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==
  
  ansi-styles@^3.2.1:
    version "3.2.1"
    resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
    integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
    dependencies:
      color-convert "^1.9.0"
  
  ansi-styles@^4.1.0:
    version "4.3.0"
    resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
    integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
    dependencies:
      color-convert "^2.0.1"
  
  argparse@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
    integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==
  
  array-buffer-byte-length@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.0.tgz"
    integrity sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==
    dependencies:
      call-bind "^1.0.2"
      is-array-buffer "^3.0.1"
  
  array-includes@^3.1.6:
    version "3.1.6"
    resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.6.tgz"
    integrity sha512-sgTbLvL6cNnw24FnbaDyjmvddQ2ML8arZsgaJhoABMoplz/4QRhtrYS+alr1BUM1Bwp6dhx8vVCBSLG+StwOFw==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
      get-intrinsic "^1.1.3"
      is-string "^1.0.7"
  
  array.prototype.flat@^1.3.1:
    version "1.3.1"
    resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.1.tgz"
    integrity sha512-roTU0KWIOmJ4DRLmwKd19Otg0/mT3qPNt0Qb3GWW8iObuZXxrjB/pzn0R3hqpRSWg4HCwqx+0vwOnWnvlOyeIA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
      es-shim-unscopables "^1.0.0"
  
  array.prototype.flatmap@^1.3.1:
    version "1.3.1"
    resolved "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.1.tgz"
    integrity sha512-8UGn9O1FDVvMNB0UlLv4voxRMze7+FpHyF5mSMRjWHUMlpoDViniy05870VlxhfgTnLbpuwTzvD76MTtWxB/mQ==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
      es-shim-unscopables "^1.0.0"
  
  available-typed-arrays@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz"
    integrity sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==
  
  axios-miniprogram-adapter@^0.3.2:
    version "0.3.3"
    resolved "https://registry.npmjs.org/axios-miniprogram-adapter/-/axios-miniprogram-adapter-0.3.3.tgz"
    integrity sha512-TjC3APVcGZCj6T6j9lUiVk7GsvwZSCZQ96AQHopNWoyd0foA4pM0m5Wfsgbmea1F3VBn0ivZlI3H69DSl1QzlQ==
    dependencies:
      axios "^0.19.2"
  
  axios@^0.19.2:
    version "0.19.2"
    resolved "https://registry.npmjs.org/axios/-/axios-0.19.2.tgz"
    integrity sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==
    dependencies:
      follow-redirects "1.5.10"
  
  axios@^0.26.1:
    version "0.26.1"
    resolved "https://registry.npmjs.org/axios/-/axios-0.26.1.tgz"
    integrity sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==
    dependencies:
      follow-redirects "^1.14.8"
  
  balanced-match@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
    integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==
  
  boolbase@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
    integrity sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==
  
  brace-expansion@^1.1.7:
    version "1.1.11"
    resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
    integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
    dependencies:
      balanced-match "^1.0.0"
      concat-map "0.0.1"
  
  browserslist@^4.21.3:
    version "4.21.5"
    resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.21.5.tgz"
    integrity sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w==
    dependencies:
      caniuse-lite "^1.0.30001449"
      electron-to-chromium "^1.4.284"
      node-releases "^2.0.8"
      update-browserslist-db "^1.0.10"
  
  call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz#4b5428c222be985d79c3d82657479dbe0b59b2d6"
    integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
    dependencies:
      es-errors "^1.3.0"
      function-bind "^1.1.2"
  
  call-bind@^1.0.0, call-bind@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz"
    integrity sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==
    dependencies:
      function-bind "^1.1.1"
      get-intrinsic "^1.0.2"
  
  call-bind@^1.0.7, call-bind@^1.0.8:
    version "1.0.8"
    resolved "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.8.tgz#0736a9660f537e3388826f440d5ec45f744eaa4c"
    integrity sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==
    dependencies:
      call-bind-apply-helpers "^1.0.0"
      es-define-property "^1.0.0"
      get-intrinsic "^1.2.4"
      set-function-length "^1.2.2"
  
  call-bound@^1.0.2:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/call-bound/-/call-bound-1.0.4.tgz#238de935d2a2a692928c538c7ccfa91067fd062a"
    integrity sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==
    dependencies:
      call-bind-apply-helpers "^1.0.2"
      get-intrinsic "^1.3.0"
  
  callsites@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
    integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==
  
  caniuse-lite@^1.0.30001449:
    version "1.0.30001488"
    resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001488.tgz"
    integrity sha512-NORIQuuL4xGpIy6iCCQGN4iFjlBXtfKWIenlUuyZJumLRIindLb7wXM+GO8erEhb7vXfcnf4BAg2PrSDN5TNLQ==
  
  chalk@^2.0.0:
    version "2.4.2"
    resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
    integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
    dependencies:
      ansi-styles "^3.2.1"
      escape-string-regexp "^1.0.5"
      supports-color "^5.3.0"
  
  chalk@^4.0.0:
    version "4.1.2"
    resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
    integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  color-convert@^1.9.0:
    version "1.9.3"
    resolved "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
    integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
    dependencies:
      color-name "1.1.3"
  
  color-convert@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
    integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
    dependencies:
      color-name "~1.1.4"
  
  color-name@1.1.3:
    version "1.1.3"
    resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
    integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==
  
  color-name@~1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
    integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==
  
  commander@2:
    version "2.20.3"
    resolved "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
    integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==
  
  concat-map@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
    integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==
  
  concaveman@*:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/concaveman/-/concaveman-2.0.0.tgz#5c4cea7e2329addf619ecae627cb721df05d1eac"
    integrity sha512-3a9C//4G44/boNehBPZMRh8XxrwBvTXlhENUim+GMm207WoDie/Vq89U5lkhLn3kKA+vxwmwfdQPWIRwjQWoLA==
    dependencies:
      point-in-polygon "^1.1.0"
      rbush "^4.0.1"
      robust-predicates "^3.0.2"
      tinyqueue "^3.0.0"
  
  confusing-browser-globals@^1.0.10:
    version "1.0.11"
    resolved "https://registry.npmjs.org/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz"
    integrity sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA==
  
  convert-source-map@^1.7.0:
    version "1.9.0"
    resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz"
    integrity sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==
  
  cross-spawn@^7.0.2:
    version "7.0.3"
    resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
    integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
    dependencies:
      path-key "^3.1.0"
      shebang-command "^2.0.0"
      which "^2.0.1"
  
  crypto-js@^4.1.1:
    version "4.1.1"
    resolved "https://registry.npmjs.org/crypto-js/-/crypto-js-4.1.1.tgz"
    integrity sha512-o2JlM7ydqd3Qk9CA0L4NL6mTzU2sdx96a+oOfPu8Mkl/PK51vSyoi8/rQ8NknZtk44vq15lmhAj9CIAGwgeWKw==
  
  csscolorparser@~1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/csscolorparser/-/csscolorparser-1.0.3.tgz#b34f391eea4da8f3e98231e2ccd8df9c041f171b"
    integrity sha512-umPSgYwZkdFoUrH5hIq5kf0wPSXiro51nPw0j2K/c83KflkPSTBGMz6NJvMB+07VlL0y7VPo6QJcDjcgKTTm3w==
  
  cssesc@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
    integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==
  
  d3-array@1:
    version "1.2.4"
    resolved "https://registry.npmmirror.com/d3-array/-/d3-array-1.2.4.tgz#635ce4d5eea759f6f605863dbcfc30edc737f71f"
    integrity sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw==
  
  d3-geo@1.7.1:
    version "1.7.1"
    resolved "https://registry.npmmirror.com/d3-geo/-/d3-geo-1.7.1.tgz#44bbc7a218b1fd859f3d8fd7c443ca836569ce99"
    integrity sha512-O4AempWAr+P5qbk2bC2FuN/sDW4z+dN2wDf9QV3bxQt4M5HfOEeXLgJ/UKQW0+o1Dj8BE+L5kiDbdWUMjsmQpw==
    dependencies:
      d3-array "1"
  
  d3-voronoi@1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/d3-voronoi/-/d3-voronoi-1.1.2.tgz#1687667e8f13a2d158c80c1480c5a29cb0d8973c"
    integrity sha512-RhGS1u2vavcO7ay7ZNAPo4xeDh/VYeGof3x5ZLJBQgYhLegxr3s5IykvWmJ94FTU6mcbtp4sloqZ54mP6R4Utw==
  
  dayjs@^1.11.0:
    version "1.11.5"
    resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.11.5.tgz"
    integrity sha512-CAdX5Q3YW3Gclyo5Vpqkgpj8fSdLQcRuzfX6mC6Phy0nfJ0eGYOeS7m4mt2plDWLAtA4TqTakvbboHvUxfe4iA==
  
  debug@=3.1.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz"
    integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
    dependencies:
      ms "2.0.0"
  
  debug@^3.2.7:
    version "3.2.7"
    resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
    integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
    dependencies:
      ms "^2.1.1"
  
  debug@^4.1.0, debug@^4.1.1, debug@^4.3.2, debug@^4.3.4:
    version "4.3.4"
    resolved "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
    integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
    dependencies:
      ms "2.1.2"
  
  deep-equal@1.x, deep-equal@^1.0.0:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/deep-equal/-/deep-equal-1.1.2.tgz#78a561b7830eef3134c7f6f3a3d6af272a678761"
    integrity sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==
    dependencies:
      is-arguments "^1.1.1"
      is-date-object "^1.0.5"
      is-regex "^1.1.4"
      object-is "^1.1.5"
      object-keys "^1.1.1"
      regexp.prototype.flags "^1.5.1"
  
  deep-is@^0.1.3:
    version "0.1.4"
    resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
    integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==
  
  define-data-property@^1.0.1, define-data-property@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
    integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
    dependencies:
      es-define-property "^1.0.0"
      es-errors "^1.3.0"
      gopd "^1.0.1"
  
  define-properties@^1.1.3, define-properties@^1.1.4, define-properties@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.2.0.tgz"
    integrity sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA==
    dependencies:
      has-property-descriptors "^1.0.0"
      object-keys "^1.1.1"
  
  define-properties@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/define-properties/-/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
    integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
    dependencies:
      define-data-property "^1.0.1"
      has-property-descriptors "^1.0.0"
      object-keys "^1.1.1"
  
  density-clustering@1.3.0:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/density-clustering/-/density-clustering-1.3.0.tgz#dc9f59c8f0ab97e1624ac64930fd3194817dcac5"
    integrity sha512-icpmBubVTwLnsaor9qH/4tG5+7+f61VcqMN3V3pm9sxxSCt2Jcs0zWOgwZW9ARJYaKD3FumIgHiMOcIMRRAzFQ==
  
  doctrine@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
    integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
    dependencies:
      esutils "^2.0.2"
  
  doctrine@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
    integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
    dependencies:
      esutils "^2.0.2"
  
  dotenv@^16.0.1:
    version "16.0.1"
    resolved "https://registry.npmjs.org/dotenv/-/dotenv-16.0.1.tgz"
    integrity sha512-1K6hR6wtk2FviQ4kEiSjFiH5rpzEVi8WW0x96aztHVMhEspNpc4DVOUTEHtEva5VThQ8IaBX1Pe4gSzpVVUsKQ==
  
  dunder-proto@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/dunder-proto/-/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
    integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
    dependencies:
      call-bind-apply-helpers "^1.0.1"
      es-errors "^1.3.0"
      gopd "^1.2.0"
  
  earcut@^2.0.0, earcut@^2.2.2:
    version "2.2.4"
    resolved "https://registry.npmmirror.com/earcut/-/earcut-2.2.4.tgz#6d02fd4d68160c114825d06890a92ecaae60343a"
    integrity sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ==
  
  electron-to-chromium@^1.4.284:
    version "1.4.399"
    resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.399.tgz"
    integrity sha512-+V1aNvVgoWNWYIbMOiQ1n5fRIaY4SlQ/uRlrsCjLrUwr/3OvQgiX2f5vdav4oArVT9TnttJKcPCqjwPNyZqw/A==
  
  es-abstract@^1.19.0, es-abstract@^1.20.4:
    version "1.21.2"
    resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.21.2.tgz"
    integrity sha512-y/B5POM2iBnIxCiernH1G7rC9qQoM77lLIMQLuob0zhp8C56Po81+2Nj0WFKnd0pNReDTnkYryc+zhOzpEIROg==
    dependencies:
      array-buffer-byte-length "^1.0.0"
      available-typed-arrays "^1.0.5"
      call-bind "^1.0.2"
      es-set-tostringtag "^2.0.1"
      es-to-primitive "^1.2.1"
      function.prototype.name "^1.1.5"
      get-intrinsic "^1.2.0"
      get-symbol-description "^1.0.0"
      globalthis "^1.0.3"
      gopd "^1.0.1"
      has "^1.0.3"
      has-property-descriptors "^1.0.0"
      has-proto "^1.0.1"
      has-symbols "^1.0.3"
      internal-slot "^1.0.5"
      is-array-buffer "^3.0.2"
      is-callable "^1.2.7"
      is-negative-zero "^2.0.2"
      is-regex "^1.1.4"
      is-shared-array-buffer "^1.0.2"
      is-string "^1.0.7"
      is-typed-array "^1.1.10"
      is-weakref "^1.0.2"
      object-inspect "^1.12.3"
      object-keys "^1.1.1"
      object.assign "^4.1.4"
      regexp.prototype.flags "^1.4.3"
      safe-regex-test "^1.0.0"
      string.prototype.trim "^1.2.7"
      string.prototype.trimend "^1.0.6"
      string.prototype.trimstart "^1.0.6"
      typed-array-length "^1.0.4"
      unbox-primitive "^1.0.2"
      which-typed-array "^1.1.9"
  
  es-define-property@^1.0.0, es-define-property@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
    integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==
  
  es-errors@^1.3.0:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
    integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==
  
  es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz#1c4f2c4837327597ce69d2ca190a7fdd172338c1"
    integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
    dependencies:
      es-errors "^1.3.0"
  
  es-set-tostringtag@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.1.tgz"
    integrity sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==
    dependencies:
      get-intrinsic "^1.1.3"
      has "^1.0.3"
      has-tostringtag "^1.0.0"
  
  es-shim-unscopables@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.0.0.tgz"
    integrity sha512-Jm6GPcCdC30eMLbZ2x8z2WuRwAws3zTBBKuusffYVUrNj/GVSUAZ+xKMaUpfNDR5IbyNA5LJbaecoUVbmUcB1w==
    dependencies:
      has "^1.0.3"
  
  es-to-primitive@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
    integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
    dependencies:
      is-callable "^1.1.4"
      is-date-object "^1.0.1"
      is-symbol "^1.0.2"
  
  escalade@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
    integrity sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==
  
  escape-string-regexp@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
    integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==
  
  escape-string-regexp@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
    integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==
  
  eslint-config-airbnb-base@^15.0.0:
    version "15.0.0"
    resolved "https://registry.npmjs.org/eslint-config-airbnb-base/-/eslint-config-airbnb-base-15.0.0.tgz"
    integrity sha512-xaX3z4ZZIcFLvh2oUNvcX5oEofXda7giYmuplVxoOg5A7EXJMrUyqRgR+mhDhPK8LZ4PttFOBvCYDbX3sUoUig==
    dependencies:
      confusing-browser-globals "^1.0.10"
      object.assign "^4.1.2"
      object.entries "^1.1.5"
      semver "^6.3.0"
  
  eslint-config-ali@^14.0.2:
    version "14.0.2"
    resolved "https://registry.npmjs.org/eslint-config-ali/-/eslint-config-ali-14.0.2.tgz"
    integrity sha512-SyNnCNNT519UI5HRAu2saQf33krtXuLjv4wM6pv3aVQ2bY3AmBkgkSZ7Za/bkxmZoxCHxHRGnm0JVDYP1wnYFA==
  
  eslint-config-prettier@^8.7.0:
    version "8.8.0"
    resolved "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-8.8.0.tgz"
    integrity sha512-wLbQiFre3tdGgpDv67NQKnJuTlcUVYHas3k+DZCc2U2BadthoEY4B7hLPvAxaqdyOGCzuLfii2fqGph10va7oA==
  
  eslint-import-resolver-node@^0.3.7:
    version "0.3.7"
    resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.7.tgz"
    integrity sha512-gozW2blMLJCeFpBwugLTGyvVjNoeo1knonXAcatC6bjPBZitotxdWf7Gimr25N4c0AAOo4eOUfaG82IJPDpqCA==
    dependencies:
      debug "^3.2.7"
      is-core-module "^2.11.0"
      resolve "^1.22.1"
  
  eslint-module-utils@^2.7.4:
    version "2.8.0"
    resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.8.0.tgz"
    integrity sha512-aWajIYfsqCKRDgUfjEXNN/JlrzauMuSEy5sbd7WXbtW3EH6A6MpwEh42c7qD+MqQo9QMJ6fWLAeIJynx0g6OAw==
    dependencies:
      debug "^3.2.7"
  
  eslint-plugin-import@^2.27.5:
    version "2.27.5"
    resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.27.5.tgz"
    integrity sha512-LmEt3GVofgiGuiE+ORpnvP+kAm3h6MLZJ4Q5HCyHADofsb4VzXFsRiWj3c0OFiV+3DWFh0qg3v9gcPlfc3zRow==
    dependencies:
      array-includes "^3.1.6"
      array.prototype.flat "^1.3.1"
      array.prototype.flatmap "^1.3.1"
      debug "^3.2.7"
      doctrine "^2.1.0"
      eslint-import-resolver-node "^0.3.7"
      eslint-module-utils "^2.7.4"
      has "^1.0.3"
      is-core-module "^2.11.0"
      is-glob "^4.0.3"
      minimatch "^3.1.2"
      object.values "^1.1.6"
      resolve "^1.22.1"
      semver "^6.3.0"
      tsconfig-paths "^3.14.1"
  
  eslint-plugin-prettier@^4.2.1:
    version "4.2.1"
    resolved "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz"
    integrity sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ==
    dependencies:
      prettier-linter-helpers "^1.0.0"
  
  eslint-plugin-vue@^9.9.0:
    version "9.13.0"
    resolved "https://registry.npmjs.org/eslint-plugin-vue/-/eslint-plugin-vue-9.13.0.tgz"
    integrity sha512-aBz9A8WB4wmpnVv0pYUt86cmH9EkcwWzgEwecBxMoRNhQjTL5i4sqadnwShv/hOdr8Hbl8XANGV7dtX9UQIAyA==
    dependencies:
      "@eslint-community/eslint-utils" "^4.3.0"
      natural-compare "^1.4.0"
      nth-check "^2.0.1"
      postcss-selector-parser "^6.0.9"
      semver "^7.3.5"
      vue-eslint-parser "^9.3.0"
      xml-name-validator "^4.0.0"
  
  eslint-scope@5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
    integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
    dependencies:
      esrecurse "^4.3.0"
      estraverse "^4.1.1"
  
  eslint-scope@^7.1.1, eslint-scope@^7.2.0:
    version "7.2.0"
    resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.0.tgz"
    integrity sha512-DYj5deGlHBfMt15J7rdtyKNq/Nqlv5KfU4iodrQ019XESsRnwXH9KAE0y3cwtUHDo2ob7CypAnCqefh6vioWRw==
    dependencies:
      esrecurse "^4.3.0"
      estraverse "^5.2.0"
  
  eslint-visitor-keys@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
    integrity sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==
  
  eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1:
    version "3.4.1"
    resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz"
    integrity sha512-pZnmmLwYzf+kWaM/Qgrvpen51upAktaaiI01nsJD/Yr3lMOdNtq0cxkrrg16w64VtisN6okbs7Q8AfGqj4c9fA==
  
  eslint@^8.36.0:
    version "8.40.0"
    resolved "https://registry.npmjs.org/eslint/-/eslint-8.40.0.tgz"
    integrity sha512-bvR+TsP9EHL3TqNtj9sCNJVAFK3fBN8Q7g5waghxyRsPLIMwL73XSKnZFK0hk/O2ANC+iAoq6PWMQ+IfBAJIiQ==
    dependencies:
      "@eslint-community/eslint-utils" "^4.2.0"
      "@eslint-community/regexpp" "^4.4.0"
      "@eslint/eslintrc" "^2.0.3"
      "@eslint/js" "8.40.0"
      "@humanwhocodes/config-array" "^0.11.8"
      "@humanwhocodes/module-importer" "^1.0.1"
      "@nodelib/fs.walk" "^1.2.8"
      ajv "^6.10.0"
      chalk "^4.0.0"
      cross-spawn "^7.0.2"
      debug "^4.3.2"
      doctrine "^3.0.0"
      escape-string-regexp "^4.0.0"
      eslint-scope "^7.2.0"
      eslint-visitor-keys "^3.4.1"
      espree "^9.5.2"
      esquery "^1.4.2"
      esutils "^2.0.2"
      fast-deep-equal "^3.1.3"
      file-entry-cache "^6.0.1"
      find-up "^5.0.0"
      glob-parent "^6.0.2"
      globals "^13.19.0"
      grapheme-splitter "^1.0.4"
      ignore "^5.2.0"
      import-fresh "^3.0.0"
      imurmurhash "^0.1.4"
      is-glob "^4.0.0"
      is-path-inside "^3.0.3"
      js-sdsl "^4.1.4"
      js-yaml "^4.1.0"
      json-stable-stringify-without-jsonify "^1.0.1"
      levn "^0.4.1"
      lodash.merge "^4.6.2"
      minimatch "^3.1.2"
      natural-compare "^1.4.0"
      optionator "^0.9.1"
      strip-ansi "^6.0.1"
      strip-json-comments "^3.1.0"
      text-table "^0.2.0"
  
  espree@^9.3.1, espree@^9.5.2:
    version "9.5.2"
    resolved "https://registry.npmjs.org/espree/-/espree-9.5.2.tgz"
    integrity sha512-7OASN1Wma5fum5SrNhFMAMJxOUAbhyfQ8dQ//PJaJbNw0URTPWqIghHWt1MmAANKhHZIYOHruW4Kw4ruUWOdGw==
    dependencies:
      acorn "^8.8.0"
      acorn-jsx "^5.3.2"
      eslint-visitor-keys "^3.4.1"
  
  esquery@^1.4.0, esquery@^1.4.2:
    version "1.5.0"
    resolved "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz"
    integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
    dependencies:
      estraverse "^5.1.0"
  
  esrecurse@^4.3.0:
    version "4.3.0"
    resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
    integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
    dependencies:
      estraverse "^5.2.0"
  
  estraverse@^4.1.1:
    version "4.3.0"
    resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
    integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==
  
  estraverse@^5.1.0, estraverse@^5.2.0:
    version "5.3.0"
    resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
    integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==
  
  esutils@^2.0.2:
    version "2.0.3"
    resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
    integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==
  
  fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
    version "3.1.3"
    resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
    integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==
  
  fast-diff@^1.1.2:
    version "1.2.0"
    resolved "https://registry.npmjs.org/fast-diff/-/fast-diff-1.2.0.tgz"
    integrity sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w==
  
  fast-json-stable-stringify@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
    integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==
  
  fast-levenshtein@^2.0.6:
    version "2.0.6"
    resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
    integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==
  
  fastq@^1.6.0:
    version "1.15.0"
    resolved "https://registry.npmjs.org/fastq/-/fastq-1.15.0.tgz"
    integrity sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==
    dependencies:
      reusify "^1.0.4"
  
  file-entry-cache@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
    integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
    dependencies:
      flat-cache "^3.0.4"
  
  find-up@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
    integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
    dependencies:
      locate-path "^6.0.0"
      path-exists "^4.0.0"
  
  flat-cache@^3.0.4:
    version "3.0.4"
    resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-3.0.4.tgz"
    integrity sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==
    dependencies:
      flatted "^3.1.0"
      rimraf "^3.0.2"
  
  flatted@^3.1.0:
    version "3.2.7"
    resolved "https://registry.npmjs.org/flatted/-/flatted-3.2.7.tgz"
    integrity sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ==
  
  follow-redirects@1.5.10:
    version "1.5.10"
    resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.10.tgz"
    integrity sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==
    dependencies:
      debug "=3.1.0"
  
  follow-redirects@^1.14.8:
    version "1.15.1"
    resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.1.tgz"
    integrity sha512-yLAMQs+k0b2m7cVxpS1VKJVvoz7SS9Td1zss3XRwXj+ZDH00RJgnuLx7E44wx02kQLrdM3aOOy+FpzS7+8OizA==
  
  for-each@^0.3.3:
    version "0.3.3"
    resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
    integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
    dependencies:
      is-callable "^1.1.3"
  
  fs.realpath@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
    integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==
  
  function-bind@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
    integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==
  
  function-bind@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
    integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==
  
  function.prototype.name@^1.1.5:
    version "1.1.5"
    resolved "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.5.tgz"
    integrity sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.3"
      es-abstract "^1.19.0"
      functions-have-names "^1.2.2"
  
  functions-have-names@^1.2.2, functions-have-names@^1.2.3:
    version "1.2.3"
    resolved "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
    integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==
  
  gensync@^1.0.0-beta.2:
    version "1.0.0-beta.2"
    resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
    integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==
  
  geojson-equality@0.1.6:
    version "0.1.6"
    resolved "https://registry.npmmirror.com/geojson-equality/-/geojson-equality-0.1.6.tgz#a171374ef043e5d4797995840bae4648e0752d72"
    integrity sha512-TqG8YbqizP3EfwP5Uw4aLu6pKkg6JQK9uq/XZ1lXQntvTHD1BBKJWhNpJ2M0ax6TuWMP3oyx6Oq7FCIfznrgpQ==
    dependencies:
      deep-equal "^1.0.0"
  
  geojson-rbush@3.x:
    version "3.2.0"
    resolved "https://registry.npmmirror.com/geojson-rbush/-/geojson-rbush-3.2.0.tgz#8b543cf0d56f99b78faf1da52bb66acad6dfc290"
    integrity sha512-oVltQTXolxvsz1sZnutlSuLDEcQAKYC/uXt9zDzJJ6bu0W+baTI8LZBaTup5afzibEH4N3jlq2p+a152wlBJ7w==
    dependencies:
      "@turf/bbox" "*"
      "@turf/helpers" "6.x"
      "@turf/meta" "6.x"
      "@types/geojson" "7946.0.8"
      rbush "^3.0.1"
  
  geojson-vt@^3.2.1:
    version "3.2.1"
    resolved "https://registry.npmmirror.com/geojson-vt/-/geojson-vt-3.2.1.tgz#f8adb614d2c1d3f6ee7c4265cad4bbf3ad60c8b7"
    integrity sha512-EvGQQi/zPrDA6zr6BnJD/YhwAkBP8nnJ9emh3EnHQKVMfg/MRVtPbMYdgVy/IaEmn4UfagD2a6fafPDL5hbtwg==
  
  get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.0:
    version "1.2.1"
    resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.1.tgz"
    integrity sha512-2DcsyfABl+gVHEfCOaTrWgyt+tb6MSEGmKq+kI5HwLbIYgjgmMcV8KQ41uaKz1xxUcn9tJtgFbQUEVcEbd0FYw==
    dependencies:
      function-bind "^1.1.1"
      has "^1.0.3"
      has-proto "^1.0.1"
      has-symbols "^1.0.3"
  
  get-intrinsic@^1.2.4, get-intrinsic@^1.3.0:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz#743f0e3b6964a93a5491ed1bffaae054d7f98d01"
    integrity sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==
    dependencies:
      call-bind-apply-helpers "^1.0.2"
      es-define-property "^1.0.1"
      es-errors "^1.3.0"
      es-object-atoms "^1.1.1"
      function-bind "^1.1.2"
      get-proto "^1.0.1"
      gopd "^1.2.0"
      has-symbols "^1.1.0"
      hasown "^2.0.2"
      math-intrinsics "^1.1.0"
  
  get-proto@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/get-proto/-/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
    integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
    dependencies:
      dunder-proto "^1.0.1"
      es-object-atoms "^1.0.0"
  
  get-stream@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
    integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==
  
  get-symbol-description@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
    integrity sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.1.1"
  
  gl-matrix@^3.2.1:
    version "3.4.4"
    resolved "https://registry.npmmirror.com/gl-matrix/-/gl-matrix-3.4.4.tgz#7789ee4982f62c7a7af447ee488f3bd6b0c77003"
    integrity sha512-latSnyDNt/8zYUB6VIJ6PCh2jBjJX6gnDsoCZ7LyW7GkqrD51EWwa9qCoGixj8YqBtETQK/xY7OmpTF8xz1DdQ==
  
  glob-parent@^6.0.2:
    version "6.0.2"
    resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
    integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
    dependencies:
      is-glob "^4.0.3"
  
  glob@^7.1.3:
    version "7.2.3"
    resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
    integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^3.1.1"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  globals@^11.1.0:
    version "11.12.0"
    resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
    integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==
  
  globals@^13.19.0:
    version "13.20.0"
    resolved "https://registry.npmjs.org/globals/-/globals-13.20.0.tgz"
    integrity sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ==
    dependencies:
      type-fest "^0.20.2"
  
  globalthis@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/globalthis/-/globalthis-1.0.3.tgz"
    integrity sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==
    dependencies:
      define-properties "^1.1.3"
  
  gopd@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz"
    integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
    dependencies:
      get-intrinsic "^1.1.3"
  
  gopd@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/gopd/-/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
    integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==
  
  grapheme-splitter@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz"
    integrity sha512-bzh50DW9kTPM00T8y4o8vQg89Di9oLJVLW/KaOGIXJWP/iqCN6WKYkbNOF04vFLJhwcpYUh9ydh/+5vpOqV4YQ==
  
  grid-index@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/grid-index/-/grid-index-1.1.0.tgz#97f8221edec1026c8377b86446a7c71e79522ea7"
    integrity sha512-HZRwumpOGUrHyxO5bqKZL0B0GlUpwtCAzZ42sgxUPniu33R1LSFH5yrIcBCHjkctCAh3mtWKcKd9J4vDDdeVHA==
  
  gt-mis-app-components@^1.2.8:
    version "1.2.7"
    resolved "https://registry.npmmirror.com/gt-mis-app-components/-/gt-mis-app-components-1.2.7.tgz#4baec24d06df31d509b85e6a2cd8d3fa4fcf105e"
    integrity sha512-MHGuYLLGRC7y/x7BIT5EzABrkUaxOWwfTBgfs8T9xPJTeFpC71wYtkN70al0QLR1q/9czsTVTkDlHm7h8bAcSQ==
  
  has-bigints@^1.0.1, has-bigints@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.2.tgz"
    integrity sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==
  
  has-flag@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
    integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==
  
  has-flag@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
    integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==
  
  has-property-descriptors@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz"
    integrity sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==
    dependencies:
      get-intrinsic "^1.1.1"
  
  has-property-descriptors@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
    integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
    dependencies:
      es-define-property "^1.0.0"
  
  has-proto@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.0.1.tgz"
    integrity sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==
  
  has-symbols@^1.0.2, has-symbols@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
    integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==
  
  has-symbols@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
    integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==
  
  has-tostringtag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
    integrity sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==
    dependencies:
      has-symbols "^1.0.2"
  
  has-tostringtag@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
    integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
    dependencies:
      has-symbols "^1.0.3"
  
  has@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
    integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
    dependencies:
      function-bind "^1.1.1"
  
  hasown@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
    integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
    dependencies:
      function-bind "^1.1.2"
  
  ieee754@^1.1.12:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
    integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==
  
  ignore@^5.2.0:
    version "5.2.4"
    resolved "https://registry.npmjs.org/ignore/-/ignore-5.2.4.tgz"
    integrity sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==
  
  import-fresh@^3.0.0, import-fresh@^3.2.1:
    version "3.3.0"
    resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
    integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
    dependencies:
      parent-module "^1.0.0"
      resolve-from "^4.0.0"
  
  imurmurhash@^0.1.4:
    version "0.1.4"
    resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
    integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==
  
  inflight@^1.0.4:
    version "1.0.6"
    resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
    integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
    dependencies:
      once "^1.3.0"
      wrappy "1"
  
  inherits@2:
    version "2.0.4"
    resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
    integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==
  
  internal-slot@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.5.tgz"
    integrity sha512-Y+R5hJrzs52QCG2laLn4udYVnxsfny9CpOhNhUvk/SSSVyF6T27FzRbF0sroPidSu3X8oEAkOn2K804mjpt6UQ==
    dependencies:
      get-intrinsic "^1.2.0"
      has "^1.0.3"
      side-channel "^1.0.4"
  
  is-arguments@^1.1.1:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/is-arguments/-/is-arguments-1.2.0.tgz#ad58c6aecf563b78ef2bf04df540da8f5d7d8e1b"
    integrity sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==
    dependencies:
      call-bound "^1.0.2"
      has-tostringtag "^1.0.2"
  
  is-array-buffer@^3.0.1, is-array-buffer@^3.0.2:
    version "3.0.2"
    resolved "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.2.tgz"
    integrity sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.2.0"
      is-typed-array "^1.1.10"
  
  is-bigint@^1.0.1:
    version "1.0.4"
    resolved "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
    integrity sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==
    dependencies:
      has-bigints "^1.0.1"
  
  is-boolean-object@^1.1.0:
    version "1.1.2"
    resolved "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
    integrity sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
    version "1.2.7"
    resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
    integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==
  
  is-core-module@^2.11.0:
    version "2.12.1"
    resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.12.1.tgz"
    integrity sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg==
    dependencies:
      has "^1.0.3"
  
  is-date-object@^1.0.1:
    version "1.0.5"
    resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
    integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-date-object@^1.0.5:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/is-date-object/-/is-date-object-1.1.0.tgz#ad85541996fc7aa8b2729701d27b7319f95d82f7"
    integrity sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==
    dependencies:
      call-bound "^1.0.2"
      has-tostringtag "^1.0.2"
  
  is-extglob@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
    integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==
  
  is-glob@^4.0.0, is-glob@^4.0.3:
    version "4.0.3"
    resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
    integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
    dependencies:
      is-extglob "^2.1.1"
  
  is-negative-zero@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
    integrity sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==
  
  is-number-object@^1.0.4:
    version "1.0.7"
    resolved "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.7.tgz"
    integrity sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-path-inside@^3.0.3:
    version "3.0.3"
    resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
    integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==
  
  is-regex@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
    integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-shared-array-buffer@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz"
    integrity sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==
    dependencies:
      call-bind "^1.0.2"
  
  is-string@^1.0.5, is-string@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
    integrity sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-symbol@^1.0.2, is-symbol@^1.0.3:
    version "1.0.4"
    resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
    integrity sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==
    dependencies:
      has-symbols "^1.0.2"
  
  is-typed-array@^1.1.10, is-typed-array@^1.1.9:
    version "1.1.10"
    resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.10.tgz"
    integrity sha512-PJqgEHiWZvMpaFZ3uTc8kHPM4+4ADTlDniuQL7cU/UDA0Ql7F70yGfHph3cLNe+c9toaigv+DFzTJKhc2CtO6A==
    dependencies:
      available-typed-arrays "^1.0.5"
      call-bind "^1.0.2"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-tostringtag "^1.0.0"
  
  is-weakref@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz"
    integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
    dependencies:
      call-bind "^1.0.2"
  
  isexe@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
    integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==
  
  js-sdsl@^4.1.4:
    version "4.4.0"
    resolved "https://registry.npmjs.org/js-sdsl/-/js-sdsl-4.4.0.tgz"
    integrity sha512-FfVSdx6pJ41Oa+CF7RDaFmTnCaFhua+SNYQX74riGOpl96x+2jQCqEfQ2bnXu/5DPCqlRuiqyvTJM0Qjz26IVg==
  
  js-tokens@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
    integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==
  
  js-yaml@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
    integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
    dependencies:
      argparse "^2.0.1"
  
  jsesc@^2.5.1:
    version "2.5.2"
    resolved "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
    integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==
  
  json-schema-traverse@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
    integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==
  
  json-stable-stringify-without-jsonify@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
    integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==
  
  json5@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
    integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
    dependencies:
      minimist "^1.2.0"
  
  json5@^2.2.2:
    version "2.2.3"
    resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
    integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==
  
  kdbush@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/kdbush/-/kdbush-3.0.0.tgz#f8484794d47004cc2d85ed3a79353dbe0abc2bf0"
    integrity sha512-hRkd6/XW4HTsA9vjVpY9tuXJYLSlelnkTmVFu4M9/7MIYQtFcHpbugAU7UbOfjOiVSVYl2fqgBuJ32JUmRo5Ew==
  
  levn@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
    integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
    dependencies:
      prelude-ls "^1.2.1"
      type-check "~0.4.0"
  
  locate-path@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
    integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
    dependencies:
      p-locate "^5.0.0"
  
  lodash.merge@^4.6.2:
    version "4.6.2"
    resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
    integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==
  
  lodash@^4.17.21, lodash@~4.17.21:
    version "4.17.21"
    resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
    integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==
  
  lru-cache@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
    integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
    dependencies:
      yallist "^3.0.2"
  
  lru-cache@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
    integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
    dependencies:
      yallist "^4.0.0"
  
  mapbox-gl@1.13.1:
    version "1.13.1"
    resolved "https://registry.npmmirror.com/mapbox-gl/-/mapbox-gl-1.13.1.tgz#322efe75ab4c764fc4c776da1506aad58d5a5b9d"
    integrity sha512-GSyubcoSF5MyaP8z+DasLu5v7KmDK2pp4S5+VQ5WdVQUOaAqQY4jwl4JpcdNho3uWm2bIKs7x1l7q3ynGmW60g==
    dependencies:
      "@mapbox/geojson-rewind" "^0.5.0"
      "@mapbox/geojson-types" "^1.0.2"
      "@mapbox/jsonlint-lines-primitives" "^2.0.2"
      "@mapbox/mapbox-gl-supported" "^1.5.0"
      "@mapbox/point-geometry" "^0.1.0"
      "@mapbox/tiny-sdf" "^1.1.1"
      "@mapbox/unitbezier" "^0.0.0"
      "@mapbox/vector-tile" "^1.3.1"
      "@mapbox/whoots-js" "^3.1.0"
      csscolorparser "~1.0.3"
      earcut "^2.2.2"
      geojson-vt "^3.2.1"
      gl-matrix "^3.2.1"
      grid-index "^1.1.0"
      minimist "^1.2.5"
      murmurhash-js "^1.0.0"
      pbf "^3.2.1"
      potpack "^1.0.1"
      quickselect "^2.0.0"
      rw "^1.3.3"
      supercluster "^7.1.0"
      tinyqueue "^2.0.3"
      vt-pbf "^3.1.1"
  
  math-intrinsics@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
    integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==
  
  minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
    version "3.1.2"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
    integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
    dependencies:
      brace-expansion "^1.1.7"
  
  minimist@^1.2.0, minimist@^1.2.5, minimist@^1.2.6:
    version "1.2.8"
    resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
    integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==
  
  moment@^2.29.1:
    version "2.29.4"
    resolved "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz"
    integrity sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==
  
  ms@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
    integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==
  
  ms@2.1.2, ms@^2.1.1:
    version "2.1.2"
    resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
    integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==
  
  murmurhash-js@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/murmurhash-js/-/murmurhash-js-1.0.0.tgz#b06278e21fc6c37fa5313732b0412bcb6ae15f51"
    integrity sha512-TvmkNhkv8yct0SVBSy+o8wYzXjE4Zz3PCesbfs8HiCXXdcTuocApFv11UWlNFWKYsP2okqrhb7JNlSm9InBhIw==
  
  natural-compare@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
    integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==
  
  node-releases@^2.0.8:
    version "2.0.10"
    resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.10.tgz"
    integrity sha512-5GFldHPXVG/YZmFzJvKK2zDSzPKhEp0+ZR5SVaoSag9fsL5YgHbUHDfnG5494ISANDcK4KwPXAx2xqVEydmd7w==
  
  nth-check@^2.0.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz"
    integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
    dependencies:
      boolbase "^1.0.0"
  
  object-assign@*:
    version "4.1.1"
    resolved "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
    integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==
  
  object-inspect@^1.12.3, object-inspect@^1.9.0:
    version "1.12.3"
    resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.3.tgz"
    integrity sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==
  
  object-is@^1.1.5:
    version "1.1.6"
    resolved "https://registry.npmmirror.com/object-is/-/object-is-1.1.6.tgz#1a6a53aed2dd8f7e6775ff870bea58545956ab07"
    integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
  
  object-keys@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
    integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==
  
  object.assign@^4.1.2, object.assign@^4.1.4:
    version "4.1.4"
    resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.4.tgz"
    integrity sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      has-symbols "^1.0.3"
      object-keys "^1.1.1"
  
  object.entries@^1.1.5:
    version "1.1.6"
    resolved "https://registry.npmjs.org/object.entries/-/object.entries-1.1.6.tgz"
    integrity sha512-leTPzo4Zvg3pmbQ3rDK69Rl8GQvIqMWubrkxONG9/ojtFE2rD9fjMKfSI5BxW3osRH1m6VdzmqK8oAY9aT4x5w==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  object.values@^1.1.6:
    version "1.1.6"
    resolved "https://registry.npmjs.org/object.values/-/object.values-1.1.6.tgz"
    integrity sha512-FVVTkD1vENCsAcwNs9k6jea2uHC/X0+JcjG8YA60FN5CMaJmG95wT9jek/xX9nornqGRrBkKtzuAu2wuHpKqvw==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  omit-deep-lodash@^1.1.7:
    version "1.1.7"
    resolved "https://registry.npmjs.org/omit-deep-lodash/-/omit-deep-lodash-1.1.7.tgz"
    integrity sha512-9m9gleSMoxq3YO8aCq5pGUrqG9rKF0w/P70JHQ1ymjUQA/3+fVa2Stju9XORJKLmyLYEO3zzX40MJYaYl5Og4w==
    dependencies:
      lodash "~4.17.21"
  
  once@^1.3.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
    integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
    dependencies:
      wrappy "1"
  
  optionator@^0.9.1:
    version "0.9.1"
    resolved "https://registry.npmjs.org/optionator/-/optionator-0.9.1.tgz"
    integrity sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw==
    dependencies:
      deep-is "^0.1.3"
      fast-levenshtein "^2.0.6"
      levn "^0.4.1"
      prelude-ls "^1.2.1"
      type-check "^0.4.0"
      word-wrap "^1.2.3"
  
  p-limit@^3.0.2:
    version "3.1.0"
    resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
    integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
    dependencies:
      yocto-queue "^0.1.0"
  
  p-locate@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
    integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
    dependencies:
      p-limit "^3.0.2"
  
  parent-module@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
    integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
    dependencies:
      callsites "^3.0.0"
  
  path-exists@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
    integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==
  
  path-is-absolute@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
    integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==
  
  path-key@^3.1.0:
    version "3.1.1"
    resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
    integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==
  
  path-parse@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
    integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==
  
  pbf@^3.2.1:
    version "3.3.0"
    resolved "https://registry.npmmirror.com/pbf/-/pbf-3.3.0.tgz#1790f3d99118333cc7f498de816028a346ef367f"
    integrity sha512-XDF38WCH3z5OV/OVa8GKUNtLAyneuzbCisx7QUCF8Q6Nutx0WnJrQe5O+kOtBlLfRNUws98Y58Lblp+NJG5T4Q==
    dependencies:
      ieee754 "^1.1.12"
      resolve-protobuf-schema "^2.1.0"
  
  picocolors@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz"
    integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==
  
  point-in-polygon@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/point-in-polygon/-/point-in-polygon-1.1.0.tgz#b0af2616c01bdee341cbf2894df643387ca03357"
    integrity sha512-3ojrFwjnnw8Q9242TzgXuTD+eKiutbzyslcq1ydfu82Db2y+Ogbmyrkpv0Hgj31qwT3lbS9+QAAO/pIQM35XRw==
  
  polygon-clipping@^0.15.3:
    version "0.15.7"
    resolved "https://registry.npmmirror.com/polygon-clipping/-/polygon-clipping-0.15.7.tgz#3823ca1e372566f350795ce9dd9a7b19e97bdaad"
    integrity sha512-nhfdr83ECBg6xtqOAJab1tbksbBAOMUltN60bU+llHVOL0e5Onm1WpAXXWXVB39L8AJFssoIhEVuy/S90MmotA==
    dependencies:
      robust-predicates "^3.0.2"
      splaytree "^3.1.0"
  
  postcss-selector-parser@^6.0.9:
    version "6.0.13"
    resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz"
    integrity sha512-EaV1Gl4mUEV4ddhDnv/xtj7sxwrwxdetHdWUGnT4VJQf+4d05v6lHYZr8N573k5Z0BViss7BDhfWtKS3+sfAqQ==
    dependencies:
      cssesc "^3.0.0"
      util-deprecate "^1.0.2"
  
  potpack@^1.0.1:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/potpack/-/potpack-1.0.2.tgz#23b99e64eb74f5741ffe7656b5b5c4ddce8dfc14"
    integrity sha512-choctRBIV9EMT9WGAZHn3V7t0Z2pMQyl0EZE6pFc/6ml3ssw7Dlf/oAOvFwjm1HVsqfQN8GfeFyJ+d8tRzqueQ==
  
  prelude-ls@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
    integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==
  
  prettier-linter-helpers@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
    integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
    dependencies:
      fast-diff "^1.1.2"
  
  prettier@^2.8.4:
    version "2.8.8"
    resolved "https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz"
    integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==
  
  protocol-buffers-schema@^3.3.1:
    version "3.6.0"
    resolved "https://registry.npmmirror.com/protocol-buffers-schema/-/protocol-buffers-schema-3.6.0.tgz#77bc75a48b2ff142c1ad5b5b90c94cd0fa2efd03"
    integrity sha512-TdDRD+/QNdrCGCE7v8340QyuXd4kIWIgapsE2+n/SaGiSSbomYl4TjHlvIoCWRpE7wFt02EpB35VVA2ImcBVqw==
  
  punycode@^2.1.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz"
    integrity sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==
  
  queue-microtask@^1.2.2:
    version "1.2.3"
    resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
    integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==
  
  quickselect@^1.0.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/quickselect/-/quickselect-1.1.1.tgz#852e412ce418f237ad5b660d70cffac647ae94c2"
    integrity sha512-qN0Gqdw4c4KGPsBOQafj6yj/PA6c/L63f6CaZ/DCF/xF4Esu3jVmKLUDYxghFx8Kb/O7y9tI7x2RjTSXwdK1iQ==
  
  quickselect@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/quickselect/-/quickselect-2.0.0.tgz#f19680a486a5eefb581303e023e98faaf25dd018"
    integrity sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw==
  
  quickselect@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/quickselect/-/quickselect-3.0.0.tgz#a37fc953867d56f095a20ac71c6d27063d2de603"
    integrity sha512-XdjUArbK4Bm5fLLvlm5KpTFOiOThgfWWI4axAZDWg4E/0mKdZyI9tNEfds27qCi1ze/vwTR16kvmmGhRra3c2g==
  
  rbush@2.x, rbush@^2.0.1:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/rbush/-/rbush-2.0.2.tgz#bb6005c2731b7ba1d5a9a035772927d16a614605"
    integrity sha512-XBOuALcTm+O/H8G90b6pzu6nX6v2zCKiFG4BJho8a+bY6AER6t8uQUZdi5bomQc0AprCWhEGa7ncAbbRap0bRA==
    dependencies:
      quickselect "^1.0.1"
  
  rbush@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/rbush/-/rbush-3.0.1.tgz#5fafa8a79b3b9afdfe5008403a720cc1de882ecf"
    integrity sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w==
    dependencies:
      quickselect "^2.0.0"
  
  rbush@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmmirror.com/rbush/-/rbush-4.0.1.tgz#1f55afa64a978f71bf9e9a99bc14ff84f3cb0d6d"
    integrity sha512-IP0UpfeWQujYC8Jg162rMNc01Rf0gWMMAb2Uxus/Q0qOFw4lCcq6ZnQEZwUoJqWyUGJ9th7JjwI4yIWo+uvoAQ==
    dependencies:
      quickselect "^3.0.0"
  
  regexp.prototype.flags@^1.4.3:
    version "1.5.0"
    resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz"
    integrity sha512-0SutC3pNudRKgquxGoRGIz946MZVHqbNfPjBdxeOhBrdgDKlRoXmYLQN9xRbrR09ZXWeGAdPuif7egofn6v5LA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.2.0"
      functions-have-names "^1.2.3"
  
  regexp.prototype.flags@^1.5.1:
    version "1.5.4"
    resolved "https://registry.npmmirror.com/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz#1ad6c62d44a259007e55b3970e00f746efbcaa19"
    integrity sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==
    dependencies:
      call-bind "^1.0.8"
      define-properties "^1.2.1"
      es-errors "^1.3.0"
      get-proto "^1.0.1"
      gopd "^1.2.0"
      set-function-name "^2.0.2"
  
  resolve-from@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
    integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==
  
  resolve-protobuf-schema@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/resolve-protobuf-schema/-/resolve-protobuf-schema-2.1.0.tgz#9ca9a9e69cf192bbdaf1006ec1973948aa4a3758"
    integrity sha512-kI5ffTiZWmJaS/huM8wZfEMer1eRd7oJQhDuxeCLe3t7N7mX3z94CN0xPxBQxFYQTSNz9T0i+v6inKqSdK8xrQ==
    dependencies:
      protocol-buffers-schema "^3.3.1"
  
  resolve@^1.22.1:
    version "1.22.2"
    resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.2.tgz"
    integrity sha512-Sb+mjNHOULsBv818T40qSPeRiuWLyaGMa5ewydRLFimneixmVy2zdivRl+AF6jaYPC8ERxGDmFSiqui6SfPd+g==
    dependencies:
      is-core-module "^2.11.0"
      path-parse "^1.0.7"
      supports-preserve-symlinks-flag "^1.0.0"
  
  reusify@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
    integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==
  
  rimraf@^3.0.2:
    version "3.0.2"
    resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
    integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
    dependencies:
      glob "^7.1.3"
  
  robust-predicates@^3.0.2:
    version "3.0.2"
    resolved "https://registry.npmmirror.com/robust-predicates/-/robust-predicates-3.0.2.tgz#d5b28528c4824d20fc48df1928d41d9efa1ad771"
    integrity sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==
  
  run-parallel@^1.1.9:
    version "1.2.0"
    resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
    integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
    dependencies:
      queue-microtask "^1.2.2"
  
  rw@^1.3.3:
    version "1.3.3"
    resolved "https://registry.npmmirror.com/rw/-/rw-1.3.3.tgz#3f862dfa91ab766b14885ef4d01124bfda074fb4"
    integrity sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==
  
  safe-regex-test@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.0.0.tgz"
    integrity sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.1.3"
      is-regex "^1.1.4"
  
  semver@^6.3.0:
    version "6.3.0"
    resolved "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
    integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==
  
  semver@^7.3.5, semver@^7.3.6:
    version "7.5.1"
    resolved "https://registry.npmjs.org/semver/-/semver-7.5.1.tgz"
    integrity sha512-Wvss5ivl8TMRZXXESstBA4uR5iXgEN/VC5/sOcuXdVLzcdkz4HWetIoRfG5gb5X+ij/G9rw9YoGn3QoQ8OCSpw==
    dependencies:
      lru-cache "^6.0.0"
  
  set-function-length@^1.2.2:
    version "1.2.2"
    resolved "https://registry.npmmirror.com/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
    integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
    dependencies:
      define-data-property "^1.1.4"
      es-errors "^1.3.0"
      function-bind "^1.1.2"
      get-intrinsic "^1.2.4"
      gopd "^1.0.1"
      has-property-descriptors "^1.0.2"
  
  set-function-name@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/set-function-name/-/set-function-name-2.0.2.tgz#16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985"
    integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
    dependencies:
      define-data-property "^1.1.4"
      es-errors "^1.3.0"
      functions-have-names "^1.2.3"
      has-property-descriptors "^1.0.2"
  
  shebang-command@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
    integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
    dependencies:
      shebang-regex "^3.0.0"
  
  shebang-regex@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
    integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==
  
  side-channel@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
    integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
    dependencies:
      call-bind "^1.0.0"
      get-intrinsic "^1.0.2"
      object-inspect "^1.9.0"
  
  skmeans@0.9.7:
    version "0.9.7"
    resolved "https://registry.npmmirror.com/skmeans/-/skmeans-0.9.7.tgz#72670cebb728508f56e29c0e10d11e623529ce5d"
    integrity sha512-hNj1/oZ7ygsfmPZ7ZfN5MUBRoGg1gtpnImuJBgLO0ljQ67DtJuiQaiYdS4lUA6s0KCwnPhGivtC/WRwIZLkHyg==
  
  splaytree@^3.1.0:
    version "3.1.2"
    resolved "https://registry.npmmirror.com/splaytree/-/splaytree-3.1.2.tgz#d1db2691665a3c69d630de98d55145a6546dc166"
    integrity sha512-4OM2BJgC5UzrhVnnJA4BkHKGtjXNzzUfpQjCO8I05xYPsfS/VuQDwjCGGMi8rYQilHEV4j8NBqTFbls/PZEE7A==
  
  string.prototype.trim@^1.2.7:
    version "1.2.7"
    resolved "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.7.tgz"
    integrity sha512-p6TmeT1T3411M8Cgg9wBTMRtY2q9+PNy9EV1i2lIXUN/btt763oIfxwN3RR8VU6wHX8j/1CFy0L+YuThm6bgOg==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  string.prototype.trimend@^1.0.6:
    version "1.0.6"
    resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.6.tgz"
    integrity sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  string.prototype.trimstart@^1.0.6:
    version "1.0.6"
    resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.6.tgz"
    integrity sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.4"
      es-abstract "^1.20.4"
  
  strip-ansi@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
    integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
    dependencies:
      ansi-regex "^5.0.1"
  
  strip-bom@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
    integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==
  
  strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
    integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==
  
  supercluster@^7.1.0:
    version "7.1.5"
    resolved "https://registry.npmmirror.com/supercluster/-/supercluster-7.1.5.tgz#65a6ce4a037a972767740614c19051b64b8be5a3"
    integrity sha512-EulshI3pGUM66o6ZdH3ReiFcvHpM3vAigyK+vcxdjpJyEbIIrtbmBdY23mGgnI24uXiGFvrGq9Gkum/8U7vJWg==
    dependencies:
      kdbush "^3.0.0"
  
  supports-color@^5.3.0:
    version "5.5.0"
    resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
    integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
    dependencies:
      has-flag "^3.0.0"
  
  supports-color@^7.1.0:
    version "7.2.0"
    resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
    integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
    dependencies:
      has-flag "^4.0.0"
  
  supports-preserve-symlinks-flag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
    integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==
  
  text-table@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
    integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==
  
  tinyqueue@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/tinyqueue/-/tinyqueue-2.0.3.tgz#64d8492ebf39e7801d7bd34062e29b45b2035f08"
    integrity sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA==
  
  tinyqueue@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/tinyqueue/-/tinyqueue-3.0.0.tgz#101ea761ccc81f979e29200929e78f1556e3661e"
    integrity sha512-gRa9gwYU3ECmQYv3lslts5hxuIa90veaEcxDYuu3QGOIAEM2mOZkVHp48ANJuu1CURtRdHKUBY5Lm1tHV+sD4g==
  
  to-fast-properties@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
    integrity sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==
  
  topojson-client@3.x:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/topojson-client/-/topojson-client-3.1.0.tgz#22e8b1ed08a2b922feeb4af6f53b6ef09a467b99"
    integrity sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==
    dependencies:
      commander "2"
  
  topojson-server@3.x:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/topojson-server/-/topojson-server-3.0.1.tgz#d2b3ec095b6732299be76a48406111b3201a34f5"
    integrity sha512-/VS9j/ffKr2XAOjlZ9CgyyeLmgJ9dMwq6Y0YEON8O7p/tGGk+dCWnrE03zEdu7i4L7YsFZLEPZPzCvcB7lEEXw==
    dependencies:
      commander "2"
  
  tsconfig-paths@^3.14.1:
    version "3.14.2"
    resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.14.2.tgz"
    integrity sha512-o/9iXgCYc5L/JxCHPe3Hvh8Q/2xm5Z+p18PESBU6Ff33695QnCHBEjcytY2q19ua7Mbl/DavtBOLq+oG0RCL+g==
    dependencies:
      "@types/json5" "^0.0.29"
      json5 "^1.0.2"
      minimist "^1.2.6"
      strip-bom "^3.0.0"
  
  tslib@^2.8.1:
    version "2.8.1"
    resolved "https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
    integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==
  
  turf-jsts@*:
    version "1.2.3"
    resolved "https://registry.npmmirror.com/turf-jsts/-/turf-jsts-1.2.3.tgz#59757f542afbff9a577bbf411f183b8f48d38aa4"
    integrity sha512-Ja03QIJlPuHt4IQ2FfGex4F4JAr8m3jpaHbFbQrgwr7s7L6U8ocrHiF3J1+wf9jzhGKxvDeaCAnGDot8OjGFyA==
  
  type-check@^0.4.0, type-check@~0.4.0:
    version "0.4.0"
    resolved "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
    integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
    dependencies:
      prelude-ls "^1.2.1"
  
  type-fest@^0.20.2:
    version "0.20.2"
    resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
    integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==
  
  typed-array-length@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.4.tgz"
    integrity sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==
    dependencies:
      call-bind "^1.0.2"
      for-each "^0.3.3"
      is-typed-array "^1.1.9"
  
  uid@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/uid/-/uid-2.0.0.tgz"
    integrity sha512-hFw+zKBA1szYdbZWj6FjTxZzJnKNf+wTDcsxlJaXS64MCy9LQEmJUVieGYHCKek/WRyFIcs0cEXtGIQmfvHe2A==
    dependencies:
      "@lukeed/csprng" "^1.0.0"
  
  unbox-primitive@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
    integrity sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==
    dependencies:
      call-bind "^1.0.2"
      has-bigints "^1.0.2"
      has-symbols "^1.0.3"
      which-boxed-primitive "^1.0.2"
  
  update-browserslist-db@^1.0.10:
    version "1.0.11"
    resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz"
    integrity sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==
    dependencies:
      escalade "^3.1.1"
      picocolors "^1.0.0"
  
  uri-js@^4.2.2:
    version "4.4.1"
    resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
    integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
    dependencies:
      punycode "^2.1.0"
  
  util-deprecate@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
    integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==
  
  uview-ui@^2.0.35:
    version "2.0.36"
    resolved "https://registry.npmjs.org/uview-ui/-/uview-ui-2.0.36.tgz"
    integrity sha512-ASSZT6M8w3GTO1eFPbsgEFV0U5UujK+8pTNr+MSUbRNcRMC1u63DDTLJVeArV91kWM0bfAexK3SK9pnTqF9TtA==
  
  vt-pbf@^3.1.1:
    version "3.1.3"
    resolved "https://registry.npmmirror.com/vt-pbf/-/vt-pbf-3.1.3.tgz#68fd150756465e2edae1cc5c048e063916dcfaac"
    integrity sha512-2LzDFzt0mZKZ9IpVF2r69G9bXaP2Q2sArJCmcCgvfTdCCZzSyz4aCLoQyUilu37Ll56tCblIZrXFIjNUpGIlmA==
    dependencies:
      "@mapbox/point-geometry" "0.1.0"
      "@mapbox/vector-tile" "^1.3.1"
      pbf "^3.2.1"
  
  vue-eslint-parser@^9.1.0, vue-eslint-parser@^9.3.0:
    version "9.3.0"
    resolved "https://registry.npmjs.org/vue-eslint-parser/-/vue-eslint-parser-9.3.0.tgz"
    integrity sha512-48IxT9d0+wArT1+3wNIy0tascRoywqSUe2E1YalIC1L8jsUGe5aJQItWfRok7DVFGz3UYvzEI7n5wiTXsCMAcQ==
    dependencies:
      debug "^4.3.4"
      eslint-scope "^7.1.1"
      eslint-visitor-keys "^3.3.0"
      espree "^9.3.1"
      esquery "^1.4.0"
      lodash "^4.17.21"
      semver "^7.3.6"
  
  which-boxed-primitive@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
    integrity sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==
    dependencies:
      is-bigint "^1.0.1"
      is-boolean-object "^1.1.0"
      is-number-object "^1.0.4"
      is-string "^1.0.5"
      is-symbol "^1.0.3"
  
  which-typed-array@^1.1.9:
    version "1.1.9"
    resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.9.tgz"
    integrity sha512-w9c4xkx6mPidwp7180ckYWfMmvxpjlZuIudNtDf4N/tTAUB8VJbX25qZoAsrtGuYNnGw3pa0AXgbGKRB8/EceA==
    dependencies:
      available-typed-arrays "^1.0.5"
      call-bind "^1.0.2"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-tostringtag "^1.0.0"
      is-typed-array "^1.1.10"
  
  which@^2.0.1:
    version "2.0.2"
    resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
    integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
    dependencies:
      isexe "^2.0.0"
  
  word-wrap@^1.2.3:
    version "1.2.3"
    resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
    integrity sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==
  
  wrappy@1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
    integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==
  
  xml-name-validator@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-4.0.0.tgz"
    integrity sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==
  
  yallist@^3.0.2:
    version "3.1.1"
    resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
    integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==
  
  yallist@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
    integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==
  
  yocto-queue@^0.1.0:
    version "0.1.0"
    resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
    integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==
