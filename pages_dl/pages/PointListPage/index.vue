<template>
  <view>
    <u-navbar
      title="点位列表"
      rightIcon="map"
      @leftClick="leftClickHandler"
      @rightClick="rightClickHandler"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
    ></u-navbar>
    <view class="content">
      <TabSearchList
        ref="tabSearchList"
        :tabSectionList="tabSectionList"
        :tabFontSize="tabFontSize"
        :searchItem="{}"
        :formUid="formUid"
        :operations="operations"
        :showTab="showTab"
        @operate="operateHandler"
      ></TabSearchList>
    </view>
    <view class="fixed-btn" v-if="formUid !== '地膜监测点位任务表'">
      <u-button
        type="primary"
        @click="addButtonClick"
        :color="$constants.COLOR.PRIMARY_COLOR"
        >添加</u-button
      >
    </view>

    <!-- 弹窗 -->
    <u-popup
      :show="value"
      mode="center"
      @close="close"
      closeable
      :closeOnClickOverlay="false"
      :customStyle="customPopStyle"
    >
      <view class="form-content">
        <view class="title">{{ popupTitle }}</view>
        <GTForm ref="gtForm" :options="formOptions" @submit="submit"></GTForm>
      </view>
      <view class="btn-list">
        <u-button text="取消" @click="value = false"></u-button>
        <u-button
          text="提交"
          type="primary"
          :color="$constants.COLOR.PRIMARY_COLOR"
          @click="commit"
        ></u-button>
      </view>
    </u-popup>
  </view>
</template>

<script>
// 数据采集类型 数组
const DATA_TYPE_ARRAY = ["地膜监测", "地膜调查", "棚膜调查"];

import TabPage from "@/pages/TabPage";
import BasePage from "@/pages/BasePage";
import TabSearchList from "@/components/common/TabSearchList";
import Navigation from "@/tools/Navigation";
import { formDef } from "./config";
import GTForm from "@/components/form/GTForm";
import { constants } from "@/pages_dl/constants";
import formDataApi from "@/apis/formData";

export default {
  mixins: [TabPage, BasePage],
  components: { TabSearchList, GTForm },
  onLoad(option) {
    this.formUid = option.子任务表_uid;
    this.dataType = option.任务类型;
    this.任务_uid = option.任务_uid;
    this.menuName = option.menuName;
    this.taskYear = option.年份;
    const systemConfigData = uni.getStorageSync("systemConfig");
    this.systemConfig = systemConfigData ? JSON.parse(systemConfigData) : null;
  },
  data() {
    return {
      menuName: null,
      dataType: null,
      任务_uid: null,
      tabSearchFilter: null,
      tabSearchHeight: null,
      tabFontSize: 15,
      formUid: null,
      dataList: [],
      taskYear: null,
      value: false,
      popupTitle: "",
      popupType: null,
      formOptions: {
        formDef,
        formRecordData: "",
      },
      // 新增的编号
      addFormNo: null,
    };
  },
  computed: {
    tabSectionList() {
      const { _id, fcode } = JSON.parse(uni.getStorageSync("userInfo"));
      switch (this.dataType) {
        case DATA_TYPE_ARRAY[0]: {
          return [
            {
              filter: [
                "and",
                ["=", "监测点类型", "地膜监测国控点"],
                ["=", "分配采集人", _id],
                ["=", "任务_uid", this.任务_uid],
              ],
              section: "国控点",
            },
            {
              filter: [
                "and",
                ["=", "监测点类型", "地膜监测省控点"],
                ["=", "任务_uid", this.任务_uid],
                ["=", "分配采集人", _id],
              ],
              section: "省控点",
            },
          ];
        }
        case DATA_TYPE_ARRAY[1]: {
          return [
            {
              filter: [
                "and",
                ["=", "任务_uid", this.任务_uid],
                ["=", "分配采集人", _id],
                ["=", "县编码", fcode],
              ],
            },
          ];
        }
        case DATA_TYPE_ARRAY[2]: {
          return [
            {
              filter: [
                "and",
                ["=", "任务_uid", this.任务_uid],
                ["=", "分配采集人", _id],
                ["=", "县编码", fcode],
              ],
            },
          ];
        }
      }
    },
    showTab() {
      switch (this.dataType) {
        case DATA_TYPE_ARRAY[0]: {
          return true;
        }
        case DATA_TYPE_ARRAY[1]: {
          return false;
        }
        case DATA_TYPE_ARRAY[2]: {
          return false;
        }
      }
    },
    operations() {
      switch (this.dataType) {
        case DATA_TYPE_ARRAY[0]: {
          return [
            {
              key: "view",
              label: "查看",
              icon: "file-text",
              type: "primary",
            },
            {
              key: "add",
              label: "填报",
              icon: "edit-pen",
              type: "primary",
            },
          ];
        }
        case DATA_TYPE_ARRAY[1]: {
          return [
            // {
            //   key: "navigate",
            //   label: "到这去",
            //   type: "primary",
            // },
            {
              key: "delete",
              label: "删除",
              icon: "trash",
              type: "primary",
            },
            {
              key: "edit",
              label: "编辑",
              icon: "file-text",
              type: "primary",
            },
            {
              key: "view",
              label: "查看",
              icon: "list",
              type: "primary",
            },
            {
              key: "add",
              label: "填报",
              icon: "edit-pen",
              type: "primary",
            },
          ];
        }
        case DATA_TYPE_ARRAY[2]: {
          return [
            {
              key: "delete",
              label: "删除",
              icon: "trash",
              type: "primary",
            },
            {
              key: "edit",
              label: "编辑",
              icon: "file-text",
              type: "primary",
            },
            {
              key: "view",
              label: "查看",
              icon: "list",
              type: "primary",
            },
            {
              key: "add",
              label: "填报",
              icon: "edit-pen",
              type: "primary",
            },
          ];
        }
      }
    },
    customPopStyle() {
      return {
        width: "80%",
        padding: "40rpx",
        "box-shadow": "0rpx 0rpx 15rpx #ccc",
      };
    },
  },
  methods: {
    rightClickHandler() {
      const pageUrl = `${constants.PAGE.MAP_PAGE}?formUid=${this.formUid}&dataType=${this.dataType}&任务_uid=${this.任务_uid}&menuName=${this.menuName}`;
      uni.redirectTo({
        url: pageUrl,
      });
    },
    // 点击数据列表某项，key为 itemClick
    async operateHandler(payload) {
      const { type, key, item } = payload;
      const typeArr = ["cy", "dc"];
      const { 调查表单_uid, 采样表单_uid, 调查指标值, 采样指标值 } = item;
      let formUid = type === typeArr[0] ? 采样表单_uid : 调查表单_uid;
      let indexField = type === typeArr[0] ? 采样指标值 : 调查指标值;
      switch (key) {
        case "add": {
          const total = await this.getFormDataTotal(formUid, item);
          if (total >= indexField) {
            this.showError("点位指标已满");
            return;
          }
          const pageUrl = `${constants.PAGE.FORM_PAGE_URL}?formUid=${formUid}`;
          const genUrl = this.getGenPageUrl(pageUrl, item);
          this.$nextTick(() => {
            this.navigateTo(genUrl);
          });
          break;
        }
        case "view": {
          const pageUrl = `${constants.PAGE.FORM_LIST_URL}?formUid=${formUid}`;
          console.log("pageUrl", pageUrl);

          const genUrl = this.getGenPageUrl(pageUrl, item);
          console.log("genUrl", genUrl);

          this.navigateTo(genUrl);
          break;
        }

        case "navigate": {
          this.navigateMap(item);
          break;
        }

        case "edit": {
          const total = await this.getFormDataTotal(formUid, item);
          if (total > 0) {
            this.showError("已填报数据不可编辑");
            return;
          }
          this.editHandle(item);
          break;
        }

        case "delete": {
          const total = await this.getFormDataTotal(formUid, item);
          if (total > 0) {
            this.showError("已填报数据不可删除");
            return;
          }
          this.deleteHandle(item);
          break;
        }
      }
    },
    navigateMap(item) {
      const { 经度, 纬度 } = item;
      Navigation.navigateTo(经度, 纬度);
    },
    async getFormDataTotal(formUid, item) {
      const { 任务_uid, 监测点_uid } = item;
      const filter = [
        "and",
        ["=", "任务_uid", 任务_uid],
        ["=", "监测点_uid", 监测点_uid],
      ];
      const params = {
        filter,
        page: {
          pageNum: 1,
          pageSize: 10,
        },
      };
      const res = await formDataApi.getFormRecords(formUid, params);
      return res.total;
    },
    getGenPageUrl(url, item) {
      const fields = [
        "任务_uid",
        "年份",
        "监测点_uid",
        "监测点类型",
        "监测点编号",
        "省编码",
        "省名称",
        "市编码",
        "市名称",
        "县编码",
        "县名称",
        "乡镇编码",
        "乡镇名称",
        "村编码",
        "村名称",
        "经度",
        "纬度",
        "调查指标值",
      ];
      let obj = {};
      fields.forEach((f) => {
        const prefix = f === "经度" || f === "纬度" ? "地块中心" : "";
        const itemUrl = `&${prefix}${f}=${item[f]}`;
        obj[`${prefix}${f}`] = item[f];
        // url = url.concat(itemUrl);
      });

      if (item["采样指标值"]) obj.采样指标值 = item["采样指标值"];
      // url = url.concat(`&采样指标值=${item["采样指标值"]}`);
      url = url.concat(`&data=${JSON.stringify(obj)}`);

      return url;
    },

    async addButtonClick() {
      this.getData();
      this.popupTitle = "添加点位";
      const { pname, pcode, cname, ccode, fname, fcode } = JSON.parse(
        uni.getStorageSync("userInfo")
      );
      // 使用 default: "{user.fname}" 赋值不生效
      this.formOptions.formRecordData = {
        省名称: pname,
        省编码: pcode,
        市名称: cname,
        市编码: ccode,
        县名称: fname,
        县编码: fcode,
      };

      this.popupType = "add";
      this.value = true;
    },
    close() {
      this.value = false;
    },

    /**
     * Commits the form submission.
     */
    commit() {
      this.$refs.gtForm.submit();
    },
    // 编辑点
    async editHandle(data) {
      this.popupTitle = "编辑点位信息";
      this.formOptions.formRecordData = data;
      this.popupType = "edit";
      this.value = true;

      // 刷新数据
      this.$refs["tabSearchList"].refreshData();
    },
    // 删除点
    async deleteHandle(data) {
      let _this = this;
      uni.showModal({
        content: `是否删除点位:${data.监测点编号}？`,
        success: async function (res) {
          if (res.confirm) {
            try {
              const formName =
                data.监测点类型 === "地膜调查点"
                  ? "地膜调查点位任务表"
                  : "棚膜调查点位任务表";
              await formDataApi.deleteFormRecord(formName, data._id);
              await formDataApi.deleteFormRecord(
                "农膜监测点位表",
                data.监测点_uid
              );
              uni.showToast({
                title: "删除成功",
                icon: "success",
              });
              // 刷新数据
              _this.$refs["tabSearchList"].refreshData();
            } catch (error) {
              uni.showToast({
                title: "删除失败",
                icon: "error",
              });
            }
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
    baseData(data) {
      return {
        省名称: data.省名称,
        市名称: data.市名称,
        县名称: data.县名称,
        乡镇名称: data.乡镇名称,
        村名称: data.村名称,
        省编码: data.省编码,
        市编码: data.市编码,
        县编码: data.县编码,
        乡镇编码: data.乡镇编码,
        村编码: data.村编码,
        作物: data.作物,
        纬度: data.纬度,
        经度: data.经度,
      };
    },
    // 新增点
    async submit(data) {
      const { username, _id } = JSON.parse(uni.getStorageSync("userInfo"));
      const baseData = this.baseData(data);
      const taskYearShort = this.taskYear.slice(-2);
      const dataType = this.dataType;
      const DorP = dataType === "地膜调查" ? "D" : "P";
      const addFormNo = this.addFormNo;
      const task_uid = this.任务_uid;

      // 设置通用数据
      data.类型 = `${dataType}点`;
      data.状态 = "启用";
      data.编号 = `${data.县编码}-${DorP}-${taskYearShort}-${addFormNo}`;
      data.地址 = `${data.省名称}${data.市名称}${data.县名称}${data.乡镇名称}`;

      const handleSuccess = () => {
        this.value = false;
        uni.showToast({ title: "操作成功", icon: "success" });
        this.$refs["tabSearchList"].refreshData();
      };

      const handleError = (error) => {
        uni.showToast({ title: "操作失败", icon: "error" });
        console.error("操作出错:", error);
      };

      if (this.popupType === "add") {
        const newData = {
          ...baseData,
          任务_uid: task_uid,
          监测点_uid: null,
          年份: Number(this.taskYear),
          监测点类型: data.类型,
          监测点编号: data.编号,
          调查指标值:
            dataType === "地膜调查"
              ? Number(this.systemConfig.地膜调查指标值)
              : Number(this.systemConfig.棚膜调查指标值),
          调查表单_uid:
            dataType === "地膜调查"
              ? this.systemConfig.地膜调查表单UID
              : this.systemConfig.棚膜调查表单UID,
          分配状态: "已分配",
          分配采集人用户名: username,
          分配采集人: _id,
          审核状态: "填报中",
          审核说明: null,
        };

        try {
          const res = await formDataApi.newAddFormRecord(
            "农膜监测点位表",
            data
          );
          newData.监测点_uid = res;
          newData.唯一值 = `${task_uid},${res}`;
          const formName =
            dataType === "地膜调查"
              ? "地膜调查点位任务表"
              : "棚膜调查点位任务表";
          await formDataApi.addFormRecord(formName, newData);
          handleSuccess();
        } catch (error) {
          const res = await formDataApi.getFormRecords("农膜监测点位表", {
            filter: ["=", "编号", data.编号],
          });
          data._id = res.list[0]._id;
          await formDataApi.newUpdateFormRecord(
            "农膜监测点位表",
            data._id,
            data
          );
          newData.监测点_uid = res.list[0]._id;
          newData.唯一值 = `${task_uid},${res.list[0]._id}`;
          const formName =
            dataType === "地膜调查"
              ? "地膜调查点位任务表"
              : "棚膜调查点位任务表";
          await formDataApi.addFormRecord(formName, newData);
          handleSuccess();
        }
      } else {
        try {
          await formDataApi.newUpdateFormRecord(
            "农膜监测点位表",
            data.监测点_uid,
            {
              ...baseData,
              _id: data.监测点_uid,
            }
          );
          const formName =
            data.监测点类型 === "地膜调查点"
              ? "地膜调查点位任务表"
              : "棚膜调查点位任务表";
          await formDataApi.newUpdateFormRecord(formName, data._id, {
            ...baseData,
            _id: data._id,
          });
          handleSuccess();
        } catch (error) {
          handleError(error);
        }
      }
    },
    // 获取编号数据，返回下一个新增的编号
    async getData() {
      if (this.formUid == "地膜监测点位任务表") return;
      const { fcode } = JSON.parse(uni.getStorageSync("userInfo"));
      let params = {
        filter: [
          "and",
          ["=", "任务_uid", this.任务_uid],
          ["=", "县编码", fcode],
        ],
        page: {
          pageNum: 1,
          pageSize: 2000,
        },
      };
      console.log("请求参数:", this.formUid, params);
      let res = await formDataApi.getFormRecords(this.formUid, params);
      console.log("监测点位管理数据：", res);
      const { list } = res;
      const bhList = list.map((item) => item.监测点编号);
      bhList.forEach((item, index) => {
        const arr = item.split("-");
        bhList[index] = Number(arr[arr.length - 1]);
      });
      this.addFormNo = this.findNewId(bhList);
    },
    // 找到下一个新增的编号
    findNewId(nums) {
      if (nums.length == 0) return "01";
      nums.sort((a, b) => a - b);
      for (let i = 1; i <= nums[nums.length - 1]; i++) {
        if (!nums.includes(i)) {
          return i.toString().padStart(2, "0");
        }
      }
      return (nums[nums.length - 1] + 1).toString().padStart(2, "0");
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_dl/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
}

.fixed-btn {
  height: 130rpx;
  position: fixed;
  bottom: -1px;
  left: 0;
  width: 100%;

  .u-button {
    height: 100%;
    font-size: 40rpx;
  }
}

.btn-list {
  display: flex;

  .u-button {
    flex: 1;
    margin: 20rpx 10rpx 10rpx 10rpx;
  }
}

.form-content {
  .title {
    text-align: center;
    font-size: 40rpx;
  }
}
</style>
