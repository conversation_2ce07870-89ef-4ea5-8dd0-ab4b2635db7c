export const formDef = {
  name: "点位管理",
  dicts: [
    {
      name: "admin_village",
      type: "table",
      fields: ["vname", "vcode", "tcode"],
      labelField: "vname",
      valueField: "vcode",
    },
    {
      name: "admin_town",
      type: "table",
      fields: ["tname", "tcode", "fcode"],
      labelField: "tname",
      valueField: "tcode",
    },
  ],
  layout: [
    {
      type: "TextField",
      options: {
        hide: true,
        type: "text",
        field: "省名称",
        label: "省名称",
        default: "{user.pname}",
        required: true,
      },
    },
    {
      type: "TextField",
      options: {
        hide: true,
        type: "text",
        field: "省编码",
        label: "省编码",
        default: "{user.pcode}",
        required: true,
      },
    },
    {
      type: "TextField",
      options: {
        hide: true,
        type: "text",
        field: "市名称",
        label: "市名称",
        default: "{user.cname}",
        required: true,
      },
    },
    {
      type: "TextField",
      options: {
        hide: true,
        type: "text",
        field: "市编码",
        label: "市编码",
        default: "{user.ccode}",
        required: true,
      },
    },
    {
      type: "TextField",
      options: {
        hide: true,
        type: "text",
        field: "县名称",
        label: "县名称",
        default: "{user.fname}",
        required: true,
      },
    },
    {
      type: "TextField",
      options: {
        hide: true,
        type: "text",
        field: "县编码",
        label: "县编码",
        default: "{user.fcode}",
        required: true,
      },
    },
    {
      type: "TextField",
      options: {
        hide: true,
        type: "text",
        field: "乡镇名称",
        label: "乡镇名称",
        readonly: true,
        required: true,
        calculator: ["get", "tname", "乡镇编码"],
      },
    },
    {
      type: "SelectField",
      options: {
        lazy: true,
        type: "text",
        field: "乡镇编码",
        label: "乡镇名称",
        filter: ["=", "fcode", "县编码"],
        dictRef: "admin_town",
        required: true,
      },
    },
    {
      type: "TextField",
      options: {
        hide: true,
        type: "text",
        field: "村名称",
        label: "村名称",
        readonly: true,
        required: true,
        calculator: ["get", "vname", "村编码"],
      },
    },
    {
      type: "SelectField",
      options: {
        lazy: true,
        type: "text",
        field: "村编码",
        label: "村名称",
        filter: ["=", "tcode", "乡镇编码"],
        dictRef: "admin_village",
        required: true,
      },
    },
    {
      type: "LonLatField",
      options: {
        label: "现场经纬度",
        lonField: {
          type: "numeric",
          field: "经度",
          label: "经度",
          required: true,
        },
        latField: {
          type: "numeric",
          field: "纬度",
          label: "纬度",
          required: true,
        },
        required: true,
        precision: 6,
      },
    },
    {
      type: "TextField",
      options: {
        type: "text",
        field: "作物",
        label: "作物",
        required: true,
      },
    },
  ],
  dictRefs: {
    村编码: "admin_village",
    乡镇编码: "admin_town",
  },
  validators: {
    fieldValidators: [
      {
        field: "任务_uid",
        label: "任务UID",
        validators: [
          {
            type: "required",
          },
        ],
      },
      {
        field: "乡镇名称",
        label: "乡镇名称",
        validators: [
          {
            type: "required",
          },
        ],
      },
      {
        field: "乡镇编码",
        label: "乡镇名称",
        validators: [
          {
            type: "required",
          },
        ],
      },
      {
        field: "村名称",
        label: "村名称",
        validators: [
          {
            type: "required",
          },
        ],
      },
      {
        field: "村编码",
        label: "村名称",
        validators: [
          {
            type: "required",
          },
        ],
      },

      {
        field: "纬度",
        label: "纬度",
        validators: [
          {
            type: "required",
          },
          {
            type: "number-range",
            options: {
              max: 54,
              min: 3,
            },
          },
        ],
      },
      {
        field: "经度",
        label: "经度",
        validators: [
          {
            type: "required",
          },
          {
            type: "number-range",
            options: {
              max: 135,
              min: 73,
            },
          },
        ],
      },
      {
        field: "作物",
        label: "作物",
        validators: [
          {
            type: "required",
          },
        ],
      },
    ],
  },
};
