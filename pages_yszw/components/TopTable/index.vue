<template>
  <view class="t-table" :style="{ height: height ? height : null }">
    <view class="t-head">
      <view
        class="t-head-item"
        v-for="(item, index) in config.columns"
        :key="index"
        :style="{ width: item.width || 750 / config.columns.length + 'rpx' }"
      >
        <text>{{ item.title }}</text>
      </view>
    </view>
    <view class="t-body" :style="{ height: `calc(${height}px - 70rpx)` }">
      <view class="t-content">
        <view class="t-tr" v-for="(item, index) in data" :key="index">
          <view
            class="t-td"
            v-for="(col, i) in config.columns"
            :key="i"
            :style="{ width: col.width || 750 / config.columns.length + 'rpx' }"
          >
            <view v-if="col.type == 'operate'">
              <view
                class="operate"
                v-for="operate in col.operates"
                @click="operateClick(operate, item)"
              >
                {{ operate.label }}
              </view>
            </view>
            <text
              :style="col.style"
              :class="col.type == 'index' ? ['td_' + index, 'td-index'] : []"
            >
              {{ setData(item, col, index) }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      height: null,
    };
  },
  props: {
    config: { type: Object },
    data: { type: Array },
  },
  mounted() {
    // let content = uni.createSelectorQuery().in(this).select('.t-table')
    // content
    //   .boundingClientRect(res => {
    //     console.log(res)
    //     this.height = res.height
    //     uni.$emit('setMapStyle', res)
    //   })
    //   .exec()
    // let content1 = uni.createSelectorQuery().in(this).select('.t-body')
    // content1
    //   .boundingClientRect(res => {
    //     console.log(res, 'content')
    //     uni.$emit('setMapStyle', res)
    //   })
    //   .exec()
  },
  methods: {
    setData(item, col, index) {
      if (col.type == "index") {
        return index < 3 ? "" : index + 1;
      }
      if (col.dataIndex) {
        if (col.customRender) {
          return col.customRender(item[col.dataIndex], item);
        }
        return item[col.dataIndex];
      }
    },
    operateClick(operate, item) {
      this.$emit(operate.key, item);
    },
  },
};
</script>

<style lang="scss" scoped>
.t-table {
  height: 100%;
  flex-direction: column;
  padding-bottom: 20rpx;
  font-size: 28rpx;

  .t-head {
    // flex: 1;
    display: flex;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #9c9c9c;

    .t-head-item {
      text-align: center;
    }
  }
  .t-body {
    overflow-y: scroll;

    .t-content {
      padding-bottom: 20px;
      .t-tr {
        display: flex;
        margin-bottom: 20rpx;
        .t-td {
          text-align: center;
          font-size: 28rpx;
          font-weight: 400;
          color: #333436;
          .td-index {
            display: inline-block;
            width: 50rpx;
            height: 50rpx;
            text-align: center;

            // border-radius: 50%;
          }
          .td_0 {
            color: #eab35e;
            background-image: url("@/static/images/yszw/top1.png");
            background-repeat: no-repeat;
            background-size: 100%;
          }
          .td_1 {
            color: #9fb2d5;
            background-image: url("@/static/images/yszw/top2.png");
            background-repeat: no-repeat;
            background-size: 100%;
          }
          .td_2 {
            color: #bb9a77;
            background-image: url("@/static/images/yszw/top3.png");
            background-repeat: no-repeat;
            background-size: 100%;
          }
        }
      }
    }
  }

  .operate {
    color: #6fa4e2;
  }
}
.box3 {
  height: 20rpx;
  background-color: aquamarine;
}
.wrap3 {
  flex: 1;
  height: 0;
  overflow-y: scroll;
}
</style>
