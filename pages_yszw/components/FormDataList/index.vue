<template>
  <view class="scroll-wrap">
    <scroll-view
      scroll-y
      class="scroll-content"
      @scrolltolower="scrollToLowerHandler"
    >
      <GTSearch
        v-if="showSearch"
        ref="GTSearch"
        class="search-wrapper"
        :placeholder="'请选择乡镇'"
        @search="searchHandler"
        @custom="searchHandler"
        @clear="clearHandler"
      ></GTSearch>
      <DataList
        :data="dataList"
        :fields="fields"
        :operations="operations"
        @edit="editHandler"
        @navigate="navigateHandler"
        @delete="deleteHandler"
        @view="viewHandler"
        @itemClick="viewHandler"
      />
      <view class="loading" v-if="dataList.length > 0">
        <u-loadmore :status="loadStatus" />
      </view>
    </scroll-view>
    <u-modal
      :show="modalShow"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_DELETE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="deleteCancelHandler"
      @confirm="deleteConfirmHandler"
    >
    </u-modal>
  </view>
</template>

<script>
import BaseDataList from "@/components/common/BaseDataList";
import DataList from "../DataList";
import Storage from "@/tools/Storage";
import Navigation from "@/tools/Navigation";
import GTSearch from "@/components/GTSearch";

import formDefApi from "@/apis/formDef";
import formDataApi from "@/apis/formData";
import { constants } from "@/pages_dm/constants";

const SEARCH_KEY_WORD = "乡镇名称";
const POINT_LON_FIELD = "经度";
const POINT_LAT_FIELD = "纬度";
const POINT_DATA_LON_FIELD = "地块中心经度";
const POINT_DATA_LAT_FIELD = "地块中心纬度";

const OPERATION = [
  {
    key: "edit",
    label: "编辑",
    depends: [
      "any",
      ["==", "审核状态", "已退回"],
      ["==", "审核状态", "已提交"],
    ],
    icon: "edit-pen",
    type: "primary",
  },
  {
    key: "view",
    label: "查看",
    icon: "file-text",
    type: "primary",
  },
  {
    key: "delete",
    label: "删除",
    type: "error",
    depends: [
      "any",
      ["==", "审核状态", "已退回"],
      ["==", "审核状态", "已提交"],
    ],
    icon: "trash",
  },
];

const PAGE_PARAM = {
  pageNum: 1,
  pageSize: 10,
};

export default {
  mixins: [BaseDataList],

  components: {
    DataList,
    GTSearch,
  },

  props: {
    formUid: {
      type: String,
    },

    filter: {
      type: Array,
    },

    formPageUrl: {
      type: String,
    },
    customOptions: {
      type: Object,
    },
  },

  data() {
    return {
      dataList: [],
      fields: [],
      page: JSON.parse(JSON.stringify(PAGE_PARAM)),
      loadStatus: "loadmore",
      modalShow: false,
      delItemId: undefined,
      operations: OPERATION,
      formDef: null,
      searchFilter: null,
    };
  },

  async mounted() {
    await this.getFormDesign();
    await this.reloadData();
  },
  watch: {
    filter: {
      async handler() {
        this.page = JSON.parse(JSON.stringify(PAGE_PARAM));

        await this.reloadData();
      },
      deep: true,
    },
  },
  computed: {
    showSearch() {
      return !!this.formDef?.list.searchFields;
    },
  },

  methods: {
    clearSearchFields() {
      if (!this.showSearch) return;
      this.$refs["GTSearch"].clearHandle();
    },

    async getFormDesign() {
      this.formDef = await formDefApi.getFormDef(this.formUid);
      this.fields = this.formDef.list.listFields;
    },

    async searchHandler(e) {
      this.searchFilter = !e ? null : this.getSearchFilter(e);
      await this.reloadData();
    },
    clearHandler(e) {
      this.searchFilter = null;
    },
    getSearchFilter(keyword) {
      let genFilter = [];
      if (!keyword) return;
      const searchFilter = ["like", "乡镇名称", `%${keyword}%`];
      if (Array.isArray(this.filter) && this.filter.length > 0) {
        if (Array.isArray(this.filter[1])) {
          genFilter = [...this.filter, searchFilter];
        } else {
          const arr = [this.filter];
          arr.unshift("and");
          genFilter = [...arr, searchFilter];
        }
      } else {
        genFilter = searchFilter;
      }
      return genFilter;
    },

    async loadMoreData() {
      this.loadStatus = "loading";
      const params = {
        filter:
          Array.isArray(this.searchFilter) && this.searchFilter.length > 0
            ? this.searchFilter
            : this.filter,
        page: this.page,
      };
      try {
        let res = await formDataApi.getFormRecords(this.formUid, params);
        this.dataList.push(...res.list);
        if (res.list && res.list.length < this.page.pageSize) {
          this.loadStatus = "nomore";
        }
      } catch (e) {
        console.log("加载数据发生错误：", e);
      }
    },

    async reloadData() {
      console.log(this.filter);
      this.page.pageNum = 1;
      const params = {
        filter:
          Array.isArray(this.searchFilter) && this.searchFilter.length > 0
            ? this.searchFilter
            : this.filter,
        page: this.page,
      };
      try {
        let res = await formDataApi.getFormRecords(this.formUid, params);
        this.dataList = res.list;
        if (res.list && res.list.length < this.page.pageSize) {
          this.loadStatus = "nomore";
        }
      } catch (e) {
        console.log("加载数据发生错误：", e);
      }
    },

    async scrollToLowerHandler() {
      this.page.pageNum += 1;
      await this.loadMoreData();
    },

    editHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      Storage.saveFormData(formRecordUid, data);
      if (this.formPageUrl) {
        const pageUrl = `${constants.PAGE[this.formPageUrl]}?formUid=${
          this.formUid
        }&formRecordUid=${formRecordUid}`;
        const genUrl = this.getGenPageUrl(pageUrl);
        this.navigateTo(genUrl);
        return;
      }
      const pageUrl = `${constants.PAGE.FORM_PAGE_URL}?formUid=${this.formUid}&formRecordUid=${formRecordUid}`;
      const genUrl = this.getGenPageUrl(pageUrl);
      this.navigateTo(genUrl);
    },

    navigateHandler(data) {
      let lon = data[POINT_LON_FIELD] || data[POINT_DATA_LON_FIELD];
      let lat = data[POINT_LAT_FIELD] || data[POINT_DATA_LAT_FIELD];
      if (isNaN(lon) || isNaN(lat)) return;
      Navigation.navigateTo(lon, lat);
    },

    deleteHandler(data) {
      this.delItemId = data._id;

      if (!this.delItemId) return;
      this.modalShow = true;
    },

    async deleteConfirmHandler() {
      try {
        this.showLoading(this.$constants.MSG.DELETING);
        await formDataApi.deleteFormRecord(this.formUid, this.delItemId);
        await this.reloadData();
        this.showSuccess(this.$constants.MSG.DELETE_SUCCESS);
      } catch (e) {
        this.showError(this.$constants.MSG.DELETE_FAIL);
      } finally {
        this.hideLoading();
      }
      this.delItemId = undefined;
      this.modalShow = false;
    },

    deleteCancelHandler() {
      this.delItemId = undefined;
      this.modalShow = false;
    },

    viewHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      Storage.saveFormData(formRecordUid, data);
      const pageUrl = `${constants.PAGE.VIEW_PAGE_URL}?formUid=${this.formUid}&formRecordUid=${formRecordUid}`;
      const genUrl = this.getGenPageUrl(pageUrl);
      this.navigateTo(genUrl);
    },

    getGenPageUrl(url) {
      Object.keys(this.customOptions).forEach((item) => {
        const itemUrl = `&${item}=${this.customOptions[item]}`;
        url = url.concat(itemUrl);
      });
      return url;
    },
  },
};
</script>

<style lang="less" scoped>
.scroll-wrap {
  height: 100%;
  .scroll-content {
    height: 100%;
  }
}
</style>
