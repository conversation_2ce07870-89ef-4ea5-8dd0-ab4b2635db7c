<template>
  <view>
    <u-navbar
      title="填报"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      autoBack
    ></u-navbar>
    <view class="content" v-if="currentPointData">
      <view
        v-for="item in typeList"
        class="point-item"
        @click="itemClickHandle(item)"
      >
        <view class="point-cont">
          <view class="title"> {{ item.label }}</view>
          <view class="cont">
            <view class="info">
              <view class="label">年份:</view>
              <view class="val">{{ currentPointData["年份"] }}</view>
              <view class="label">轮次:</view>
              <view class="val">{{ currentPointData["轮次"] }}</view>
            </view>
          </view>
          <view class="arrow">
            <u-icon name="play-right-fill" color="#00a88a"></u-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Storage from "@/tools/Storage";
import _ from "lodash";

import { constants } from "@/pages_yszw/constants";

import formDataApi from "@/apis/formData";
import formDataLocalApi from "@/apis/formDataLocal";

export default {
  data() {
    return {
      currentPointData: null,
      typeList: [],
      pageType: null,
    };
  },
  async onLoad(option) {
    const key = option.primaryId;
    this.pageType = option.type;
    const result = Storage.getFormData(key);
    console.log(result, "---");
    this.currentPointData = result;
    await this.getFormsData();
  },
  async onShow() {
    if (this.currentPointData) {
      await this.getFormsData();
    }
  },

  methods: {
    async getFormsData() {
      const TYPE_LIST = [
        {
          label: "监测表（一）",
          formUid: "野生植物原生境保护点监测表一",
        },
        {
          label: "监测表（二）",
          formUid: "野生植物原生境保护点监测表二",
        },
        {
          label: "报告",
          formUid: "野生植物原生境保护点监测报告表",
        },
      ];
      const arr = [];
      for (let i = 0; i < TYPE_LIST.length; i++) {
        const item = TYPE_LIST[i];
        let res = await formDataApi.getFormRecords(item.formUid, {
          filter: ["=", "唯一值", this.currentPointData["唯一值"]],
        });
        if (res && res.list && res.list.length) {
          item.formData = res.list[0];
        }
        // else {
        let cache = await formDataLocalApi.getCacheRecords(item.formUid);
        if (cache && cache.length) {
          console.log(cache);
          const cacheData = cache.find((c) => {
            return c.content["唯一值"] == this.currentPointData["唯一值"];
          });
          if (cacheData) {
            item.cacheData = cacheData;
          }
        }
        // }
        // console.log(item);
        arr.push(item);
      }
      this.typeList = arr;
      console.log(this.typeList);
    },
    itemClickHandle(e) {
      console.log(e, "🦌🦌🦌");
      if (e.formData) {
        const formRecordUid = e.formData._id;
        Storage.saveFormData(formRecordUid, e.formData);
        let url = `${constants.PAGE.PLANTS_FORM_PAGE}?formRecordUid=${formRecordUid}&formUid=${e["formUid"]}&cacheable=false`;
        if (this.pageType == "view") {
          url += "&readonly=1";
        }
        uni.navigateTo({
          url,
        });
        return;
      }

      if (e.cacheData) {
        const cacheRecordId = e.cacheData.id;
        Storage.saveFormData(cacheRecordId, e.cacheData.content);
        let url = `${constants.PAGE.PLANTS_FORM_PAGE}?cacheRecordId=${cacheRecordId}&formUid=${e["formUid"]}`;
        if (this.pageType == "view") {
          url += "&readonly=1";
        }
        uni.navigateTo({
          url,
        });
        return;
      }
      const { 唯一值, 年份, 轮次, 保护点_uid, 保护点名称, 所在地 } =
        _.cloneDeep(this.currentPointData);

      const data = { 唯一值, 年份, 轮次, 保护点_uid, 保护点名称, 所在地 };
      Storage.saveFormData(data["唯一值"], data);
      let url = `${constants.PAGE.PLANTS_FORM_PAGE}?primaryId=${data["唯一值"]}&formUid=${e["formUid"]}`;
      if (this.pageType == "view") {
        url += "&readonly=1";
      }
      uni.navigateTo({
        url,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";
/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
}

.content {
  padding: 30rpx 0 100rpx;
  .point-item {
    padding: 8rpx;
    margin: 0 auto 30rpx;
    border-radius: 20rpx;
    border: solid 1px #eee;
    box-shadow: 0 0 3px 2px #eee;
    width: 690rpx;
    max-height: 400rpx;
    background: #fff;
    display: flex;
    flex-direction: column;
    .point-cont {
      padding: 20rpx;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        rgba(0, 168, 139, 0.08) 0%,
        rgba(0, 168, 139, 0) 100%
      );
      border-radius: 16rpx;
      border: 8rpx solid #ffffff;
      position: relative;
      .title {
        color: $gt-primary-color;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
      }
      .cont {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0 0;
        .info {
          display: flex;
          flex: 1;
          .label {
            color: #666;
          }
          .val {
            font-weight: 400;
            margin-right: 30rpx;
            color: #333;
          }
        }
      }
      .arrow {
        position: absolute;
        right: 30rpx;
        top: calc(50% - 8px);
      }
    }
  }
}
</style>
