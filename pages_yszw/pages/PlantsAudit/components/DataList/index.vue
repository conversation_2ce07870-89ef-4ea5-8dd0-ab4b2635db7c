<template>
  <view class="wrap">
    <view v-if="!data || data.length === 0">
      <u-empty text="没有数据" icon="/static/images/empty.png"></u-empty>
    </view>
    <view class="data-item" v-for="item in data">
      <view class="item-cont">
        <view class="title">{{ item["保护点名称"] }}</view>

        <view class="cont">
          <view class="flex">
            <view v-for="col in defaultColumn" class="info">
              <view class="label">{{ col.label }}: </view>
              <view class="val"> {{ item[col.dataIndex] }} </view>
            </view>
          </view>

          <view :class="['info', item['审核状态'] == '已退回' ? 'active' : '']">
            <view class="label">状态</view>
            <view class="val">{{ item["审核状态"] }} </view>
          </view>
          <view v-if="item['审核状态'] == '已退回'">
            <view class="label">退回原因</view>
            <view class="val bg">{{ item["退回原因"] }} </view>
          </view>
        </view>
        <view class="operate">
          <view class="btn">
            <u-button
              type="info"
              @click="$emit('view', item)"
              shape="circle"
              icon="file-text"
              plain
            >
              查看
            </u-button>
          </view>
          <view class="btn" v-if="showAudit(item)">
            <u-button
              type="primary"
              @click="$emit('edit', item)"
              shape="circle"
              icon="edit-pen"
              plain
              :color="$constants.COLOR.PRIMARY_COLOR"
            >
              审核
            </u-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      defaultColumn: [
        {
          label: "年份",
          dataIndex: "年份",
        },
        {
          label: "轮次",
          dataIndex: "轮次",
        },
        // {
        //   label: "状态",
        //   dataIndex: "审核状态",
        // },
      ],
    };
  },
  props: {
    data: {
      type: Array,
    },
    userInfo: {
      type: Object,
    },
  },
  methods: {
    showAudit(item) {
      const codes = this.userInfo.roles.map((item) => item.code);
      if (item["审核状态"] == "已退回") {
        return false;
      }
      console.log(codes);
      if (codes.indexOf("admin") !== -1) {
        return true;
      }
      if (codes.indexOf("yszw_county_manage") !== -1) {
        // return item["审核状态"] == "未审核" || item["审核状态"] == "县级已审核";
        return item["审核状态"] == "未审核";
      }
      if (codes.indexOf("yszw_city_manage") !== -1) {
        return (
          // item["审核状态"] == "县级已审核" || item["审核状态"] == "市级已审核"
          item["审核状态"] == "县级已审核"
        );
      }
      if (codes.indexOf("yszw_province_manage") !== -1) {
        return item["审核状态"] == "市级已审核";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";
.wrap {
  padding: 20rpx;
  .data-item {
    padding: 8rpx;
    margin: 0 auto 30rpx;
    border-radius: 20rpx;
    border: solid 1px #eee;
    box-shadow: 0 0 3px 2px #eee;
    width: 690rpx;
    max-height: 400rpx;
    background: #fff;
    display: flex;
    flex-direction: column;
    .item-cont {
      padding: 20rpx 20rpx 0;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        rgba(0, 168, 139, 0.08) 0%,
        rgba(0, 168, 139, 0) 100%
      );
      border-radius: 16rpx;
      border: 8rpx solid #ffffff;
      .title {
        color: $gt-primary-color;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
      }
      .cont {
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        padding: 20rpx 0 0;
        .flex {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .info {
          margin: 10rpx 0;
          display: flex;
          flex: 1;
          font-size: 28rpx;
          .label {
            margin-right: 10rpx;
            color: #666;
          }
          .val {
            font-weight: 400;
            margin-right: 30rpx;
            color: #333;
          }
        }
        .active {
          .val {
            color: #d0021b;
          }
        }

        .bg {
          background-color: #e5e5e5;
          margin: 10rpx 0;
          padding: 5rpx 10rpx;
          border-radius: 5rpx;
          color: #6b6b6b;
        }
      }
      .operate {
        display: flex;
        justify-content: end;
        .btn {
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 50rpx;
          margin-left: 30rpx;
          height: 64rpx;
          .u-button {
            width: 160rpx;
            height: 64rpx;
          }
        }
      }
    }
  }
}
</style>
