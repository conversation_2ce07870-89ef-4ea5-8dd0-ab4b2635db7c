<template>
  <view class="wrap">
    <u-navbar
      title="进度汇交"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      :rightText="rightText"
      @rightClick="rightClickHandler"
      placeholder
      autoBack
    ></u-navbar>
    <view class="head-wrapper">
      <view class="select-actions-wrapper">
        <view
          v-for="item in selectActions"
          :key="item.key"
          :style="{ display: 'flex', margin: 'auto', padding: '15rpx' }"
          @click="selectActionClickHandler(item.key)"
          >{{ item.label || item.default
          }}<u-icon class="arrow" name="arrow-down"></u-icon
        ></view>
      </view>
      <view class="subsection-wrapper">
        <u-subsection
          class="subsection"
          :list="subList"
          :current="currentSection"
          @change="sectionChangeHandler"
          :activeColor="$constants.COLOR.PRIMARY_COLOR"
          fontSize="15"
        ></u-subsection>
      </view>
    </view>
    <view class="contain-wrapper">
      <ProgressInfo
        :options="progressOptions"
        :currentSection="currentSection"
      ></ProgressInfo>
      <ProgressPointList
        v-if="currentAdmin && !currentSection"
        :admin="currentAdmin"
      />
    </view>
    <u-popup
      v-if="show"
      :show="show"
      @close="closePopup"
      @open="showPopup"
      closeable
    >
      <view class="pop-content">
        <ProgressPopup
          ref="ProgressPopup"
          @adminChange="adminChangeHandler"
        ></ProgressPopup>
      </view>
    </u-popup>
    <view v-if="selectShow">
      <u-action-sheet
        :actions="selectList"
        :show="selectShow"
        @select="selectClick"
      ></u-action-sheet>
    </view>
  </view>
</template>

<script>
import ProgressInfo from "./ProgressInfo.vue";
import ProgressPopup from "./ProgressPopup.vue";
import ProgressPointList from "./components/ProgressPointList";

const selectActions = [
  { key: "year", default: 2022, label: "" },
  { key: "timeScale", default: "上半年", label: "" },
];
const subList = [{ name: "采集进度" }, { name: "审核进度" }];
export default {
  data() {
    return {
      subList,
      selectActions,
      currentSection: 0,
      show: false,
      selectShow: false,
      adminLabel: null,
      adminValue: null,
      adminLevel: null,
      parentCode: null,
      actionType: null,
      selectList: [],
      currentAdmin: null,
    };
  },
  components: { ProgressPopup, ProgressInfo, ProgressPointList },
  computed: {
    rightText() {
      return this.adminLabel && `切换【${this.adminLabel.slice(0, 2)}】`;
    },
    progressOptions() {
      return {
        adminLevel: this.adminLevel,
        adminCode: this.adminValue,
        parentCode: this.parentCode,
        year: this.selectActions[0].label || this.selectActions[0].default,
        rotation: this.selectActions[1].label || this.selectActions[1].default,
      };
    },
  },
  mounted() {
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      let userInfo = JSON.parse(uni.getStorageSync("userInfo"));
      const { fname, cname, pname, fcode, ccode, pcode } = userInfo;
      this.adminLabel = fname || cname || pname || "广西壮族自治区";
      this.adminValue = fcode;
      this.adminLevel = this.getDefaultLevel();
      this.currentAdmin = {
        fname,
        cname,
        pname,
        fcode,
        ccode,
        pcode,
        level: this.adminLevel,
      };

      this.parentCode = ccode;
    },
    getDefaultLevel() {
      let userInfo = JSON.parse(uni.getStorageSync("userInfo"));
      const { fcode, ccode, pcode } = userInfo;
      let level = null;
      level =
        (fcode && "county") ||
        (ccode && "county") ||
        (pcode && "city") ||
        "city";
      return level;
    },
    showPopup() {
      this.show = true;
    },
    closePopup() {
      this.show = false;
    },
    rightClickHandler() {
      this.showPopup();
    },
    adminChangeHandler(val) {
      console.log("🚀 ~ file: index.vue:66 ~ adminChangeHandler ~ val:", val);
      const { cname, pname, fname, fcode, ccode, pcode, level } = val;
      this.currentAdmin = val;
      this.adminLabel = fname || cname || pname;
      this.adminValue = fcode;
      this.adminLevel = level;
      this.parentCode = ccode;
      this.closePopup();
    },
    selectActionClickHandler(key) {
      if (!key) return;
      this.actionType = key;
      switch (key) {
        case "year": {
          this.selectList = this.getYearList();
          break;
        }
        case "timeScale": {
          this.selectList = [
            { name: "上半年", key: "上半年" },
            { name: "下半年", key: "下半年" },
          ];
          break;
        }
      }
      this.selectShow = true;
    },
    getYearList() {
      const currentYear = new Date().getFullYear();
      const lastYear = 2021;
      const arr = [];
      for (let i = 0; i >= lastYear - currentYear; i--) {
        arr.push({ name: currentYear + i, key: currentYear + i });
      }
      return arr;
    },
    selectClick(e) {
      this.selectActions.forEach((item) => {
        if (item.key === this.actionType) {
          item.label = e.name;
        }
      });
      this.selectShow = false;
    },
    sectionChangeHandler(val) {
      this.currentSection = val;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";
.wrap {
  height: 100vh;
  background-color: #f5f5f9;
}
/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
  .u-navbar__content__right {
    padding-right: 0;
    cursor: pointer;
    .u-navbar__content__right__text {
      color: $gt-navbar-title-color !important;
      font-size: 28rpx;
    }
  }
}

.head-wrapper {
  position: fixed;
  z-index: 99;
  width: 100%;
  background-color: #f5f5f9;
}

.contain-wrapper {
  background-color: #f5f5f9;
  margin-top: 220rpx;
  width: 100%;
}

.pop-content {
  height: 75vh;
}

.select-actions-wrapper {
  background: #ffffff;
  width: 710rpx;
  margin: 20rpx auto 0;
  height: 80rpx;
  display: flex;
  border-radius: 15rpx;
  font-size: 30rpx;
  .arrow {
    margin-left: 20rpx;
  }
}

.subsection-wrapper {
  height: 90rpx;
  margin: 20rpx;
  width: calc(100% - 40rpx);
  .subsection {
    height: 80rpx;
  }
}
</style>
