<template>
  <view class="info-wrapper">
    <view class="part">
      <view class="part-content">
        <view class="title-bar"> 整体情况 </view>
        <view class="species-area">
          <view v-for="(item, index) in CardList" :key="index">
            <view class="card-wrapper" :style="{ background: item.background }">
              <view class="num-label">{{ item.label }}</view>
              <view>
                <text class="num-value" :style="{ color: item.color }">{{
                  item.content
                }}</text>
                <text class="num-value" :style="{ color: item.color }">{{
                  item.unit
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="part">
      <view class="part-content">
        <view class="title-bar"> 审核进度 </view>
        <TopTable
          class="table-box"
          :config="tableConfig"
          :data="progressResult"
        />
      </view>
    </view>
  </view>
</template>

<script>
import TopTable from "@/pages_yszw/components/TopTable";

export default {
  data() {
    return {};
  },

  components: { TopTable },

  props: {
    progressResult: {
      type: Array,
      default: () => [],
    },
  },

  computed: {
    tableConfig() {
      return {
        columns: [
          {
            title: "排名",
            type: "index",
          },
          {
            title: "地区",
            dataIndex: "name",
            style: { width: "150rpx" },
          },
          {
            title: "审核进度",
            dataIndex: "rate",
            customRender: (rate) => {
              return `${parseFloat(rate.toFixed(1))}%`;
            },
            style: { width: "100rpx" },
          },
          {
            title: "完成数",
            dataIndex: "completeNum",
            customRender: (completeNum, item) => {
              return `${completeNum}/${item.totalNum}`;
            },
            style: { width: "100rpx" },
          },
        ],
      };
    },
    CardList() {
      let completeNumCount = 0;
      let totalNumCount = 0;
      if (this.progressResult) {
        completeNumCount = this.progressResult.reduce(
          (acc, cur) => acc + cur.completeNum,
          0
        );
        totalNumCount = this.progressResult.reduce(
          (acc, cur) => acc + cur.totalNum,
          0
        );
      }
      return [
        {
          label: "审核数量",
          background: "#C9E8FF",
          color: "#007CFF",
          content: `${completeNumCount}/${totalNumCount}`,
          unit: "个",
        },
        {
          label: "完成率",
          background: "#C2EBEA",
          color: "#00A88B",
          content: `${
            completeNumCount
              ? ((completeNumCount / totalNumCount) * 100).toFixed(1)
              : 0
          }`,
          unit: "%",
        },
      ];
    },
  },

  watch: {},
  methods: {},
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";

.info-wrapper {
  height: 100%;
}
.part {
  width: 690rpx;
  margin: 30rpx auto;
  background-color: #fff;
  padding: 8rpx;
  border: solid 1px #eee;
  box-shadow: 0 0 3px 2px #eee;
  border-radius: 20rpx;
  .part-content {
    padding: 20rpx;
    box-sizing: border-box;
    background: linear-gradient(
      180deg,
      rgba(0, 168, 139, 0.08) 0%,
      rgba(0, 168, 139, 0) 100%
    );
  }
}
.title-bar {
  color: $gt-primary-color;
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
}

.species-area {
  display: flex;
  justify-content: space-between;
  // width: calc(100% - 90rpx);
  margin-top: 20rpx;
}

.card-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100rpx;
  width: 315rpx;
  height: 135rpx;
  border-radius: 8rpx;
  .num-label {
    margin-bottom: 10rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 500;
  }
  .num-value {
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 500;
  }
}
</style>
