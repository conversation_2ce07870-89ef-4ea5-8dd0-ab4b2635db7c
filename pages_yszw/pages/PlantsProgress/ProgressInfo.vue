<template>
  <view class="info-wrapper">
    <ReviewProgress
      v-if="currentSection"
      :progressResult="progressResult"
    ></ReviewProgress>
    <CollectionProgress
      v-else
      :progressResult="progressResult"
    ></CollectionProgress>
  </view>
</template>

<script>
import CollectionProgress from "@/pages_yszw/pages/PlantsProgress/CollectionProgress.vue";
import ReviewProgress from "@/pages_yszw/pages/PlantsProgress/ReviewProgress.vue";
import PROGRESS from "@/pages_yszw/apis/progress";

export default {
  data() {
    return {
      progressResult: null,
    };
  },

  components: { CollectionProgress, ReviewProgress },

  props: {
    options: {
      type: Object,
      default: () => {},
    },
    currentSection: {
      type: Number,
      default: () => 0,
    },
  },

  computed: {},

  watch: {
    options: {
      async handler(val) {
        if (this.currentSection) {
          this.progressResult = await PROGRESS.getAuditProgress(val);
        } else {
          this.progressResult = await PROGRESS.getCollectionProgress(val);
        }
      },
      deep: true,
      immediate: false,
    },

    currentSection: {
      async handler(val) {
        if (val) {
          this.progressResult = await PROGRESS.getAuditProgress(this.options);
        } else {
          this.progressResult = await PROGRESS.getCollectionProgress(
            this.options
          );
        }
      },
      deep: true,
      immediate: false,
    },
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";

.info-wrapper {
  height: 100%;
  margin-left: 10rpx;
  width: calc(100% - 20rpx);
}
</style>
