<template>
  <view class="popup-wrap">
    <view class="admin-wrapper">
      <view v-for="(item, index) in adminList" :key="index">
        <text class="admin-type-title" v-if="item.data.length > 0">{{
          item.title
        }}</text>
        <u-grid :border="false" col="3">
          <u-grid-item
            v-for="(f, findex) in item.data"
            :key="findex"
            @click="getAdminList(f, item, ['=', item.key, f[item.key]])"
          >
            <view
              class="grid-text"
              :class="{ 'grid-text-active': f.checked }"
              >{{ f[item.label] }}</view
            >
          </u-grid-item>
        </u-grid>
      </view>
    </view>
    <u-button
      class="change-btn"
      text="确定"
      type="primary"
      @click="adminChange"
      :color="$constants.COLOR.PRIMARY_COLOR"
    ></u-button>
  </view>
</template>

<script>
import { ROLE_ADMIN, PAGE_PARAM, ADMIN_LIST } from "./config";

import formDataApi from "@/apis/formData";

export default {
  data() {
    return {
      userInfo: null,
      page: PAGE_PARAM,
      filter: null,
      adminList: ADMIN_LIST,
    };
  },
  mounted() {
    this.getUserInfo();
    this.getAdminList();
  },

  computed: {
    manageCode() {
      const role = this.userInfo.roles.filter((item) =>
        Object.keys(ROLE_ADMIN).includes(item.code)
      );
      if (Array.isArray(role) && role.length > 0) {
        return role[0].code;
      }
      return null;
    },
    adminCodeInfo() {
      const list = JSON.parse(JSON.stringify(this.adminList));
      const obj = {};
      list.forEach((item) => {
        item.data = item.data.filter((f) => f.checked);
        if (Array.isArray(item.data) && item.data.length > 0) {
          obj[item.key] = item.data[0][item.key];
          obj[item.label] = item.data[0][item.label];
          obj.level = item.level;
        }
      });
      return obj;
    },
  },
  methods: {
    getUserInfo() {
      let userInfo = JSON.parse(uni.getStorageSync("userInfo"));
      userInfo.pcode = "450000";
      userInfo.pname = "广西壮族自治区";
      this.userInfo = userInfo;
    },
    async getAdminList(f = {}, item = {}, filter) {
      const { cFormUid, data } = item;
      if (Object.keys(f).length > 0 && Object.keys(item).length > 0) {
        data.forEach((k) => (k.checked = false));
        f.checked = true;
      }
      if (cFormUid === "") return;
      const formUid = cFormUid || ROLE_ADMIN[this.manageCode].formUid;
      const params = {
        filter: filter || [
          "=",
          ROLE_ADMIN[this.manageCode].code,
          this.userInfo[ROLE_ADMIN[this.manageCode].code] || "450000",
        ],
        page: this.page,
      };
      try {
        let res = await formDataApi.getFormRecords(formUid, params);
        let current = 0;
        for (let [index, item] of this.adminList.entries()) {
          if (item.formUid === formUid) {
            item.data = res.list.map((item) =>
              Object.assign({ checked: false }, item)
            );
          }
          if (item.cFormUid === formUid) {
            current = index;
          }
          if (index > current + 1) {
            item.data = [];
          }
          if (item.label === ROLE_ADMIN[this.manageCode].name) {
            const keys = [
              ROLE_ADMIN[this.manageCode].name,
              ROLE_ADMIN[this.manageCode].code,
            ];
            let obj = {};
            keys.forEach((f) => (obj[f] = this.userInfo[f]));
            item.data = [Object.assign({ checked: true }, obj)];
          }
        }
      } catch (e) {
        console.log(e);
      }
    },

    adminChange() {
      this.$emit("adminChange", this.adminCodeInfo);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";
.popup-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .change-btn {
    margin: 30rpx;
    width: calc(100% - 60rpx);
  }
}
.admin-wrapper {
  flex: 1;
  overflow: scroll;
  display: flex;
  flex-direction: column;
  margin-top: 75rpx;
  .admin-type-title {
    margin: 30rpx;
    color: $gt-primary-color;
  }

  .u-grid {
    margin: 20rpx 0;
  }

  .grid-text {
    display: flex;
    justify-content: center;
    border: 1rpx rgba(0, 0, 0, 0.15) solid;
    border-radius: 10rpx;
    padding: 10rpx;
    color: $gt-light-color;
    margin: 10rpx;
    width: calc(100% - 60rpx);
    font-size: 28rpx;
  }

  .grid-text-active {
    background: $gt-primary-color;
    color: #fff;
  }
}
</style>
