<template>
  <view class="wrapper">
    <div class="wrap-content">
      <view class="wrap-title"> 原生境保护点 </view>
      <view class="card" v-for="item in dataList" @click="itemClick(item)">
        <div class="content">
          <view class="head">
            <image
              class="icon"
              src="@/static/images/yszw/point_.png"
              mode="widthFix"
            />
            <view class="title">{{ item["名称"] }}</view></view
          >
          <view class="cont">
            <view class="info">
              <view class="label">目标物种</view>
              <view class="val">{{ item["目标物种数"] }}种</view>
            </view>
          </view>
          <view class="right">
            <u-icon
              class="arrow"
              name="play-right-fill"
              color="#00a88a"
            ></u-icon>
          </view>
        </div>
      </view>
    </div>
  </view>
</template>

<script>
import formDataApi from "@/apis/formData";

import { constants } from "@/pages_yszw/constants";

const FORM_UID = "野生植物原生境保护点表";
export default {
  data() {
    return {
      dataList: [],
    };
  },
  props: {
    admin: Object,
  },
  computed: {},
  watch: {
    admin: {
      handler(e) {
        if (e) {
          this.getDataList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async getDataList() {
      let filter = null;
      if (this.admin.fcode) {
        filter = ["=", "县编码", this.admin.fcode];
      } else if (this.admin.ccode) {
        filter = ["=", "市编码", this.admin.ccode];
      } else if (this.admin.pcode) {
        filter = ["=", "省编码", this.admin.pcode];
      }
      let res = await formDataApi.getFormRecords(FORM_UID, {
        filter,
      });
      this.dataList = res.list.map((item) => {
        item.目标物种数 = item.目标物种.split("、").length;
        return item;
      });
      console.log(res);
    },
    itemClick(e) {
      console.log(e);
      uni.navigateTo({
        url: `${constants.PAGE.PLANTS_PROGRESS_POINT_DETAIL}?point_id=${e._id}&point_name=${e.名称}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";
.wrapper {
  width: calc(100% - 60rpx);
  margin: 20rpx;
  background-color: #fff;
  padding: 8rpx;
  border: solid 1px #eee;
  box-shadow: 0 0 3px 2px #eee;
  border-radius: 20rpx;
  .wrap-content {
    padding: 20rpx;
    box-sizing: border-box;
    background: linear-gradient(
      180deg,
      rgba(0, 168, 139, 0.08) 0%,
      rgba(0, 168, 139, 0) 300rpx
    );
  }
  .wrap-title {
    font-size: 32rpx;
    font-weight: 550;
    color: #00a88b;
  }
  .card {
    background-color: #fff;
    margin-top: 30rpx;
    padding: 8rpx;
    border: solid 1px #eee;
    box-shadow: 0 0 3px 2px #eee;
    border-radius: 20rpx;
    .content {
      padding: 20rpx;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        rgba(0, 168, 139, 0.08) 0%,
        rgba(0, 168, 139, 0) 100%
      );
      position: relative;
      .head {
        display: flex;
        align-items: center;
        .icon {
          width: 35rpx;
          margin-right: 20rpx;
        }
      }
      .title {
        color: $gt-primary-color;
        font-size: 30rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
      }
      .cont {
        margin-top: 15rpx;
        .info {
          display: flex;
          .label {
            color: #666;
            margin-right: 10rpx;
          }
          .val {
            font-weight: 400;
            margin-right: 30rpx;
            color: #333;
          }
        }
      }
      .right {
        position: absolute;
        right: 30rpx;
        top: calc(50% - 8px);
        height: 100%;
      }
    }
  }
}
</style>
