<template>
  <view class="wrapper">
    <view v-if="!data || data.length === 0">
      <u-empty text="没有数据" icon="/static/images/empty.png"></u-empty>
    </view>
    <view class="data-item" v-for="item in data">
      <view class="item-cont">
        <view class="title">{{ item["保护点名称"] }}</view>

        <view class="cont">
          <view class="flex">
            <view v-for="col in defaultColumn" class="info">
              <view class="label">{{ col.label }}: </view>
              <view class="val"> {{ item[col.dataIndex] }} </view>
            </view>
          </view>

          <view :class="['info', item['审核状态'] == '已退回' ? 'active' : '']">
            <view class="label">状态</view>
            <view class="val">{{ item["审核状态"] }} </view>
          </view>
          <view class="reason" v-if="item['审核状态'] == '已退回'">
            <view class="label">退回原因</view>
            <view class="val bg">{{ item["退回原因"] }} </view>
          </view>
        </view>
        <view class="operate">
          <!-- <view class="btn">
            <u-button type="primary" @click="$emit('view', item)">
              查看
            </u-button>
          </view> -->
          <view class="btn">
            <u-button
              type="info"
              @click="$emit('view', item)"
              shape="circle"
              icon="file-text"
              plain
            >
              查看
            </u-button>
          </view>
          <view class="btn" v-if="showBtn(item)">
            <u-button
              type="primary"
              @click="$emit('edit', item)"
              shape="circle"
              icon="edit-pen"
              plain
              :color="$constants.COLOR.PRIMARY_COLOR"
            >
              编辑
            </u-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import color from "uview-ui/libs/config/color";

export default {
  data() {
    return {
      defaultColumn: [
        {
          label: "年份",
          dataIndex: "年份",
        },
        {
          label: "轮次",
          dataIndex: "轮次",
        },
        // {
        //   label: "状态",
        //   dataIndex: "审核状态",
        // },
      ],
      userInfo: JSON.parse(uni.getStorageSync("userInfo")),
    };
  },
  props: {
    data: {
      type: Array,
    },
  },
  methods: {
    showBtn(item) {
      return (
        item["create_by"] == this.userInfo._id &&
        (item["审核状态"] == "填报中" ||
          item["审核状态"] == "未审核" ||
          item["审核状态"] == "已退回")
      );
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";
.wrapper {
  padding: 20rpx 0 200rpx;
  .data-item {
    padding: 8rpx;
    margin: 0 auto 30rpx;
    border-radius: 20rpx;
    border: solid 1px #eee;
    box-shadow: 0 0 3px 2px #eee;
    width: 690rpx;
    max-height: 400rpx;
    background: #fff;
    display: flex;
    flex-direction: column;
    .item-cont {
      padding: 20rpx 0 0;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        rgba(0, 168, 139, 0.08) 0%,
        rgba(0, 168, 139, 0) 100%
      );
      border-radius: 16rpx;
      border: 8rpx solid #ffffff;
      .title {
        padding: 0 20rpx;
        color: $gt-primary-color;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
      }
      .cont {
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        padding: 20rpx 0 0;
        font-size: 28rpx;
        .flex {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .info {
          padding: 0 20rpx;
          margin: 10rpx 0;
          display: flex;
          flex: 1;
          font-size: 28rpx;
          .label {
            margin-right: 10rpx;
            color: #666;
          }
          .val {
            font-weight: 400;
            margin-right: 30rpx;
            color: #333;
          }
        }
        .active {
          .val {
            color: #d0021b;
          }
        }
        .reason {
          margin: 10rpx 0;
          .label {
            margin: 10rpx 0;
            padding: 0 20rpx;
            color: #666;
          }
        }
        .bg {
          width: calc(100% - 60rpx);

          background-color: #f5f5f9;
          padding: 15rpx;
          margin: 10rpx auto 20rpx;
          border-radius: 5rpx;
          color: #061735;
        }
      }
      .operate {
        display: flex;
        justify-content: end;
        .btn {
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 50rpx;
          margin-left: 30rpx;
          height: 64rpx;
          .u-button {
            width: 160rpx;
            height: 64rpx;
          }
        }
      }
    }
  }
}
</style>
