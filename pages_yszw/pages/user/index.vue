<template>
  <view class="user-wrapper">
    <view class="head-wrapper">
      <u-image
        class="user-img-wrapper"
        src="@/static/images/nickpic.png"
        :width="'112rpx'"
        :height="'112rpx'"
        mode="aspectFit"
      ></u-image>
      <view class="user-roles-wrapper">
        <view class="user-roles-name">{{ getValue({ key: "username" }) }}</view>
        <view class="user-roles-code">{{ getValue({ key: "roles" }) }}</view>
      </view>
    </view>
    <view class="body-wrapper">
      <view
        class="user-info-item"
        v-if="getValue(item)"
        v-for="item in userList"
        :key="item.key"
      >
        <view class="user-info-item-label">
          <u-icon
            :name="item.icon"
            :size="'40rpx'"
            :color="item.color"
          ></u-icon>
          <text>{{ item.title }}</text>
        </view>
        <text>{{ getValue(item) }}</text>
      </view>

      <view class="operations-wrapper">
        <view class="operations-item" @click="changePassWord">
          <view class="operations-item-label">
            <u-icon name="lock" :size="'40rpx'" color="#ff9301"></u-icon>
            <text>修改密码</text>
          </view>
          <u-icon name="arrow-right" :size="'40rpx'" color="#ccc"></u-icon>
        </view>
        <view class="operations-item" @click="report">
          <view class="operations-item-label">
            <u-icon name="email" :size="'40rpx'" color="#ff3030"></u-icon>
            <text>投诉举报</text>
          </view>
          <u-icon name="arrow-right" :size="'40rpx'" color="#ccc"></u-icon>
        </view>
        <view class="operations-item" @click="checkVersion">
          <view class="operations-item-label">
            <u-icon
              name="level"
              width="'24rpx'"
              height="'40rpx'"
              color="#01b6f2"
            ></u-icon>
            <text> {{ `版本 ${versionInfo.version}` }}</text>
          </view>
          <view class="check-wrapper">
            <u-text
              class="check-title"
              :size="'28rpx'"
              color="#1cbcf4"
              text="检查更新"
            ></u-text>
            <u-icon name="arrow-right" :size="'40rpx'" color="#ccc"></u-icon>
          </view>
        </view>
      </view>
      <view class="logout">
        <u-button
          text="退出登录"
          type="primary"
          @click="outLogin"
          :color="$constants.COLOR.PRIMARY_COLOR"
        ></u-button>
      </view>
    </view>
    <Tabbar :config="tabbar" :value="1"></Tabbar>
  </view>
</template>

<script>
import Tabbar from "@/components/common/Tabbar";
import { tabbar } from "@/pages_yszw/config";
import BasePage from "@/pages/BasePage";

import { constants } from "@/pages_yszw/constants";
import versionApi from "@/apis/version";

const VERSION_TEST = "DEV.0.1";
export default {
  mixins: [BasePage],
  components: { Tabbar },
  data() {
    return {
      tabbar,
      userInfo: null,
      userList: [
        {
          key: "phone",
          title: "电话",
          icon: "phone",
          color: "#02B5F2",
        },
        {
          key: "address",
          title: "地区",
          icon: "map",
          color: "#4B96F3",
        },
      ],
      versionInfo: {
        version: null,
      },
    };
  },
  onLoad() {
    uni.hideTabBar();
  },
  created() {
    let version = "";
    try {
      version = plus.runtime.version;
    } catch (e) {
      version = VERSION_TEST;
    }
    this.versionInfo.version = version;

    const userInfo = JSON.parse(uni.getStorageSync("userInfo"));
    this.userInfo = userInfo;
  },

  methods: {
    getValue(item) {
      const { key } = item;
      if (!key) return;
      switch (key) {
        case "roles": {
          return this.getRoles(this.userInfo[item.key]);
          break;
        }
        case "address": {
          return this.getAddress();
          break;
        }
        default: {
          return this.userInfo[item.key];
          break;
        }
      }
    },
    outLogin() {
      uni.showToast({
        title: "退出成功",
        complete() {
          uni.clearStorageSync();
          uni.$u.route({
            type: "redirectTo",
            url: `/pages/Login/index`,
          });
        },
      });
    },
    getRoles(roles) {
      let str = "";
      roles.forEach((i) => {
        str += "、" + i.name;
      });
      str = str.replace("、", "");
      return str;
    },
    getAddress() {
      const addressArr = ["pname", "cname", "fname"];
      let address = "";
      addressArr.forEach((item) => {
        if (this.userInfo[item]) {
          address = address.concat(this.userInfo[item]);
        }
      });
      return address;
    },
    async checkVersion() {
      this.showLoading(this.$constants.VERSION.CHECK);
      try {
        const result = await versionApi.checkVersion();

        uni.hideLoading();
        console.log(
          !Object.values(result).length > 0,
          Object.values(result).filter((f) => [null, undefined, ""].includes(f))
            .length > 0
        );
        if (
          !Object.values(result).length > 0 ||
          Object.values(result).filter((f) => [null, undefined, ""].includes(f))
            .length > 0
        ) {
          this.showError(this.$constants.VERSION.FAILED);

          return;
        }
        const { upgrade, version, url } = result;
        if (upgrade === true) {
          uni.showModal({
            title: "版本更新",
            content: this.$constants.VERSION.NEW_VERSION,
            success: function (res) {
              if (res.confirm) {
                plus.runtime.openURL(url);
              }
            },
          });
        } else {
          this.showInfo(this.$constants.VERSION.LATEST_ALREADY);
        }
      } catch (error) {
        uni.hideLoading();
      }
    },

    changePassWord() {
      this.navigateTo(constants.PAGE.CHANGE_PSW_PAGE);
    },

    report() {
      const pageUrl = `${constants.PAGE.FORM_PAGE_URL}?formUid=投诉举报表&cacheable=false`;
      this.navigateTo(pageUrl);
    },

    showLoading(msg) {
      uni.showLoading({
        title: msg,
        mask: true,
      });
    },
    showError(msg) {
      uni.showToast({
        title: msg,
        icon: "error",
        duration: 2000,
      });
    },

    showInfo(msg) {
      uni.showToast({
        title: msg,
        icon: "info",
        duration: 2000,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";

.user-wrapper {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f9;

  .head-wrapper {
    box-sizing: border-box;
    position: fixed;
    width: 100%;
    background: url("@/static/images/userBackground.png") no-repeat;
    background-position: center;
    background-size: 100% 100%;
    display: flex;
    height: 450rpx;
    align-items: center;

    .user-img-wrapper {
      margin-left: 50rpx;
    }

    .user-roles-wrapper {
      margin-left: 35rpx;
      display: flex;
      flex-direction: column;

      .user-roles-name {
        font-size: 36rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 50rpx;
      }

      .user-roles-code {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 40rpx;
      }
    }
  }

  .body-wrapper {
    position: absolute;
    top: 332rpx;
    margin: 0 30rpx 20rpx 30rpx;
    width: calc(100% - 60rpx);

    .user-info-item {
      box-sizing: border-box;
      padding: 40rpx 65rpx 40rpx 40rpx;
      height: 120rpx;
      background: #fff;
      display: flex;
      justify-content: space-between;

      text {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
      }

      .user-info-item-label {
        display: flex;

        text {
          margin-left: 26rpx;
        }
      }
    }

    // 伪类选择器的使用问题？？？
    .user-info-item:nth-of-type(n) {
      border-bottom-left-radius: 16rpx;
      border-bottom-right-radius: 16rpx;
    }

    .user-info-item:nth-of-type(1) {
      // border-top-left-radius: 16rpx;
      // border-top-right-radius: 16rpx;
      border-radius: 16rpx 16rpx 0 0;
    }

    .operations-wrapper {
      margin-top: 20rpx;

      .operations-item {
        box-sizing: border-box;
        padding: 40rpx;
        height: 120rpx;
        background: #fff;
        display: flex;
        justify-content: space-between;

        text {
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
        }

        .operations-item-label {
          display: flex;

          text {
            margin-left: 26rpx;
          }
        }

        .check-wrapper {
          display: flex;

          .check-title {
            cursor: pointer;
            margin-right: 24rpx !important;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
          }
        }
      }

      .operations-item:nth-of-type(1) {
        border-top-left-radius: 16rpx;
        border-top-right-radius: 16rpx;
      }

      .operations-item:nth-last-of-type(1) {
        border-bottom-left-radius: 16rpx;
        border-bottom-right-radius: 16rpx;
      }
    }
  }
}

/deep/.logout {
  padding: 50rpx;
  margin-top: 100rpx;

  span {
    // color: #00a88a;
    color: #fff;
  }
}
</style>
