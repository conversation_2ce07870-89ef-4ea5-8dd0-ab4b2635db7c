<template>
  <view>
    <u-navbar
      title="数据填报"
      @leftClick="leftClickHandler"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
    ></u-navbar>
    <view class="content">
      <GTForm
        ref="gtForm"
        v-if="options"
        :options="options"
        @initialized="initHandler"
        @submit="submitHandler"
      ></GTForm>
    </view>
    <view class="btn-list" v-if="!readonly">
      <u-button
        text="暂 存"
        type="info"
        v-if="cacheable"
        @click="saveLocalHandler"
      >
      </u-button>
      <u-button
        text="立即提交"
        type="primary"
        @click="commitHandler"
        throttleTime="1000"
        :color="$constants.COLOR.PRIMARY_COLOR"
      >
      </u-button>
    </view>
    <u-modal
      :show="modalVisible"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_SAVE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="modalCancelHandler"
      @confirm="modalConfirmHandler"
    ></u-modal>
  </view>
</template>

<script>
import BasePage from "@/pages/BasePage";
import GTForm from "@/components/form/GTForm";
import Storage from "@/tools/Storage";
import ChangeDetector from "@/tools/ChangeDetector";

import formDefApi from "@/apis/formDef";
import formDataLocalApi from "@/apis/formDataLocal";
import formDataApi from "@/apis/formData";

import formUtil from "@/utils/form";

export default {
  mixins: [BasePage],

  components: {
    GTForm,
  },

  data() {
    return {
      options: undefined,
      formUid: undefined,
      formDef: undefined,
      formRecordData: undefined,
      cacheRecordId: undefined,
      cacheable: true,
      changeDetector: undefined,
      modalVisible: false,
      readonly: false,
      customFields: {},
    };
  },

  onLoad(option) {
    this.formUid = option.formUid;
    this.formRecordUid = option.formRecordUid;
    this.cacheRecordId = option.cacheRecordId;
    this.cacheable = option.cacheable !== "false";
    this.readonly = option.readonly;
    //  定制化数据处理
    let pick = ["formUid", "formRecordUid", "readonly", "cacheable"];
    this.customFields = JSON.parse(JSON.stringify(option));
    Object.keys(this.customFields).forEach((item) => {
      if (pick.includes(item)) {
        delete this.customFields[item];
      }
    });

    this.initForm();
  },

  // 响应手机物理或者虚拟返回按键
  onBackPress(e) {
    if (e.from === "backbutton") {
      this.leftClickHandler();
      return true;
    }
  },

  computed: {
    formRecordUid() {
      return this.$route.query.formRecordUid;
    },
  },

  methods: {
    async initForm() {
      let options = {};
      if (this.formUid) {
        // 获取表单定义
        options.formUid = this.formUid;
        options.formDef = await formDefApi.getFormDef(this.formUid);
      }
      if (this.formRecordUid) {
        // 获取表单数据
        options.formRecordUid = this.formRecordUid;
        const result = Storage.getFormData(this.formRecordUid);
        options.formRecordData = formUtil.toFrontFormData(result);
      }
      if (this.cacheRecordId) {
        options.formRecordData = Storage.getFormData(this.cacheRecordId);
      }
      //  只读
      if (this.readonly) {
        options.readonly = true;
      }
      // 定制化数据处理
      if (this.customFields && Object.keys(this.customFields).length > 0) {
        options.formRecordData = Object.assign(
          this.customFields,
          options.formRecordData
        );
      }
      this.options = options;
    },

    initHandler(formData) {
      this.changeDetector = new ChangeDetector(formData);
    },

    // 返回到上一页
    leftClickHandler() {
      const formData = this.$refs.gtForm.getFormData();
      const change = this.changeDetector.detect(formData);
      // if (change) {
      //   this.modalVisible = true;
      // } else {
      this.navigateBack();
      // }
    },

    async saveLocalHandler() {
      const formData = this.$refs.gtForm.getFormData();
      await this.saveLocal(formData);
    },

    async saveLocal(formData) {
      if (!formData) return;
      const cacheRecordId = formDataLocalApi.upsertCacheRecord(
        this.formUid,
        this.cacheRecordId,
        formData
      );
      if (cacheRecordId) {
        this.cacheRecordId = cacheRecordId;
        this.showSuccess(this.$constants.MSG.DATA_CACHED_SUCCESS);
        // 重置变化检测
        this.changeDetector.setSource(formData);
      } else {
        this.showError(this.$constants.MSG.DATA_CACHED_FAIL);
      }
    },

    commitHandler() {
      this.$refs.gtForm.submit();
    },

    async submitHandler(data) {
      if (data["年份"]) data["年份"] = Number(data["年份"]);
      await this.submitData(data);
    },

    // 本地存储
    async saveLocal(formData) {
      if (!formData) return;
      const cacheRecordId = await formDataLocalApi.upsertCacheRecord(
        this.formUid,
        this.cacheRecordId,
        formData
      );
      if (cacheRecordId) {
        this.cacheRecordId = cacheRecordId;
        this.showSuccess(this.$constants.MSG.DATA_CACHED_SUCCESS);
        // 重置变化检测
        this.changeDetector.setSource(formData);
      } else {
        this.showError(this.$constants.MSG.DATA_CACHED_FAIL);
      }
    },

    // 向服务器端提交数据
    async submitData(data) {
      if (!data) return;
      this.showLoading(this.$constants.MSG.DATA_SUBMITTING);
      const formData = formUtil.toApiFormData(data);
      if (formData._id) {
        await this.updateFormData(formData);
      } else {
        await this.addFormData(formData);
      }
    },

    // 更新表单数据
    async updateFormData(formData) {
      try {
        const result = await formDataApi.updateFormRecord(
          this.formUid,
          formData._id,
          formData,
          this.cacheRecordId
        );
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS);
        this.navigateBack();
      } catch (err) {
        // this.showError(this.$constants.MSG.DATA_SUBMIT_FAIL);
        uni.$u.toast(err);
      } finally {
        uni.hideLoading();
      }
    },

    // 增加表单数据
    async addFormData(formData) {
      try {
        const result = await formDataApi.addFormRecord(
          this.formUid,
          formData,
          this.cacheRecordId
        );
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS);
        this.navigateBack();
      } catch (err) {
        // this.showError(this.$constants.MSG.DATA_SUBMIT_FAIL);
        uni.$u.toast(err);
      } finally {
        uni.hideLoading();
      }
    },

    modalCancelHandler() {
      this.modalVisible = false;
    },

    modalConfirmHandler() {
      this.navigateBack();
      this.modalVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";

.content {
  padding: 0 30rpx 140rpx;
}

/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
  }
}

.btn-list {
  box-sizing: border-box;
  z-index: 999;
  display: flex;
  columns: 2;
  gap: 24rpx;
  padding: 24rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
}
</style>
