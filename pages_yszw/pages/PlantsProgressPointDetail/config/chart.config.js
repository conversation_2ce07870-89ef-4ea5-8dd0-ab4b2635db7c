export const TABLE_COLUMNS = [
  {
    label: "监测任务",
    dataIndex: "监测任务",
    width: "230rpx",
  },
  {
    label: "目标物种(种)",
    dataIndex: "目标物种数",
    width: "180rpx",
  },
  {
    label: "分布面积(ha)",
    dataIndex: "分布面积",
    width: "180rpx",
  },
  { type: "operate", label: "", operate: "查看" },
];

export const LINE_OPTS = {
  padding: [0, 10, 30, 15],
  enableScroll: true,
  touchMoveLimit: 24,
  dataLabel: false,
  legend: {
    position: "top",
    margin: 10,
  },
  xAxis: {
    scrollShow: true,
    disableGrid: true,
    fontSize: 10,
    itemCount: 4,
  },
  yAxis: {
    gridType: "dash",
    dashLength: 2,
  },
  extra: {
    line: {
      type: "curve",
      width: 2,
      activeType: "hollow",
    },
  },
};

export const COL_OPTS = {
  padding: [35, 15, 0, 5],
  enableScroll: true,
  touchMoveLimit: 24,
  dataLabel: false,
  legend: {},
  xAxis: {
    scrollShow: true,
    disableGrid: true,
    fontSize: 10,
    itemCount: 10,
  },
  yAxis: {
    data: [
      {
        min: 0,
      },
    ],
  },
  extra: {
    column: {
      type: "stack",
      width: 30,
      activeBgColor: "#000000",
      activeBgOpacity: 0.08,
      labelPosition: "center",
    },
  },
};
