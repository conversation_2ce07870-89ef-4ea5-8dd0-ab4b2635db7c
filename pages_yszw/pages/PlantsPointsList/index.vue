<template>
  <view>
    <u-navbar
      title="原生境保护点列表"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      autoBack
    ></u-navbar>
    <view class="content">
      <view
        v-for="item in pointList"
        class="point-item"
        @click="itemClickHandle(item)"
      >
        <view class="point-cont">
          <view class="head">
            <image
              class="icon"
              src="@/static/images/yszw/point_.png"
              mode="widthFix"
            />
            <view class="title"> {{ item["名称"] }}</view>
          </view>
          <view class="cont">
            <view class="info">
              <view class="label">所在地:</view>
              <view class="val">{{ item["所在地"] }}</view>
              <!-- <view class="label">县:</view>
              <view class="val">{{ item["县名称"] }}</view> -->
            </view>
          </view>
          <view class="arrow">
            <u-icon name="play-right-fill" color="#00a88a"></u-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const FORMUID = "野生植物原生境保护点表";
const NEXT_FORMUID = "野生植物原生境保护点填报表";
import Storage from "@/tools/Storage";
import { constants } from "@/pages_yszw/constants";
import formDataApi from "@/apis/formData";

export default {
  data() {
    return {
      pointList: [],
    };
  },
  async mounted() {
    let userInfo = JSON.parse(uni.getStorageSync("userInfo"));
    this.userInfo = userInfo;
    await this.getPointList();
  },

  methods: {
    async getPointList() {
      let filter = ["and", ["=", "状态", "启用"], ["=", "省编码", "450000"]];
      if (this.userInfo.fcode) {
        filter.push(["=", "县编码", this.userInfo.fcode]);
      }
      let res = await formDataApi.getFormRecords(FORMUID, {
        filter,
      });
      this.pointList = res.list;
      console.log(this.pointList);
    },
    itemClickHandle(e) {
      Storage.saveFormData(e["_id"], e);
      uni.navigateTo({
        url: `${constants.PAGE.PLANTS_POINTS_FORM_LIST}?pointId=${e["_id"]}`,
      });
      console.log(e);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";
/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
}
.content {
  padding: 30rpx 0 100rpx;
  .point-item {
    padding: 8rpx;
    margin: 0 auto 30rpx;
    border-radius: 20rpx;
    border: solid 1px #eee;
    box-shadow: 0 0 3px 2px #eee;
    width: 690rpx;
    max-height: 400rpx;
    background: #fff;
    display: flex;
    flex-direction: column;
    .point-cont {
      padding: 20rpx;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        rgba(0, 168, 139, 0.08) 0%,
        rgba(0, 168, 139, 0) 100%
      );
      border-radius: 16rpx;
      border: 8rpx solid #ffffff;
      position: relative;
      .head {
        display: flex;
        align-items: center;
        .icon {
          width: 35rpx;
          margin-right: 20rpx;
        }
      }
      .title {
        color: $gt-primary-color;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
      }
      .cont {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0 0;
        .info {
          display: flex;
          flex: 1;
          .label {
            color: #666;
          }
          .val {
            font-weight: 400;
            margin-right: 30rpx;
            color: #333;
          }
        }
      }
      .arrow {
        position: absolute;
        right: 30rpx;
        top: calc(50% - 8px);
      }
    }
  }
}
</style>
