// .eslintrc.js
module.exports = {
  root: true,
  env: {
    node: true,
    es2021: true,
  },
  extends: ["eslint:recommended", "eslint-config-ali/vue", "eslint-config-prettier"],
  overrides: [],
  parser: "vue-eslint-parser",
  parserOptions: {
    parser: "@babel/eslint-parser",
    // 指定支持es6的语法
    ecmaVersion: 6,
    // 默认是script，但我们现在都是模块，所以将它指定为module
    sourceType: "module",
    ecmaFeatures: {
      // 启用jsx语法，如果不打开，当我们写jsx语法的时候，eslint就会校验不通过，因为它会认为<不是一个合法的token
      jsx: true,
    },
  },
  plugins: ["vue"],
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    // 字符串默认使用prettier的双信号
    quotes: "off",
    // 不对模块解析路径进行检验（webpack有缩写路径）
    "import/no-unresolved": "off",
  },
  globals: {
    uni: true,
    wx: true,
  },
};
