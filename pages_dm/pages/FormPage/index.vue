<template>
  <view>
    <u-navbar
      title="数据填报"
      @leftClick="leftClickHandler"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      @rightClick="navRightClick"
    >
      <view v-if="options && options.formDef" class="u-nav-slot" slot="right">
        <template v-if="options.formDef.help">
          <u-icon name="question-circle" size="20"></u-icon>
          <text>填报说明</text>
        </template>
      </view>
    </u-navbar>
    <view class="content">
      <GTForm
        ref="gtForm"
        v-if="options"
        :options="options"
        @initialized="initHandler"
        @submit="submitHandler"
      ></GTForm>
    </view>
    <view class="btn-list">
      <u-button
        text="暂 存"
        type="info"
        v-if="cacheable"
        @click="saveLocalHandler"
      >
      </u-button>
      <u-button
        text="立即提交"
        type="primary"
        @click="commitHandler"
        throttleTime="1000"
        :color="$constants.COLOR.PRIMARY_COLOR"
      >
      </u-button>
    </view>
    <u-modal
      :show="modalVisible"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_SAVE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="modalCancelHandler"
      @confirm="modalConfirmHandler"
    ></u-modal>

    <u-modal
      :show="helpVisible"
      title="填报说明"
      confirm-text="我知道了"
      :confirm-color="$constants.COLOR.PRIMARY_COLOR"
      @confirm="helpVisible = false"
    >
      <scroll-view
        v-if="options && options.formDef"
        scroll-y
        style="height: 50vh"
      >
        <rich-text :nodes="options.formDef.help"></rich-text>
      </scroll-view>
    </u-modal>
  </view>
</template>

<script>
import BasePage from "@/pages/BasePage";
import GTForm from "@/components/form/GTForm";
import Storage from "@/tools/Storage";
import ChangeDetector from "@/tools/ChangeDetector";

import formUtils from "@/utils/form.js";
import { constants } from "@/pages_dm/constants";
import formDefApi from "@/apis/formDef";
import formDataApi from "@/apis/formData";
import formDataLocalApi from "@/apis/formDataLocal";

export default {
  mixins: [BasePage],

  components: {
    GTForm,
  },

  data() {
    return {
      options: undefined,
      formUid: undefined,
      formDef: undefined,
      formRecordData: undefined,
      cacheRecordId: undefined,
      cacheable: true,
      changeDetector: undefined,
      modalVisible: false,
      customFields: {},
      helpVisible: false,
    };
  },

  onLoad(option) {
    console.log("页面参数", option);
    this.formUid = option.formUid;
    this.formRecordUid = option.formRecordUid;
    this.cacheRecordId = option.cacheRecordId;
    this.cacheable = option.cacheable !== "false";

    let pick = ["formUid", "cacheable", "formPageUrl"];

    if (option.data) {
      this.customFields = JSON.parse(option.data);
    } else {
      this.customFields = JSON.parse(JSON.stringify(option));
    }

    Object.keys(this.customFields).forEach((item) => {
      if (pick.includes(item)) {
        delete this.customFields[item];
      }
    });
    const provinceInfo = { 省名称: "广西壮族自治区", 省编码: "450000" };
    this.customFields = Object.assign(this.customFields, provinceInfo);

    this.initForm();
  },

  methods: {
    navRightClick() {
      this.helpVisible = true;
    },
    async initForm() {
      try {
        console.log("初始化");
        let options = {};
        if (this.formUid) {
          // 获取表单定义
          options.formUid = this.formUid;
          options.formDef = await formDefApi.getFormDef(this.formUid);
        }
        if (this.formRecordUid) {
          // 获取表单数据
          options.formRecordUid = this.formRecordUid;
          const result = Storage.getFormData(this.formRecordUid);
          options.formRecordData = formUtils.toFrontFormData(result);
        }
        if (this.cacheRecordId) {
          let data = Storage.getFormData(this.cacheRecordId);
          // 获取缓存数据，因此要与表单校验
          const handle_data = await formUtils.checkFormData(
            data,
            options.formDef
          );
          options.formRecordData = formUtils.toFrontFormData(handle_data);
        }
        if (this.customFields && Object.keys(this.customFields).length > 0) {
          options.formRecordData = Object.assign(
            this.customFields,
            options.formRecordData
          );
        }
        //
        this.options = options;
      } catch (err) {
        console.log(err);
      }
    },

    initHandler(formData) {
      this.changeDetector = new ChangeDetector(formData);
    },

    // 返回到上一页
    leftClickHandler() {
      const formData = this.$refs.gtForm.getFormData();
      const change = this.changeDetector.detect(formData);
      if (change) {
        this.modalVisible = true;
      } else {
        this.navigateBack();
      }
    },

    async saveLocalHandler() {
      try {
        const formData = this.$refs.gtForm.getFormData();
        await this.saveLocal(formData);
        setTimeout(() => {
          this.navigateBack();
        }, 1500);
      } catch (err) {
        uni.$u.toast(err || "暂存数据出错");
      }
    },

    commitHandler() {
      this.$refs.gtForm.submit();
    },

    async submitHandler(data) {
      const values = JSON.parse(JSON.stringify(data));
      if (this.formUid === "种植业典型地块抽样调查表") {
        const cropArr = values["种植业典型地块抽样调查表_作物信息表"][
          "data"
        ].map((item) => {
          return `${item["种植季"]}_${item["作物名称"]}`;
        });
        const fertilizerArr = values["种植业典型地块抽样调查表_肥料施用情况表"][
          "data"
        ].map((item) => {
          return `${item["种植季"]}_${item["作物名称"]}`;
        });

        if (fertilizerArr.some((value) => !cropArr.includes(value))) {
          this.showError("种植季信息缺失");
          return;
        }
      }

      // 年份 地块经纬度 数字类型
      // ?? Object.assign 更新的顺序问题影响属性吗
      const genData = Object.assign(this.customFields, values);
      const numberFields = ["年份"];
      numberFields.forEach((item) => (genData[item] = Number(genData[item])));
      await this.submitData(genData);
    },

    // 本地存储
    async saveLocal(formData) {
      if (!formData) return;
      const cacheRecordId = await formDataLocalApi.upsertCacheRecord(
        this.formUid,
        this.cacheRecordId,
        formData
      );
      if (cacheRecordId) {
        this.cacheRecordId = cacheRecordId;
        this.showSuccess(this.$constants.MSG.DATA_CACHED_SUCCESS);
        // 重置变化检测
        this.changeDetector.setSource(formData);
      } else {
        this.showError(this.$constants.MSG.DATA_CACHED_FAIL);
      }
    },

    // 向服务器端提交数据
    async submitData(data) {
      if (!data) return;
      this.showLoading(this.$constants.MSG.DATA_SUBMITTING);

      const formData = formUtils.toApiFormData(data);

      if (data["首次覆膜年份"]) {
        formData["首次覆膜年份"] = Number(formData["首次覆膜年份"]);
      }
      if (formData._id) {
        await this.updateFormData(formData);
      } else {
        await this.addFormData(formData);
      }
      uni.hideLoading();
    },

    // 更新表单数据
    async updateFormData(formData) {
      try {
        const result = await formDataApi.updateFormRecord(
          this.formUid,
          formData._id,
          formData,
          this.cacheRecordId
        );
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS);

        // 更新表单状态
        if (formData["审核状态"] === "已退回") {
          await this.updateFormState(formData);
        }
        // this.navigateBackWithNoCheck();
        this.navigateBack();
      } catch (err) {
        if (err) {
          uni.$u.toast(err);
        } else {
          this.showError(this.$constants.MSG.DATA_SUBMIT_FAIL);
        }
      } finally {
        uni.hideLoading();
      }
    },

    // 增加表单数据
    async addFormData(formData) {
      try {
        const result = await formDataApi.addFormRecord(
          this.formUid,
          formData,
          this.cacheRecordId
        );
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS);

        // this.navigateBackWithNoCheck();
        this.navigateBack();
      } catch (err) {
        if (err === "超出限制") {
          this.showError("填报数量已上限");
        } else {
          if (err) {
            uni.$u.toast(err);
          } else {
            this.showError(this.$constants.MSG.DATA_SUBMIT_FAIL);
          }
        }
      } finally {
        uni.hideLoading();
      }
    },

    // 更新表单状态
    async updateFormState(data) {
      await formDataApi.updateFormRecordState(this.formUid, data._id, {
        field: "审核状态",
        state: "已提交",
        msg: "已提交",
        attrs: {
          退回原因: null,
        },
      });
    },

    modalCancelHandler() {
      this.modalVisible = false;
    },

    modalConfirmHandler() {
      // this.navigateBackWithNoCheck();
      this.navigateBack();
      this.modalVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_dm/styles/common.scss";

.u-nav-slot {
  display: flex;
  align-items: center;

  text {
    color: #fff;
    font-size: 24rpx;
    margin-left: 10rpx;
  }
}

.content {
  padding: 0 30rpx 140rpx;
}

/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
  }
}

.btn-list {
  box-sizing: border-box;
  z-index: 999;
  display: flex;
  columns: 2;
  gap: 24rpx;
  padding: 24rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
}
</style>
