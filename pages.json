{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
    }
  },
  "pages": [
    {
      "path": "pages/Workspace/index"
    },
    {
      "path": "pages/User/index"
    },
    {
      "path": "pages/FormList/index"
    },
    {
      "path": "pages/FormPage/index"
    },
    {
      "path": "pages/Login/index"
    },
    {
      "path": "pages/ViewPage/index"
    },
    {
      "path": "components/form/field/SubForm/SubFormPage/index"
    }
  ],
  "subPackages": [
    {
      "root": "pages_plowland",
      "pages": [
        {
          "path": "pages/index/index"
        },
        {
          "path": "pages/Verify/PlotInspect/index"
        },
        {
          "path": "pages/Verify/InspectRecord/index"
        },
        {
          "path": "pages/Verify/FillSchedule/index"
        },
        {
          "path": "pages/UpdatePage/index"
        },
        {
          "path": "pages/Verify/index"
        },
        {
          "path": "pages/Project/index"
        },
        {
          "path": "pages/ProjectList/index"
        },
        {
          "path": "pages/SelfFormPage/index"
        },
        {
          "path": "pages/Query/index"
        },
        {
          "path": "pages/user/index"
        }
      ]
    },
    {
      "root": "pages_farmland",
      "pages": [
        {
          "path": "pages/index/index"
        },
        {
          "path": "pages/workspaceMenu/workspaceMenu"
        },
        {
          "path": "pages/pointListPage/pointListPage"
        },
        {
          "path": "pages/pointMapPage/pointMapPage"
        },
        {
          "path": "pages/pointFillPage/pointFillPage"
        },
        {
          "path": "pages/user/index"
        }
      ]
    },
    {
      "root": "pages_dm",
      "pages": [
        {
          "path": "pages/index/index"
        },
        {
          "path": "pages/PointListPage/index"
        },
        {
          "path": "pages/MapFormListPage/index"
        },
        {
          "path": "pages/FormList/index"
        },
        {
          "path": "pages/FormPage/index"
        },
        {
          "path": "pages/ViewPage/index"
        },
        {
          "path": "pages/user/index"
        },
        {
          "path": "pages/ChangePswPage/index"
        }
      ]
    },
    {
      "root": "pages_dl",
      "pages": [
        {
          "path": "pages/index/index"
        },
        {
          "path": "pages/PointListPage/index"
        },
        {
          "path": "pages/MapFormListPage/index"
        },
        {
          "path": "pages/FormList/index"
        },
        {
          "path": "pages/FormPage/index"
        },
        {
          "path": "pages/ViewPage/index"
        },
        {
          "path": "pages/user/index"
        },
        {
          "path": "pages/ChangePswPage/index"
        }
      ]
    },
    {
      "root": "pages_yszw",
      "pages": [
        {
          "path": "pages/index/index"
        },

        {
          "path": "pages/FormList/index"
        },
        {
          "path": "pages/FormPage/index"
        },
        {
          "path": "pages/PlantsPointsList/index"
        },
        {
          "path": "pages/PlantsProgress/index"
        },
        {
          "path": "pages/PlantsAudit/index"
        },
        {
          "path": "pages/PlantsProgressPointDetail/index"
        },
        {
          "path": "pages/WildPlantPage/index"
        },
        {
          "path": "pages/WildPlantPage/taskList"
        },
        {
          "path": "pages/PlantsAuditTabs/index"
        },
        {
          "path": "pages/PlantsPointsFormList/index"
        },
        {
          "path": "pages/PlantsFormTypes/index"
        },
        {
          "path": "pages/PlantsFormPage/index"
        },
        {
          "path": "pages/user/index"
        },
        {
          "path": "pages/ChangePswPage/index"
        }
      ]
    }
  ],
  "tabBar": {
    "selectedColor": "#00C56E",
    "list": [
      {
        "selectedIconPath": "static/images/workspace-active.png",
        "iconPath": "static/images/workspace.png",
        "pagePath": "pages/Workspace/index",
        "text": "工作台"
      },
      {
        "selectedIconPath": "static/images/workspace-active.png",
        "iconPath": "static/images/workspace.png",
        "pagePath": "pages/User/index",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "navigationStyle": "custom",
    "navigationBarTextStyle": "white",
    "navigationBarBackgroundColor": "#ffffff",
    "backgroundColor": "#ffffff",
    "app-plus": {
      "bounce": "none",
      "scrollIndicator": "none" //全局 在APP页面都不显示滚动条
    }
  }
}
