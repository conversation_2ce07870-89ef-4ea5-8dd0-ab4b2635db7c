<template>
	<view class="gt-map-page">
		<u-navbar title="地图视图" @leftClick="leftClickHandler"
		:bgColor="getApp().$constants.COLOR.PRIMARY_COLOR"
		placeholder></u-navbar>
		<view class="map-wrapper">
			<map class="map" :longitude="lon" :latitude="lat" :scale="scale"></map>
			<view class="toolbar-wrapper">
				<u-button icon="plus" type="primary"></u-button>
				<u-button icon="minus" type="info"></u-button>
			</view>
		</view>
	</view>
</template>

<script>

	import BasePage from '@/pages/BasePage'
	
	const DEFAULT_CENTER_LON = 105
	const DEFAULT_CENTER_LAT = 39
	const DEFAULT_SCALE = 3

	export default {

		mixins: [ BasePage ],

		data() {
			return {
				lon: DEFAULT_CENTER_LON,
				lat: DEFAULT_CENTER_LAT,
				scale: DEFAULT_SCALE,
				formUid: undefined,
				isCache: false,
				lonField: undefined,
				latField: undefined,
				filter: undefined,
				callOutProperties: {},
				markers: []
			}
		},

		onShow(){
			
		},

		onLoad(option) {
			this.lon = option.lon || DEFAULT_CENTER_LON,
			this.lat = option.lat || DEFAULT_CENTER_LAT,
			this.scale = option.scale || DEFAULT_SCALE,
			this.formUid = option.formUid
			this.isCache = option.isCache || false
			this.lonField = option.lonField || 'lon'
			this.latField = option.latField || 'lat'
			this.callOutProperties = option.callOutProperties
			this.filter = option.filter
			this.markers = option.markers || []
		},

		methods: {

			leftClickHandler(){
				this.navigateBack()
			}

		}
	}
</script>

<style lang="scss" scoped>

@import '@/static/styles/common.scss';

.gt-map-page{
	display: flex;
	flex-direction: column;
	height: 100vh;

	/deep/ .u-navbar__content{
		.u-navbar__content__title, .u-icon__icon{
			color: $gt-navbar-title-color !important;
		}
	}

	.map-wrapper{
		width: 100%;
		height: 100%;
		.map{
			width: 100%;
			height: 100%;
		}
	}
	
	.toolbar-wrapper{
		position: absolute;
		right: 12px;
		bottom: 12px;
		z-index: 1000;
	}
}

</style>
