<template>
  <view class="wrap">
    <u-navbar
      title="数据列表"
      rightIcon="plus-circle"
      @leftClick="leftClickHandler"
      @rightClick="rightClickHandler"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
    ></u-navbar>
    <view class="content">
      <view class="head-subsection" v-if="cacheable">
        <u-subsection
          class="subsection"
          :list="sectionList"
          :current="currentSection"
          @change="sectionChagneHandler"
          :activeColor="$constants.COLOR.PRIMARY_COLOR"
          fontSize="15"
        ></u-subsection>
      </view>
      <FormDataList
        ref="formDataList"
        :formUid="formUid"
        v-show="currentSection === 0"
      />
      <CacheDataList
        ref="cacheDataList"
        :formUid="formUid"
        v-if="cacheable"
        v-show="currentSection === 1"
      />
    </view>
  </view>
</template>

<script>
import BasePage from "@/pages/BasePage";
import FormDataList from "@/components/common/FormDataList";
import CacheDataList from "@/components/common/CacheDataList";

export default {
  mixins: [BasePage],

  components: {
    FormDataList,
    CacheDataList,
  },

  data() {
    return {
      // 表单UID
      formUid: undefined,
      cacheable: true,
      sectionList: ["已提交", "暂存"],
      currentSection: 0,
    };
  },

  onShow() {
    if (this.$refs.cacheDataList) {
      this.$refs.cacheDataList.loadData();
    }
    if (this.$refs.formDataList) {
      this.$refs.formDataList.reloadData();
    }
  },

  onLoad(option) {
    this.formUid = option.formUid;
    this.cacheable = option.cacheable !== "false";
  },

  methods: {
    leftClickHandler() {
      this.navigateBack();
    },

    rightClickHandler() {
      const pageUrl = `${this.$constants.PAGE.FORM_PAGE_URL}?formUid=${this.formUid}`;
      this.navigateTo(pageUrl);
    },

    sectionChagneHandler(index) {
      this.currentSection = index;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

.wrap {
  display: flex;
  flex-direction: column;
  height: 100vh;

  /deep/ .u-navbar__content {
    .u-navbar__content__title,
    .u-icon__icon {
      color: $gt-navbar-title-color !important;
    }
  }

  .head-subsection {
    .subsection {
      height: 80rpx;
    }
  }

  .content {
    flex: 1;
    padding: 12px;
    // display: flex;
    // flex-direction: column;
    overflow: hidden;
  }
}
</style>
