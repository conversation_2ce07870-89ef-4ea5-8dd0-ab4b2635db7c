/**
 * tab相关页面功能
 */

export default {
  data() {
    return {
      userInfo: null,
    };
  },

  onLoad() {
    const USER_INFO_KEY = "userInfo";
    let userInfo = uni.getStorageSync(USER_INFO_KEY);
    this.userInfo = JSON.parse(userInfo);

    uni.hideTabBar();
  },

  methods: {
    navToNewWorkSpace() {
      uni.switchTab({ url: this.$constants.PAGE.NEW_WORK_SPACE });
    },

    navToWorkSpace() {
      uni.switchTab({ url: this.$constants.PAGE.WORK_SPACE });
    },

    navToIndexPage() {
      uni.switchTab({ url: this.$constants.PAGE.INDEX_PAGE });
    },

    navToInventory() {
      uni.switchTab({ url: this.$constants.PAGE.SPECIES_INVENTORY });
    },

    navToAccount() {
      console.log("1111", this.$constants.PAGE.USER_PAGE);

      uni.redirectTo({
        url: this.$constants.PAGE.USER_PAGE,
        fail: (res) => {
          console.log("跳转失败", res);
        },
      });
    },

    navToPointList() {
      uni.switchTab({ url: this.$constants.PAGE.POINT_LIST_URL });
    },
  },
};
