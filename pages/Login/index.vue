<template>
  <view>
    <view class="login-bg">
      <view class="t-b">{{ tip }}</view>
    </view>
    <view class="login-view" style="">
      <view class="t-login">
        <u-form :model="loginInfo" ref="uForm">
          <view class="input-wrapper">
            <u-form-item prop="userName">
              <u-input
                placeholder="请输入用户名"
                :adjustPosition="false"
                :clearable="true"
                @clear="handleClear('user')"
                border="none"
                v-model="loginInfo.userName"
                prefixIcon="account"
                placeholderClass="data-placeholder"
              >
              </u-input>
            </u-form-item>
          </view>

          <view v-if="inputType == 'text'" :key="1">
            <u-form-item prop="passWord">
              <u-input
                placeholder="请输入密码"
                :adjustPosition="false"
                :clearable="true"
                :type="'text'"
                :password="inputType == 'password'"
                @clear="handleClear('psd')"
                border="none"
                v-model="loginInfo.passWord"
                prefixIcon="lock"
                placeholderClass="data-placeholder"
              >
                <template slot="suffix">
                  <view @click="iconChange" class="psd-icon">
                    <u-icon :name="pswSuffixIcon" size="16"></u-icon>
                  </view>
                </template>
              </u-input>
            </u-form-item>
          </view>
          <view v-if="inputType == 'password'" :key="2">
            <u-form-item prop="passWord">
              <u-input
                placeholder="请输入密码"
                :adjustPosition="false"
                :clearable="true"
                :type="'password'"
                :password="inputType == 'password'"
                @clear="handleClear('psd')"
                border="none"
                v-model="loginInfo.passWord"
                prefixIcon="lock"
                placeholderClass="data-placeholder"
              >
                <template slot="suffix">
                  <view @click="iconChange" class="psd-icon">
                    <u-icon :name="pswSuffixIcon" size="16"></u-icon>
                  </view>
                </template>
              </u-input>
            </u-form-item>
          </view>
        </u-form>
        <view class="login-btn">
          <u-button @click="login" type="primary">登 录</u-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Storage from "@/tools/Storage";
import system from "@/common/system";

export default {
  data() {
    return {
      systemInfo: null,
      userInfo: undefined,
      iconState: false,
      loading: false,
      pswSuffixIcon: "eye-off",
      inputType: "password",
      // 系统存储对象，用于缓存不同系统的登录信息
      system_storage: {
        access_token: "",
        refresh_token: "",
        userInfo: null,
      },
      loginInfo: {
        userName: "",
        passWord: "",
      },
      rules: {
        userName: [
          {
            required: true,
            message: "请输入用户名",
            // 可以单个或者同时写两个触发验证方式
            trigger: ["blur", "change"],
          },
        ],
        passWord: [
          {
            required: true,
            message: "请输入密码",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  watch: {
    system_storage: {
      handler(newVal) {
        if (!this.systemInfo || !newVal) return;

        try {
          // 获取现有的系统存储
          const systemStorageStr = uni.getStorageSync("system_storage");
          let storage = {};

          if (systemStorageStr) {
            try {
              storage = JSON.parse(systemStorageStr);
            } catch (parseError) {
              storage = {};
            }
          }

          // 更新当前系统的存储
          storage[this.systemInfo.key] = newVal;

          // 保存到缓存
          uni.setStorageSync("system_storage", JSON.stringify(storage));
        } catch (error) {
          console.error("[Login] 保存system_storage失败:", error);
        }
      },
      deep: true, // 深度监听对象变化
      immediate: false, // 不立即执行，避免初始化时的无效保存
    },
  },
  computed: {
    tip() {
      if (!this.systemInfo) return "广西项目系统";
      return this.systemInfo.name || "广西项目系统";
    },
  },
  onLoad(options) {
    // 获取系统信息
    let systemInfo = system.find((item) => item.key == options.system);
    if (!systemInfo) {
      uni.showToast({
        title: "系统信息错误",
        icon: "error",
        duration: 1500,
        mask: true,
      });
      setTimeout(() => {
        uni.reLaunch({ url: "/pages/Workspace/index" });
      }, 1500);
    }

    this.systemInfo = systemInfo;

    // 获取系统缓存
    this.getSystemStorage(systemInfo);
    // 刷新缓存
    this.refreshToken();
  },
  // H5端
  mounted() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    getSystemStorage(info) {
      try {
        const defaultObj = {
          access_token: "",
          refresh_token: "",
          userInfo: null,
        };

        // 获取系统存储，如果不存在则返回空对象
        const systemStorageStr = uni.getStorageSync("system_storage");
        let systemStorage = {};

        if (systemStorageStr) {
          try {
            systemStorage = JSON.parse(systemStorageStr);
          } catch (parseError) {
            systemStorage = {};
          }
        }

        // 获取当前系统的存储数据
        this.system_storage = systemStorage[info.key] || defaultObj;
      } catch (error) {
        this.system_storage = {
          access_token: "",
          refresh_token: "",
          userInfo: null,
        };
      } finally {
        this.setGlobalStorage();
      }
    },
    setGlobalStorage() {
      const { access_token, refresh_token, userInfo } = this.system_storage;
      if (access_token) {
        uni.setStorageSync("access_token", access_token);
      } else {
        uni.removeStorageSync("access_token");
      }
      if (refresh_token) {
        uni.setStorageSync("refresh_token", refresh_token);
      } else {
        uni.removeStorageSync("refresh_token");
      }
      if (userInfo) {
        uni.setStorageSync("userInfo", JSON.stringify(userInfo));
      } else {
        uni.removeStorageSync("userInfo");
      }
    },
    refreshToken() {
      uni.getStorage({
        key: "refresh_token",
        success: async (res) => {
          console.log(res);
          let data = await this.$apis.login.refreshToken();
          this.system_storage.access_token = data.access_token;
          this.system_storage.refresh_token = data.refresh_token;

          await this.getUserInfo(data.user_id);
          uni.reLaunch({ url: this.systemInfo.home });
        },
      });
    },
    login() {
      this.$refs.uForm
        .validate()
        .then(async (res) => {
          this.loading = true;
          uni.showLoading({ title: "登录中...", mask: true });
          await this.$apis.login
            .login(this.loginInfo.userName, this.loginInfo.passWord)
            .then(async (data) => {
              this.system_storage.access_token = data.access_token;
              this.system_storage.refresh_token = data.refresh_token;
              await uni.setStorage({
                key: "access_token",
                data: data.access_token,
              });
              await uni.setStorage({
                key: "refresh_token",
                data: data.refresh_token,
              });
              await this.getUserInfo(data.user_id);

              uni.reLaunch({ url: this.systemInfo.home });
            })
            .catch((err) => {
              console.log(err);
              uni.showToast({
                title: err,
                icon: "error",
              });
            });
          this.loading = false;
          uni.hideLoading();
        })
        .catch((errors) => {
          // uni.$u.toast('校验失败')
        });
    },
    iconChange() {
      this.pswSuffixIcon = this.pswSuffixIcon === "eye-off" ? "eye" : "eye-off";
      this.inputType = this.inputType === "password" ? "text" : "password";
    },
    async getUserInfo(userId) {
      const { list } = await this.$apis.formData.getFormRecords("tb_user_info_map", {
        filter: ["=", "user_id", userId],
      });
      const userInfo = list[0];
      this.userInfo = userInfo;
      this.system_storage.userInfo = userInfo;
      await uni.setStorage({
        key: "userInfo",
        data: JSON.stringify(userInfo),
      });
      // 存储用户信息作为全局变量
      Storage.saveUser(userInfo);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/static/styles/common.scss";
.login-bg {
  width: 100%;
  height: 480rpx;
  background-color: $gt-primary-color;
  background-position: center;
  background-size: 100% 100%;
  .t-b {
    text-align: center;
    font-size: 46rpx;
    color: #ffffff;
    padding: 190rpx 0 0 0;
    line-height: 70rpx;
  }
}
.login-view {
  width: 100%;
  position: relative;
  margin-top: -120rpx;
  background-color: #ffffff;
  border-radius: 8% 8% 0% 0;
  .t-login {
    width: 600rpx;
    margin: 0 auto;
    font-size: 28rpx;
    padding-top: 80rpx;
  }
  .t-login input {
    height: 90rpx;
    line-height: 90rpx;
    margin-bottom: 50rpx;
    border-bottom: 1px solid #e9e9e9;
    font-size: 28rpx;
  }
  .login-btn {
    margin-top: 30rpx;
  }
}
</style>
