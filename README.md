# 广西一体化平台

## 子系统开发

### 项目文件

1. 项目文件在`src`目录下新建`pages_[项目key]`目录，将原项目文件迁入其中,相应的`pages`、`components`等
2. 图片资源文件需要迁入`static/images/[项目key]`目录下

### 配置文件

在`common/system.js`中配置相关信息，例如

```json
{
  "key": "plowland", // 项目key
  "name": "农用地安全利用", // 系统名称
  "url": "api/plowland-data-apply-server/", // 系统API地址
  "icon": "/static/images/plowland_logo.png", // 系统logo图片
  "env": "PLOWLAND_API_URL", // 环境变量名称
  "home": "/pages_plowland/pages/index/index" // 首页地址
}
```

> 项目 `key` 要和分包名称保持一致

- 在`pages.json`中配置分包页面信息
- 在`env.dev`、`env.h5`、`env.prod`、`env.test`、`env.js`中配置环境变量并重启项目

### 定制 apis

如若项目使用的表单填写编辑等都是常规功能，则使用公共的表单请求，如果有特殊情况则在分包下创建`/apis/xxxx.js`接口文件处理

**_使用示例_**

```js
import request from "@pages_[项目key]/apis/xxxx.js";

await request.getPoint();
```

### 定制组件

如果子系统需要定制相关的 Field 组件，则需要以下操作

1. 在`pages_[项目key]/components/Field`目录下创建组件，组件名称和参数等保持一致即可。
2. 在`common/system.js`中配置`customFields`属性，值为组件名称数组
